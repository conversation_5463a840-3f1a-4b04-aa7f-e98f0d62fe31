## rsyncが使えればどんなイメージでもOK
container:
  image: docker-registry.devops.dmmga.me/devops/games-ci/php-5.6-node14.x-mongodb:latest

## 通知チャンネルが決まったら指定する
## Botの招待方法 : https://confl.arms.dmm.com/pages/viewpage.action?pageId=*********
## notify:
##   channel: team-devops-notify-test

server: &server
  - dmm@*************:/home/<USER>/developer-games

stages:
  - name: setup .env
    type: script
    script: |
      cp .env.production .env
 
  - name: composer install
    type: script
    script: |
      composer install --no-dev
      chmod -R a+w storage/*
      chmod -R a+w bootstrap/cache
 
  - name: ssh connection test
    type: script-loop
    server: *server
    concurrency: 20
    script: |
      ssh "$SERVER_ACCOUNT@$SERVER_HOST" "hostname"
 
  - name: deploy
    type: script-loop
    server: *server
    concurrency: 20
    script: |
      ssh "$SERVER_ACCOUNT@$SERVER_HOST" "mkdir -p $SERVER_PATH"
      rsync -a --delete --stats --itemize-changes $RSYNC_OPTIONS --exclude-from=.ghes/.rsyncignore . "$SERVER_ACCOUNT@$SERVER_HOST:$SERVER_PATH" 
 
  - name: artisan view:clear
    type: script-loop
    server: *server
    concurrency: 20
    script: |
      ssh "$SERVER_ACCOUNT@$SERVER_HOST" "cd $SERVER_PATH && php artisan view:clear"