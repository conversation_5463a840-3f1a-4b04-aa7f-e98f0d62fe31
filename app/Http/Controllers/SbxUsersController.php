<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\SbxUsers\SbxUsersCreateRequest;
use App\Http\Requests\SbxUsers\SbxUsersEditRequest;
use App\Services\SbxUsersService;
use Log;

/**
 * Sandbox Users
 */
class SbxUsersController extends Controller
{
    protected $sbxUsersService;

    public function __construct(SbxUsersService $sbxUsersService)
    {
        parent::__construct();
        $this->sbxUsersService = $sbxUsersService;

        // configの固定パラメタ
        view()->share([
            'formData' => $this->sbxUsersService->getFormData(),
        ]);

    }

    /**
     * 一覧トップ
     */
    public function index(Request $request)
    {
        // sandbox_memberはじめとするテーブルデータ取得
        $condition = $this->sbxUsersService->formatSearchCondition($request->all());
        $paginator = $this->sbxUsersService->getSbxUserList($condition);
        $pagerView = $this->sbxUsersService->getPagerView($paginator, config('forms.SbxUsers.pagerLinkNum'));

        return view('SbxUsers.index', compact('paginator', 'pagerView'));

    }

    /**
     * 新規
     */
    public function create(Request $request)
    {
        return view('SbxUsers.create', compact('request'));
    }

    /**
     * 新規確認
     */
    public function createConfirm(SbxUsersCreateRequest $request)
    {
        return view('SbxUsers.createconfirm', compact('request'));
    }

    /**
     * 新規登録
     */
    public function store(SbxUsersCreateRequest $request)
    {
        $this->sbxUsersService->storeSbxUser($request);
        return view('SbxUsers.store');
    }

    /**
     * 編集
     */
    public function edit(Request $request)
    {
        if ($request->method() == 'GET' && $request->session()->has('errors')) {
            // バリデーションエラー時は直前まで入力していたデータを取得
            $content = $request->old();
        } elseif ($request->method() == 'POST') {
            // 確認画面から遷移時は入力していたデータを取得
            $content = $request->all();
        } else {
            // 初期表示時はDBからデータを取得
            $content = $this->sbxUsersService->getUserByIdWithMember($request->id);
            $content['nowPoint'] = $content['point'];
            $content['point'] = 0;
        }
        return view('SbxUsers.edit', compact('content'));
    }

    /**
     * 編集確認
     */
    public function editConfirm(SbxUsersEditRequest $request)
    {
        return view('SbxUsers.editconfirm', compact('request'));
    }

    /**
     * 編集更新
     */
    public function update(SbxUsersEditRequest $request)
    {
        $this->sbxUsersService->updateSbxUser($request);
        return view('SbxUsers.update');
    }

    /**
     * 削除
     */
    public function destroy(Request $request)
    {
        $result = $this->sbxUsersService->deleteSbxUser($request->get('id'));
        if (empty($result)) {
            Log::error('Not Found user : id=' . $request->get('id'));
            abort(404);
        }

        // トップへリダイレクト
        return redirect()->route('SbxUsers.index', ['search' => 'on']);
    }
}
