<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;

/**
 * Test controller for reproducing Chrome download issues
 */
class TestDownloadController extends Controller
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Simple test download to reproduce Chrome issues
     */
    public function testDownload(Request $request)
    {
        $mode = $request->get('mode', 'default');
        $fileName = "test-download-{$mode}.csv";

        // Log current PHP settings
        \Log::info("=== Test Download Debug Info ===", [
            'mode' => $mode,
            'output_buffering' => ini_get('output_buffering'),
            'implicit_flush' => ini_get('implicit_flush'),
            'ob_get_level' => ob_get_level(),
            'memory_limit' => ini_get('memory_limit'),
        ]);

        $callback = function () use ($mode) {
            // Clear any existing output buffers
            while (ob_get_level() > 0) {
                ob_end_clean();
            }

            switch ($mode) {
                case 'small':
                    // Small file - should work fine
                    echo "header1,header2,header3\n";
                    echo "value1,value2,value3\n";
                    break;
                    
                case 'large':
                    // Large file with streaming
                    echo "id,data,timestamp\n";
                    for ($i = 1; $i <= 10000; $i++) {
                        echo "$i,\"Large data chunk with lots of text to make it bigger\",\"" . date('Y-m-d H:i:s') . "\"\n";
                        if ($i % 100 == 0) {
                            flush();
                            usleep(10000); // 10ms delay
                        }
                    }
                    break;
                    
                case 'chunked':
                    // Chunked streaming with explicit flushes
                    echo "chunk,size,data\n";
                    for ($i = 1; $i <= 50; $i++) {
                        $data = str_repeat("x", 1000); // 1KB of data
                        echo "$i,1000,\"$data\"\n";
                        ob_flush();
                        flush();
                        sleep(1); // 1 second delay between chunks
                    }
                    break;
                    
                case 'problematic':
                    // This mode tries to reproduce the Chrome issue
                    // by creating buffering conflicts
                    
                    // First, output some data
                    echo "header1,header2,header3\n";
                    
                    // Force a flush
                    ob_flush();
                    flush();
                    
                    // Wait a bit
                    sleep(2);
                    
                    // Output more data in chunks that might cause buffering issues
                    for ($i = 1; $i <= 100; $i++) {
                        // Create chunks that are exactly at buffer boundaries
                        $chunk_size = 4096; // Match common buffer sizes
                        $data = str_repeat("a", $chunk_size - 20); // Leave room for CSV formatting
                        echo "$i,\"$data\"\n";
                        
                        // Only flush every few iterations to create buffering conflicts
                        if ($i % 5 == 0) {
                            ob_flush();
                            flush();
                            usleep(100000); // 100ms delay
                        }
                    }
                    break;
                    
                default:
                    // Default test
                    echo "test_type,description,status\n";
                    echo "default,\"Basic CSV download test\",\"working\"\n";
                    echo "small,\"Small file test\",\"should work\"\n";
                    echo "large,\"Large file with streaming\",\"may have issues\"\n";
                    echo "chunked,\"Chunked streaming test\",\"may have issues\"\n";
                    echo "problematic,\"Buffering conflict test\",\"likely to cause Chrome issues\"\n";
                    break;
            }
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename=' . $fileName,
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0'
        ]);
    }
}
