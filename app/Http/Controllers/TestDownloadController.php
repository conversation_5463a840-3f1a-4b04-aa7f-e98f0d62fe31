<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;

/**
 * Test controller for reproducing Chrome download issues
 */
class TestDownloadController extends Controller
{
    public function __construct()
    {
        // Don't call parent::__construct() to bypass auth middleware for testing
        // parent::__construct();
    }

    /**
     * Simple test download to reproduce Chrome issues
     */
    public function testDownload(Request $request)
    {
        $mode = $request->get('mode', 'default');
        $fileName = "test-download-{$mode}.csv";

        // Log current PHP settings
        \Log::info("=== Test Download Debug Info ===", [
            'mode' => $mode,
            'output_buffering' => ini_get('output_buffering'),
            'implicit_flush' => ini_get('implicit_flush'),
            'ob_get_level' => ob_get_level(),
            'memory_limit' => ini_get('memory_limit'),
        ]);

        $callback = function () use ($mode) {
            // Clear any existing output buffers
            while (ob_get_level() > 0) {
                ob_end_clean();
            }

            switch ($mode) {
                case 'small':
                    // Small file - should work fine
                    echo "header1,header2,header3\n";
                    echo "value1,value2,value3\n";
                    break;
                    
                case 'large':
                    // Large file with streaming
                    echo "id,data,timestamp\n";
                    for ($i = 1; $i <= 10000; $i++) {
                        echo "$i,\"Large data chunk with lots of text to make it bigger\",\"" . date('Y-m-d H:i:s') . "\"\n";
                        if ($i % 100 == 0) {
                            flush();
                            usleep(10000); // 10ms delay
                        }
                    }
                    break;
                    
                case 'chunked':
                    // Chunked streaming with explicit flushes
                    echo "chunk,size,data\n";
                    for ($i = 1; $i <= 50; $i++) {
                        $data = str_repeat("x", 1000); // 1KB of data
                        echo "$i,1000,\"$data\"\n";
                        ob_flush();
                        flush();
                        sleep(1); // 1 second delay between chunks
                    }
                    break;
                    
                case 'problematic':
                    // This mode tries to reproduce the Chrome issue
                    // by creating severe buffering conflicts

                    // First, output some data
                    echo "header1,header2,header3\n";

                    // Force a flush
                    ob_flush();
                    flush();

                    // Wait a bit to create timing issues
                    sleep(3);

                    // Output data in a way that creates buffering conflicts
                    for ($i = 1; $i <= 200; $i++) {
                        // Create chunks that are problematic for Chrome
                        $chunk_size = 8192; // Larger chunks
                        $data = str_repeat("x", $chunk_size - 50); // Leave room for CSV formatting
                        echo "$i,\"$data\",\"" . date('Y-m-d H:i:s') . "\"\n";

                        // Create irregular flushing patterns
                        if ($i % 3 == 0) {
                            ob_flush();
                            flush();
                            usleep(200000); // 200ms delay
                        } else if ($i % 7 == 0) {
                            // Don't flush, let it buffer
                            usleep(50000); // 50ms delay
                        }

                        // Add some random delays to create timing issues
                        if ($i % 13 == 0) {
                            sleep(1); // 1 second delay
                        }
                    }
                    break;

                case 'extreme':
                    // Extreme mode - most likely to cause Chrome issues
                    echo "id,large_data,timestamp,extra_field\n";

                    // Start with a large initial chunk
                    $initial_data = str_repeat("INITIAL_", 2000);
                    echo "0,\"$initial_data\",\"" . date('Y-m-d H:i:s') . "\",\"start\"\n";
                    ob_flush();
                    flush();

                    // Long delay to let Chrome start processing
                    sleep(5);

                    // Now send data in very problematic patterns
                    for ($i = 1; $i <= 500; $i++) {
                        // Vary chunk sizes dramatically
                        $size = ($i % 2 == 0) ? 16384 : 1024; // Alternate between 16KB and 1KB
                        $data = str_repeat(chr(65 + ($i % 26)), $size - 100); // Different characters

                        echo "$i,\"$data\",\"" . date('Y-m-d H:i:s.u') . "\",\"row_$i\"\n";

                        // Very irregular flushing
                        if ($i % 2 == 0) {
                            ob_flush();
                            flush();
                        }

                        // Random delays
                        if ($i % 10 == 0) {
                            usleep(500000); // 500ms
                        } else if ($i % 5 == 0) {
                            usleep(100000); // 100ms
                        }

                        // Occasional long pauses
                        if ($i % 50 == 0) {
                            sleep(2);
                        }
                    }
                    break;

                case '6mb':
                    // Generate exactly around 6.3MB to reproduce the production issue
                    echo "id,data_chunk,timestamp,description\n";

                    $target_size = 6.3 * 1024 * 1024; // 6.3MB in bytes
                    $current_size = 0;
                    $row_count = 0;

                    // Calculate approximate row size
                    $base_row_size = 1000; // Approximate size per row

                    while ($current_size < $target_size) {
                        $row_count++;

                        // Create a row with approximately 1KB of data
                        $data_chunk = str_repeat("X", $base_row_size - 100); // Leave room for other fields
                        $row = "$row_count,\"$data_chunk\",\"" . date('Y-m-d H:i:s.u') . "\",\"Row $row_count of large dataset\"\n";

                        echo $row;
                        $current_size += strlen($row);

                        // Critical: Flush at specific intervals that might trigger the 6.3MB issue
                        if ($current_size > 6.2 * 1024 * 1024 && $current_size < 6.4 * 1024 * 1024) {
                            // We're in the critical 6.3MB range - create problematic flushing
                            if ($row_count % 2 == 0) {
                                ob_flush();
                                flush();
                                usleep(100000); // 100ms delay in critical range
                            }
                        } else if ($row_count % 10 == 0) {
                            // Normal flushing outside critical range
                            ob_flush();
                            flush();
                        }

                        // Add small delays to simulate real data processing
                        if ($row_count % 100 == 0) {
                            usleep(50000); // 50ms every 100 rows
                        }
                    }

                    // Final message
                    echo "FINAL,\"Data generation complete\",\"" . date('Y-m-d H:i:s') . "\",\"Total size: " . round($current_size / 1024 / 1024, 2) . "MB\"\n";
                    break;

                case '6mb-with-length':
                    // This mode pre-generates content to send Content-Length header
                    // This should reproduce the production behavior more accurately

                    $target_size = 6.3 * 1024 * 1024; // 6.3MB in bytes
                    $content = "id,data_chunk,timestamp,description\n";
                    $current_size = strlen($content);
                    $row_count = 0;
                    $base_row_size = 1000;

                    // Pre-generate all content to calculate exact size
                    while ($current_size < $target_size) {
                        $row_count++;
                        $data_chunk = str_repeat("X", $base_row_size - 100);
                        $row = "$row_count,\"$data_chunk\",\"" . date('Y-m-d H:i:s.u') . "\",\"Row $row_count of large dataset\"\n";
                        $content .= $row;
                        $current_size = strlen($content);
                    }

                    // Add final message
                    $content .= "FINAL,\"Data generation complete\",\"" . date('Y-m-d H:i:s') . "\",\"Total size: " . round(strlen($content) / 1024 / 1024, 2) . "MB\"\n";

                    // Set the Content-Length header with exact size
                    header('Content-Length: ' . strlen($content));

                    // Now output the content in chunks with problematic patterns
                    $chunk_size = 8192; // 8KB chunks
                    $offset = 0;
                    $total_length = strlen($content);

                    while ($offset < $total_length) {
                        $chunk = substr($content, $offset, $chunk_size);
                        echo $chunk;
                        $offset += strlen($chunk);

                        // Create problematic flushing around 6.3MB mark
                        if ($offset > 6.2 * 1024 * 1024 && $offset < 6.4 * 1024 * 1024) {
                            // In critical range - irregular flushing
                            if (($offset / $chunk_size) % 3 == 0) {
                                ob_flush();
                                flush();
                                usleep(200000); // 200ms delay
                            }
                        } else {
                            // Normal flushing
                            if (($offset / $chunk_size) % 10 == 0) {
                                ob_flush();
                                flush();
                                usleep(50000); // 50ms delay
                            }
                        }
                    }
                    break;

                default:
                    // Default test
                    echo "test_type,description,status\n";
                    echo "default,\"Basic CSV download test\",\"working\"\n";
                    echo "small,\"Small file test\",\"should work\"\n";
                    echo "large,\"Large file with streaming\",\"may have issues\"\n";
                    echo "chunked,\"Chunked streaming test\",\"may have issues\"\n";
                    echo "problematic,\"Buffering conflict test\",\"likely to cause Chrome issues\"\n";
                    echo "extreme,\"Extreme buffering conflicts\",\"most likely to break Chrome\"\n";
                    echo "6mb,\"6.3MB test - reproduces production issue\",\"should break at 6.3MB in Chrome\"\n";
                    echo "6mb-with-length,\"6.3MB with Content-Length header\",\"most accurate production reproduction\"\n";
                    break;
            }
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename=' . $fileName,
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0'
        ]);
    }
}
