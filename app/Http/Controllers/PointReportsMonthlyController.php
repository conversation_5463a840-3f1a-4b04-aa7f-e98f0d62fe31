<?php
namespace App\Http\Controllers;

use App\Http\Requests\PointReportsMonthlyRequest;
use App\Services\PointReportsMonthlyService;

/**
 * 課金履歴ダウンロード(月別)
 */
class PointReportsMonthlyController extends Controller
{
    protected $PointReportsMonthlyService;
    protected $config;
    protected $breadcrumbs;

    public function __construct(PointReportsMonthlyService $PointReportsMonthlyService)
    {
        parent::__construct();
        $this->PointReportsMonthlyService = $PointReportsMonthlyService;
        $this->config = config('forms.PointReportsMonthly');

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'PointReportsMonthly.index'];

        view()->share(['formData' => $this->config]);
    }

    /**
     * index
     *
     */
    public function index()
    {
        $data = array(
            'breadcrumbs'  => $this->breadcrumbs,
            'appTitleType' => $this->PointReportsMonthlyService->getApplicationList(),
            'periodType'   => $this->config['periodType'],
        );

        return view('PointReportsMonthly.index', $data);
    }

    /**
     * CSVダウンロード
     *
     * @param PointReportsMonthlyRequest $request
     */
    public function csvDownload(PointReportsMonthlyRequest $request)
    {
        $condition = $request->all();
        $fileName = $this->PointReportsMonthlyService->getCsvFileName($condition);
 
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename=' . $fileName);

        # add headers for each column in the CSV download
        $fieldName = $this->PointReportsMonthlyService->getCsvHeader($condition);
        echo mb_convert_encoding(implode(',', $fieldName), 'sjis-win', 'UTF-8')."\n";

        # get CSV data and output it
        $csvData = $this->PointReportsMonthlyService->getCsvList($condition);

        # output each row
        foreach ($csvData as $row) {
            echo mb_convert_encoding(implode(',', $row), 'sjis-win', 'UTF-8')."\n";
        }

        return;
    }
}