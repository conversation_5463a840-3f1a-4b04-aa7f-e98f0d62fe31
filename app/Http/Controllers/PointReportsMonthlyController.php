<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\PointReportsMonthlyRequest;
use App\Services\PointReportsMonthlyService;
use Symfony\Component\HttpFoundation\StreamedResponse;

/**
 * 課金履歴ダウンロード(月別)
 */
class PointReportsMonthlyController extends Controller
{
    protected $PointReportsMonthlyService;
    protected $config;
    protected $breadcrumbs;

    public function __construct(PointReportsMonthlyService $PointReportsMonthlyService)
    {
        parent::__construct();
        $this->PointReportsMonthlyService = $PointReportsMonthlyService;
        $this->config = config('forms.PointReportsMonthly');

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'PointReportsMonthly.index'];

        view()->share(['formData' => $this->config]);
    }

    /**
     * index
     *
     */
    public function index()
    {
        $data = array(
            'breadcrumbs'  => $this->breadcrumbs,
            'appTitleType' => $this->PointReportsMonthlyService->getApplicationList(),
            'periodType'   => $this->config['periodType'],
        );

        return view('PointReportsMonthly.index', $data);
    }

    /**
     * CSVダウンロード
     *
     * @param PointReportsMonthlyRequest $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function csvDownload(PointReportsMonthlyRequest $request)
    {
        $condition = $request->all();
        $fileName = $this->PointReportsMonthlyService->getCsvFileName($condition);

        $callback = function () use ($condition) {
            // Clear any existing output buffers to prevent unexpected output.
            while (ob_get_level() > 0) {
                ob_end_clean();
            }

            # add headers for each column in the CSV download
            $fieldName = $this->PointReportsMonthlyService->getCsvHeader($condition);
            echo mb_convert_encoding(implode(',', $fieldName), 'sjis-win', 'UTF-8')."\n";

            $this->PointReportsMonthlyService->getCsvList($condition);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv; charset=sjis-win',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }
}