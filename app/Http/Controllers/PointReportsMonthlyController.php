<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\PointReportsMonthlyRequest;
use App\Services\PointReportsMonthlyService;

/**
 * 課金履歴ダウンロード(月別)
 */
class PointReportsMonthlyController extends Controller
{
    protected $PointReportsMonthlyService;
    protected $config;
    protected $breadcrumbs;

    public function __construct(PointReportsMonthlyService $PointReportsMonthlyService)
    {
        parent::__construct();
        $this->PointReportsMonthlyService = $PointReportsMonthlyService;
        $this->config = config('forms.PointReportsMonthly');

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'PointReportsMonthly.index'];

        view()->share(['formData' => $this->config]);
    }

    /**
     * index
     *
     */
    public function index()
    {
        $data = array(
            'breadcrumbs'  => $this->breadcrumbs,
            'appTitleType' => $this->PointReportsMonthlyService->getApplicationList(),
            'periodType'   => $this->config['periodType'],
        );

        return view('PointReportsMonthly.index', $data);
    }

    /**
     * CSVダウンロード
     *
     * @param PointReportsMonthlyRequest $request
     *
     */
    public function csvDownload(PointReportsMonthlyRequest $request)
    {
        $condition = $request->all();
        $fileName = $this->PointReportsMonthlyService->getCsvFileName($condition);

        // Save and disable output_buffering
        $originalOutputBuffering = ini_get('output_buffering');
        ini_set('output_buffering', 'off');

        $callback = function () use ($condition, $originalOutputBuffering) {
            while (ob_get_level() > 0) { ob_end_clean(); }

            // Output PHP settings
            $phpSettings = [
                'output_buffering'   => ini_get('output_buffering'),
                'ob_get_level'       => ob_get_level(),
                'memory_limit'       => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
                'max_input_time'     => ini_get('max_input_time'),
                'post_max_size'      => ini_get('post_max_size'),
                'upload_max_filesize'=> ini_get('upload_max_filesize'),
            ];
            foreach ($phpSettings as $key => $value) {
                echo "\"$key\",\"$value\"\n";
                flush();
            }
            echo "\n"; // Blank line before the actual CSV header

            $fieldName = $this->PointReportsMonthlyService->getCsvHeader($condition);
            echo mb_convert_encoding(implode(',', $fieldName), 'sjis-win', 'UTF-8') . "\n";
            flush();

            $rows = $this->PointReportsMonthlyService->getCsvList($condition);
            foreach ($rows as $row) {
                echo mb_convert_encoding(implode(',', $row), 'sjis-win', 'UTF-8') . "\n";
                flush();
            }

            // (Optional) Restore original output_buffering
            ini_set('output_buffering', $originalOutputBuffering);
        };

        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename=' . $fileName,
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0'
        ]);
    }
}
