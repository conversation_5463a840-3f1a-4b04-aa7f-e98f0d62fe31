<?php
namespace App\Http\Controllers;

use App\Http\Requests\PointReportsMonthlyRequest;
use App\Services\PointReportsMonthlyService;

/**
 * 課金履歴ダウンロード(月別)
 */
class PointReportsMonthlyController extends Controller
{
    protected $PointReportsMonthlyService;
    protected $config;
    protected $breadcrumbs;

    public function __construct(PointReportsMonthlyService $PointReportsMonthlyService)
    {
        parent::__construct();
        $this->PointReportsMonthlyService = $PointReportsMonthlyService;
        $this->config = config('forms.PointReportsMonthly');

        $this->breadcrumbs[] = $this->config['breadcrumbsParent'];
        $this->breadcrumbs[] = [$this->config['screenName'], 'PointReportsMonthly.index'];

        view()->share(['formData' => $this->config]);
    }

    /**
     * index
     *
     */
    public function index()
    {
        $data = array(
            'breadcrumbs'  => $this->breadcrumbs,
            'appTitleType' => $this->PointReportsMonthlyService->getApplicationList(),
            'periodType'   => $this->config['periodType'],
        );

        return view('PointReportsMonthly.index', $data);
    }

    /**
     * CSVダウンロード
     *
     * @param PointReportsMonthlyRequest $request
     */
    public function csvDownload(PointReportsMonthlyRequest $request)
    {
        $condition = $request->all();
        $fileName = $this->PointReportsMonthlyService->getCsvFileName($condition);
 
        // 直接にデータをブラウザに送るではなく、まずローカル変数にデータを保存する。
        ob_start();
 
        try {
            $fieldName = $this->PointReportsMonthlyService->getCsvHeader($condition);
            echo mb_convert_encoding(implode(',', $fieldName), 'sjis-win', 'UTF-8')."\n";
 
            $this->PointReportsMonthlyService->getCsvList($condition);
 
            $csvOutput = ob_get_contents();
        } finally {
            ob_end_clean();
        }
 
        $headers = [
            'Content-Type' => 'text/csv; charset=sjis-win',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
            'Content-Length' => strlen($csvOutput), // Set the exact file size
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ];
 
        // 変数に保存されたデータを一気にブラウザに送る
        return response($csvOutput, 200, $headers);
    }
}