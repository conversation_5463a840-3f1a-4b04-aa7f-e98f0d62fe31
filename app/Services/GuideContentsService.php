<?php

namespace App\Services;

use App\Models\FreegameDeveloper\DeveloperGuideApplication;
use App\Models\FreegameGuide\GuideApplication;
use App\Models\FreegameGuide\GuideContentsMaster;
use App\Models\FreegameGuide\GuideContentsTemplate;
use App\Models\FreegameGuide\GuideContentsCluster;
use App\Models\FreegameGuide\GuideContentsValue;
use App\Models\FreegameGuide\GuideContentsImage;
use \Exception;
use \Log;

class GuideContentsService extends CustomService
{
    // 画像ファイル保存ディレクトリ
    const IMAGES_DIRECTORY = 'contents';

    protected $guideApp;
    protected $devGuideApp;
    protected $guideContentsMaster;
    protected $guideContentsTemplate;
    protected $guideContentsCluster;
    protected $guideContentsValue;
    protected $guideContentsImage;

    protected $dataTypeList = [
        'alnum'     => '半角英数',
        'text'      => 'テキスト',
        'flag'      => 'フラグ',
        'digit'     => '数字',
        'image_url' => '画像',
        'url'       => 'URL',
        'html'      => 'HTML',
        'date'      => '日付',
    ];

    public function __construct(
        GuideApplication $guideApp,
        DeveloperGuideApplication $devGuideApp,
        GuideContentsMaster $guideContentsMaster,
        GuideContentsTemplate $guideContentsTemplate,
        GuideContentsCluster $guideContentsCluster,
        GuideContentsValue $guideContentsValue,
        GuideContentsImage $guideContentsImage
    ) {
        $this->guideApp = $guideApp;
        $this->devGuideApp = $devGuideApp;
        $this->guideContentsMaster = $guideContentsMaster;
        $this->guideContentsTemplate = $guideContentsTemplate;
        $this->guideContentsCluster = $guideContentsCluster;
        $this->guideContentsValue = $guideContentsValue;
        $this->guideContentsImage = $guideContentsImage;
    }

    public function getDataTypeList()
    {
        return $this->dataTypeList;
    }

    /**
     * コンテンツマスター一覧取得
     * @param  integer $guideAppId
     * @return array
     */
    public function getMasterList($guideAppId)
    {
        $guideMasterList = $this->guideContentsMaster->getListByGuideAppId($guideAppId);

        return $guideMasterList->toArray();
    }

    /**
     * コンテンツマスター一覧検索
     * @param  array $params 検索条件
     * @return LengthAwarePaginator
     */
    public function searchMasterList($params)
    {
        $guideMasterList = $this->guideContentsMaster->search($params);

        // ページャリンクで検索条件と表示件数を保持するためパラメータ追加
        $guideMasterList->appends($params);

        return $guideMasterList;
    }

    /**
     * グループ一覧取得
     * @param  integer $guideAppId
     * @return array
     */
    public function getGroupList($guideAppId)
    {
        $groupList = $this->guideContentsMaster->getGroupList($guideAppId)->toArray();
        $groupList = array_pluck($groupList, 'group_name');
        $groupList = array_combine($groupList, $groupList);
        return $groupList;
    }

    /**
     * マスターキーのユニーク確認
     * @param integer $guideAppId
     * @param string $masterKey
     * @return boolean
     */
    public function isUniqueMasterKey($guideAppId, $masterKey)
    {
        return !$this->guideContentsMaster->existsMasterKey($guideAppId, $masterKey);
    }

    /**
     * マスター名のユニーク確認
     * @param integer $guideAppId
     * @param string $masterName
     * @param string $group
     * @return boolean
     */
    public function isUniqueName($guideAppId, $masterName, $group, $ignoreId = null)
    {
        return !$this->guideContentsMaster->existsName($guideAppId, $masterName, $group, $ignoreId);
    }

    /**
     * データキーのユニーク確認
     * @param integer $masterId
     * @param string $clusterKey
     * @return boolean
     */
    public function isUniqueClusterKey($masterId, $clusterKey)
    {
        return !$this->guideContentsCluster->existsClusterKey($masterId, $clusterKey);
    }

    /**
     * マスター新規登録
     * @param Request $request
     * @return boolean
     */
    public function createMaster($request)
    {
        GuideContentsMaster::beginTransaction();
        GuideContentsTemplate::beginTransaction();
        try {
            $group = (empty($request['group_select'])) ? $request['group_name'] : $request['group_select'];
            $data = [
                'guide_application_id' => $request['guideAppId'],
                'name' => $request['name'],
                'master_key' => $request['master_key'],
                'group_name' => $group,
                'description' => $request['description'],
                'memo' => $request['memo'],
            ];
            $master_id = $this->guideContentsMaster->insertGetId($data);

            $datum = [];
            foreach ($request['templates'] as $value) {
                $datum[] = [
                    'master_id' => $master_id,
                    'template_key' => $value['template_key'],
                    'value_type' => $value['value_type'],
                    'memo' => $value['memo'],
                ];
            }
            $this->guideContentsTemplate->insert($datum);
        } catch (Exception $e) {
            GuideContentsMaster::rollback();
            GuideContentsTemplate::rollback();
            Log::error(var_export($e->getMessage(), true));
            throw $e;
        }
        GuideContentsMaster::commit();
        GuideContentsTemplate::commit();

        return true;
    }

    /**
     * マスター更新
     * @param Request $request
     * @return boolean
     */
    public function updateMaster($request)
    {
        GuideContentsMaster::beginTransaction();
        GuideContentsTemplate::beginTransaction();
        GuideContentsValue::beginTransaction();
        try {
            $oldMasterData = $this->getMasterData($request['id']);
            $oldTemplates = collect($oldMasterData['templates'])->keyBy('id')->toArray();
            $group = (empty($request['group_select'])) ? $request['group_name'] : $request['group_select'];

            // マスター更新
            $data = [
                'name' => $request['name'],
                'group_name' => $group,
                'description' => $request['description'],
                'memo' => $request['memo'],
            ];
            $this->guideContentsMaster->edit($data, $request['id']);


            // 管理項目削除
            $newTemplates = $request['templates'];
            $newTemplateIds = array_pluck($newTemplates, 'id');
            $deleteTemplateIds = [];
            foreach ($oldTemplates as $oldTemplate) {
                if (!in_array($oldTemplate['id'], $newTemplateIds)) {
                    $deleteTemplateIds[] = $oldTemplate['id'];
                }
            }
            $this->guideContentsTemplate->deleteByIds($deleteTemplateIds);

            // 管理項目追加・編集
            $insertDatum = [];
            $editTemplateIds = [];
            foreach ($newTemplates as $template) {
                if (empty($template['id'])) {
                    $insertDatum[] = [
                        'master_id' => $request['id'],
                        'template_key' => $template['template_key'],
                        'value_type' => $template['value_type'],
                        'memo' => $template['memo'],
                    ];
                } else {
                    $editData = [
                        'template_key' => $template['template_key'],
                        'value_type' => $template['value_type'],
                        'memo' => $template['memo'],
                    ];
                    $this->guideContentsTemplate->edit($editData, $template['id']);

                    if (($template['template_key'] != $oldTemplates[$template['id']]['template_key']) || $template['value_type'] != $oldTemplates[$template['id']]['value_type']) {
                        $editTemplateIds[] = $template['id'];
                    }
                }
            }
            $this->guideContentsTemplate->insert($insertDatum);

            $this->guideContentsValue->deleteByMultiTemplateIds(array_merge($editTemplateIds, $deleteTemplateIds));
        } catch (Exception $e) {
            GuideContentsMaster::rollback();
            GuideContentsTemplate::rollback();
            GuideContentsValue::rollback();
            Log::error(var_export($e->getMessage(), true));
            throw $e;
        }
        GuideContentsMaster::commit();
        GuideContentsTemplate::commit();
        GuideContentsValue::commit();

        return true;
    }

    /**
     * マスター削除
     * @param integer $masterId
     * @return boolean
     */
    public function deleteMaster($masterId)
    {
        GuideContentsMaster::beginTransaction();
        GuideContentsTemplate::beginTransaction();
        GuideContentsCluster::beginTransaction();
        GuideContentsValue::beginTransaction();
        GuideContentsImage::beginTransaction();
        try {
            $masterData = $this->getMasterData($masterId);
            $templateIds = array_keys($masterData['templates']);

            $this->deleteImageByMasterId($masterId);
            $this->guideContentsValue->deleteByMultiTemplateIds($templateIds);
            $this->guideContentsTemplate->deleteByIds($templateIds);
            $this->guideContentsCluster->deleteByMasterId($masterId);
            $this->guideContentsMaster->destroy($masterId);

        } catch (Exception $e) {
            GuideContentsMaster::rollback();
            GuideContentsTemplate::rollback();
            GuideContentsCluster::rollback();
            GuideContentsValue::rollback();
            GuideContentsImage::rollback();
            Log::error(var_export($e->getMessage(), true));
            throw $e;
        }
        GuideContentsMaster::commit();
        GuideContentsTemplate::commit();
        GuideContentsCluster::commit();
        GuideContentsValue::commit();
        GuideContentsImage::commit();

        return true;
    }

    /**
     * マスター情報取得
     * @param integer $masterId
     * @return array
     */
    public function getMasterData($masterId)
    {
        $collection = collect($this->guideContentsMaster->getMasterData($masterId));
        $templates = collect($collection['templates'])->keyBy('id');
        $collection->put('templates', $templates);
        return $collection->toArray();
    }

    /**
     * データ取得
     * @param integer $clusterId
     * @return array
     */
    public function getContentsData($clusterId)
    {
        $collection = collect($this->guideContentsCluster->getContentsCluster($clusterId));
        $values = collect($collection['values'])->keyBy('template_id');
        $collection->put('values', $values);
        return $collection->toArray();
    }

    /**
     * データ一覧取得
     * @param integer $masterId
     * @param integer $limit 最大取得件数
     * @return LengthAwarePaginator
     */
    public function getContentsList($masterId, $limit)
    {
        $contentsList = $this->guideContentsCluster->getByMasterId($masterId, $limit);

        // ページャリンクで表示件数を保持するためパラメータ追加
        $appends['perPage'] = $limit;
        $contentsList->appends($appends);

        return $contentsList;
    }

    /**
     * データ表示順テーブル取得
     * @param integer $masterId
     * @param integer $editId 編集中データID(新規登録時はnull)
     * @return array
     */
    public function getContentsOrderList($masterId, $editId = null)
    {
        $currentOrderList = $this->guideContentsCluster->getOrderList($masterId)->keyBy('order_no');

        $orderList = [];
        // 新規登録の場合
        if (is_null($editId)) {
            $orderList[1] = '先頭に追加';

            $currentOrderList = $currentOrderList->toArray();
            if (empty($currentOrderList)) {
                return $orderList;
            }

            foreach ($currentOrderList as $orderNo => $orderCluster) {
                if ($orderNo != 1) {
                    $orderList[$orderNo] = $orderCluster['name'] . 'の前に追加';
                }
            }
            $orderList[-1] = '末尾に追加';
        } else { // 編集の場合
            $lastNo = $currentOrderList->last()->toArray()['order_no'];
            $editCluster = $currentOrderList->where('id', $editId)->first()->toArray();
            $currentOrderList = $currentOrderList->toArray();

            foreach ($currentOrderList as $orderNo => $orderCluster) {
                if ($orderCluster['id'] == $editCluster['id']) {
                    $orderList[0] = '移動しない';
                    continue;
                }
    
                if ($orderNo == ($editCluster['order_no'] + 1)) {
                    continue;
                }

                if ($orderNo == 1) {
                    $orderList[1] = '先頭に移動';
                } else {
                    $orderList[$orderNo] = $orderCluster['name'] . 'の前に移動';
                }
            }

            if ($editCluster['order_no'] != $lastNo) {
                $orderList[-1] = '末尾に移動';
            }
        }
        return $orderList;
    }

    /**
     * 管理項目一覧取得
     * @param integer $masterId
     * @return array
     */
    public function getTemplateList($masterId)
    {
        $collection = collect($this->guideContentsTemplate->getByMasterId($masterId));
        $collection = $collection->keyBy('id');
        return $collection->toArray();
    }

    /**
     * データ新規登録
     * @param Request $request
     * @return boolean
     */
    public function createCluster($request)
    {
        guideContentsCluster::beginTransaction();
        guideContentsValue::beginTransaction();
        try {
            $masterId = $request['master_id'];
            $order = $request['order_no'];

            $data = [
                'master_id' => $masterId,
                'name' => $request['name'],
                'cluster_key' => $request['cluster_key'],
                'period_flag' => $request['period_flag'],
                'begin_time' => $request['begin_time'],
                'end_time' => $request['end_time'],
                'view_flag' => $request['view_flag'],
                'memo' => $request['memo'],
            ];
            if ($request['period_flag'] == 0) {
                $data['begin_time'] = null;
                $data['end_time'] = null;
            }

            // 末尾へ追加する場合
            if ($order == -1) {
                $data['order_no'] = $this->guideContentsCluster->getLastOrder($masterId) + 1;
            } else { // 末尾以外に追加する場合
                $data['order_no'] = $order;
                $this->guideContentsCluster->incrementOrder($masterId, $order);
            }

            $clusterId = $this->guideContentsCluster->insertGetId($data);

            $datum = [];
            foreach ($request['values'] as $key => $value) {
                $datum[] = [
                    'cluster_id' => $clusterId,
                    'template_id' => $key,
                    'value' => $value['value'],
                ];
            }
            $this->guideContentsValue->insert($datum);

            if (isset($request['images'])) {
                $imageIds = array_keys($request['images']);
                $this->guideContentsImage->updateClusterId($imageIds, $clusterId);
            }
        } catch (exception $e) {
            guideContentsCluster::rollback();
            guideContentsValue::rollback();
            log::error(var_export($e->getmessage(), true));
            throw $e;
        }
        guideContentsCluster::commit();
        guideContentsValue::commit();

        return true;
    }

    /**
     * データ更新
     * @param Request $request
     * @return boolean
     */
    public function updateCluster($request)
    {
        guideContentsCluster::beginTransaction();
        guideContentsValue::beginTransaction();
        try {
            $data = [
                'name' => $request['name'],
                'period_flag' => $request['period_flag'],
                'begin_time' => $request['begin_time'],
                'end_time' => $request['end_time'],
                'view_flag' => $request['view_flag'],
                'memo' => $request['memo'],
            ];
            if ($request['period_flag'] == 0) {
                $data['begin_time'] = null;
                $data['end_time'] = null;
            }

            $this->guideContentsCluster->edit($data, $request['id']);

            $newOrder = $request['order_no'];
            if ($newOrder != 0) {
                $contentsCluster = $this->guideContentsCluster->getContentsCluster($request['id']);
                $masterId = $contentsCluster['master_id'];
                $currentOrder = $contentsCluster['order_no'];

                $this->guideContentsCluster->setOrderNo($request['id'], 0);

                // 末尾へ移動する場合
                if ($newOrder == -1) {
                    $newOrder = $this->guideContentsCluster->getLastOrder($masterId) + 1;
                }

                // 前に移動する場合
                if ($newOrder < $currentOrder) {
                    $this->guideContentsCluster->incrementOrder($masterId, $newOrder, $currentOrder);
                    $this->guideContentsCluster->setOrderNo($request['id'], $newOrder);
                } else { // 後ろに移動する場合
                    $this->guideContentsCluster->decrementOrder($masterId, $currentOrder, $newOrder);
                    $this->guideContentsCluster->setOrderNo($request['id'], $newOrder - 1);
                }
            }

            foreach ($request['values'] as $templateId => $value) {
                $upsertData = ['value' => $value['value']];
                $this->guideContentsValue->upsert($upsertData, $request['id'], $templateId);
            }
        } catch (Exception $e) {
            guideContentsCluster::rollback();
            guideContentsValue::rollback();
            Log::error(var_export($e->getMessage(), true));
            throw $e;
        }
        guideContentsCluster::commit();
        guideContentsValue::commit();

        return true;
    }

    /**
     * データ削除
     * @param integer $clusterId
     * @return boolean
     */
    public function deleteCluster($clusterId)
    {
        GuideContentsCluster::beginTransaction();
        GuideContentsValue::beginTransaction();
        try {
            $contentsCluster = $this->guideContentsCluster->getContentsCluster($clusterId);

            $this->deleteImageByClusterId($clusterId);
            $this->guideContentsValue->deleteByClusterId($clusterId);
            $this->guideContentsCluster->destroy($clusterId);
            $this->guideContentsCluster->decrementOrder($contentsCluster['master_id'], $contentsCluster['order_no']);
        } catch (Exception $e) {
            GuideContentsCluster::rollback();
            GuideContentsValue::rollback();
            Log::error(var_export($e->getMessage(), true));
            throw $e;
        }
        GuideContentsCluster::commit();
        GuideContentsValue::commit();

        return true;
    }

    /**
     * 表示ステータス切替
     * @param integer $clusterId
     * @return boolean
     */
    public function switchClusterViewStatus($clusterId)
    {
        $contentsCluster = $this->guideContentsCluster->getContentsCluster($clusterId);
        $this->guideContentsCluster->switchViewStatus($clusterId);
        return true;
    }

    /**
     * ファイルアップロード
     * @param Request $request
     * @return array
     */
    public function uploadClusterImage($request)
    {
        // 一時ファイルをアップロード作業用フォルダに移動
        $objFile = $request['image_file'];
        $imagePath = $objFile->getRealPath();

        $fileName = $objFile->getClientOriginalName();
        $fileName = $this->getUploadFileName($fileName);
        $uploadPath = $this->imageFilePath($request['guide_application_id']);

        // img-freegame にアップ
        $resultFile = $this->uploadFile($imagePath, $fileName, $uploadPath);

        // pics にアップ
        $resultFileToPics = $this->uploadFileToPics($imagePath, $fileName, $uploadPath);

        // ローカルの作業用ファイルを削除
        \File::delete($imagePath);

        // アップ失敗時
        if (!$resultFile || !$resultFileToPics) {
            $success = false;
            $imgId = '';
            $msg = '$imagePath=' . var_export($imagePath, true)
                    . ', $fileName=' . var_export($fileName, true)
                    . ', $uploadPath=' . var_export($uploadPath, true)
                    . ', $resultFile=' . var_export($resultFile, true)
                    . ', $resultFileToPics=' . var_export($resultFileToPics, true);
            Log::error($msg);
        } else { // アップ成功時
            $success = true;
            $params = [
                'cluster_id' => empty($request['id']) ? 0 : $request['id'],
                'image' => $fileName,
            ];
            $imgId = $this->guideContentsImage->insertGetId($params);
        }

        $data = [
            'files' => [
                'file' => [
                    'name' => $fileName,
                    'id' => $imgId,
                    'guideappid' => $request['guide_application_id'],
                    'success' => $success,
                ],
            ],
        ];
        return $data;
    }

    /**
     * ファイル名整形
     * @param string $fileName
     * @return string
     */
    private function getUploadFileName($fileName)
    {
        $str = preg_split("/\./", $fileName);
        return sprintf('%s.%s', $this->makeRandImageName(), $str[1]);
    }

    /**
     * ファイルパス取得
     * @param integer $guideAppId
     * @return string
     */
    private function imageFilePath($guideAppId)
    {
        return 'guide/' . $guideAppId . '/contents_data/';
    }

    /**
     * アップロード画像削除(マスターID指定)
     * @param integer $masterId
     * @return boolean
     */
    public function deleteImageByMasterId($masterId)
    {
        $imageList = $this->guideContentsImage->getByMasterId($masterId);
        if (empty($imageList)) {
            return true;
        }

        // DBレコード削除
        if (!$this->guideContentsImage->deleteByMasterId($masterId)) {
            return false;
        }

        $imageList = $imageList->toArray();
        foreach ($imageList as $image) {
            $this->deleteImageFile($image['guide_application_id'], $image['image']);
        }
        return true;
    }

    /**
     * アップロード画像削除(データID指定)
     * @param array|int $clusterId
     * @return boolean
     */
    public function deleteImageByClusterId($clusterId)
    {
        $imageList = $this->guideContentsImage->getByClusterId($clusterId);
        if (empty($imageList)) {
            return true;
        }

        // DBレコード削除
        if (!$this->guideContentsImage->deleteByClusterId($clusterId)) {
            return false;
        }

        $imageList = $imageList->toArray();
        foreach ($imageList as $image) {
            $this->deleteImageFile($image['guide_application_id'], $image['image']);
        }
        return true;
    }

    /**
     * アップロード画像削除
     * @param integer $guideAppId
     * @param string $fileName
     * @param integer $imageId
     * @return boolean
     */
    public function deleteClusterImage($guideAppId, $fileName, $imageId)
    {
        // DBレコード削除
        if (!$this->guideContentsImage->destroy($imageId)) {
            return false;
        }

        // 画像ファイル削除
        $this->deleteImageFile($guideAppId, $fileName);
        return true;
    }

    /**
     * 画像ファイル削除
     * @param integer $guideAppId
     * @param string $image
     * @return boolean
     */
    private function deleteImageFile($guideAppId, $image)
    {
        $uploadFile = $this->imageFilePath($guideAppId) . $image;

        // img-freegameから削除
        $resultFile = $this->deleteFile($uploadFile);

        // picsから削除
        $resultFileToPics = $this->deleteFileToPics($uploadFile);

        // 片方でも失敗していたら削除失敗とする
        if (!$resultFile || !$resultFileToPics) {
            $msg = '$uploadFile=' . var_export($uploadFile, true)
                    . ', $resultFile=' . var_export($resultFile, true)
                    . ', $resultFileToPics=' . var_export($resultFileToPics, true);
            Log::error($msg);
            return false;
        }
        return true;
    }

    /**
     * 編集権限確認
     * @param integer $guideAppId
     * @return boolean
     */
    public function allowedEdit($guideAppId)
    {
        $userId = auth_user_id();
        if (empty($userId)) {
            return false;
        }
        if (auth_is_pf()) {
            return true;
        } else {
            $idList = $this->devGuideApp->getListGuideAppIdByDevId($userId);
            if (in_array($guideAppId, $idList->toArray())) {
                return true;
            }
        }
        return false;
    }

    /**
     * ページタイトル・パンくず情報取得
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.GuideContents');
        return [
            'screenName' => $privateConfigs['screenName'],
            'menuName' => $privateConfigs['menuName'],
        ];
    }
}
