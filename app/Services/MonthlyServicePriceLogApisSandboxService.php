<?php
namespace App\Services;

use App\Models\FreegameSandbox\MonthlyService;
use App\Models\FreegameDeveloper\DeveloperParentage;
use App\Models\FreegameDeveloper\MonthlyServiceMismatchedPriceSandbox;
use App\Models\FreegameDeveloper\MonthlyServiceTotalPriceSandbox;

/**
 * 月額課金比較API(サンドボックス)
 */
class MonthlyServicePriceLogApisSandboxService extends CustomService
{
    protected $MonthlyService;
    protected $DeveloperParentage;
    protected $MonthlyServiceMismatchedPriceSandbox;
    protected $MonthlyServiceTotalPriceSandbox;

    public function __construct(
        MonthlyService                       $MonthlyService,
        DeveloperParentage                   $DeveloperParentage,
        MonthlyServiceMismatchedPriceSandbox $MonthlyServiceMismatchedPriceSandbox,
        MonthlyServiceTotalPriceSandbox      $MonthlyServiceTotalPriceSandbox
    ) {
        $this->MonthlyService                       = $MonthlyService;
        $this->DeveloperParentage                   = $DeveloperParentage;
        $this->MonthlyServiceMismatchedPriceSandbox = $MonthlyServiceMismatchedPriceSandbox;
        $this->MonthlyServiceTotalPriceSandbox      = $MonthlyServiceTotalPriceSandbox;
    }

    /**
     * CSVファイル名取得
     *
     * @param  object $condition
     *
     * @return string
     *
     */
    public function getCsvFileName($condition = [])
    {
        if (empty($condition['begin'])) {
            $condition['begin'] = '1970/01/01';
        }
        if (empty($condition['end'])) {
            $condition['end'] = date('Y/m/d');
        }
        $date = date('Y-m-d', strtotime($condition['begin']));
        if ($condition['begin'] != $condition['end']) {
            $date = $date.'_'.date('Y-m-d', strtotime($condition['end']));
        }

        return sprintf(
            config('forms.MonthlyServicePriceLogApisSandbox.CsvFileName'),
            $condition['monthly_service_id'],
            $date
        );
    }

    /**
     * CSVヘッダー取得
     *
     * @return array  $header
     *
     */
    public function getCsvHeader()
    {
        $header      = [];
        $header['1'] = '月額サービスID';
        $header['2'] = 'ペイメントID(送信データ)';
        $header['3'] = '単価(送信データ)';
        $header['4'] = 'ステータス(送信データ)';
        $header['5'] = 'ペイメントID(弊社データ)';
        $header['6'] = '単価(弊社データ)';
        return $header;
    }

    /**
     * CSV出力データ取得
     *
     * @param  object $condition
     *
     * @return array  $list
     *
     */
    public function getCsvList($condition = [])
    {
        $list = [];

        $listStatus = config('forms.MonthlyServicePriceLogApisSandbox.listStatus');

        $search = [
            'monthly_service_id' => $condition['monthly_service_id'],
            'begin'              => $condition['begin'].' 00:00:00',
            'end'                => $condition['end'].' 23:59:59',
        ];

        $dataTotalPrice = $this->MonthlyServiceTotalPriceSandbox->getTotal($search);

        $list[] = [
            '1' => '',
            '2' => '合計'
        ];
        $list[] = [
            '1' => 'SAP側のみ',
            '2' => array_get($dataTotalPrice, 'game_total_price', 0)
        ];
        $list[] = [
            '1' => 'DMM側のみ',
            '2' => array_get($dataTotalPrice, 'dmm_total_price', 0)
        ];
        $list[] = [
            '1' => '双方に存在',
            '2' => array_get($dataTotalPrice, 'matched_total_price', 0)
        ];
        $list[] = [];
        $list[] = $this->getCsvHeader();

        $listMismatchedPrice = $this->MonthlyServiceMismatchedPriceSandbox->getList($search);

        foreach ($listMismatchedPrice as $val) {
            $list[] = [
                '1' => $val->monthly_service_id,
                '2' => $val->game_payment_id,
                '3' => $val->game_unit_price,
                '4' => $listStatus[$val->compare_status],
                '5' => $val->dmm_payment_id,
                '6' => $val->dmm_unit_price,
            ];
        }

        return $list;
    }

    /**
     * 月額サービス一覧取得
     *
     * @param  array $conditions
     *
     * @return array $list
     *
     */
    public function getServiceList($conditions = [])
    {
        $list = [];

        $developerids   = [];
        $developerids[] = auth_user_id();
        if (!auth_is_user_admin()) {
            $listTmp = $this->DeveloperParentage->getList(['parent_id' => auth_user_id()]);
            foreach ($listTmp as $val) {
                $developerids[] = $val->developer_id;
            }
        }

        $search = [];
        $search = ['status' => 'active'];
        $search = ['developer_id' => $developerids];

        $listTmp = $this->MonthlyService->getListWithApplication($search);
        foreach ($listTmp as $val) {
            $list[$val->id] = $val->service_name.'（'.$val->title.'）';
        }

        return $list;
    }
}
