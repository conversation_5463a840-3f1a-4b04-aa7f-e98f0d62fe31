<?php
namespace App\Services;

use App\Models\FreegameDeveloper\Information;
use App\Services\Accessory\InformationFormat;

/**
 * TOP
 */
class IndexService extends CustomService
{
    use InformationFormat;

    protected $information;

    public function __construct(
        Information $information
    ) {
        $this->information = $information;
    }

    /**
     * お知らせ一覧取得
     *
     * @return array
     */
    public function getInformationList()
    {
        $list = $this->information->getList(
            [
                'category' => [
                    'info',
                    'info_important',
                    'required_important',
                    'required_info'
                ],
                'take' => config('forms.Index.topTake'),
            ]
        );

        return $this->formatInfoList($list);
    }

    /**
     * 障害報告一覧取得
     *
     * @return array
     */
    public function getFailureList()
    {
        $list = $this->information->getList(
            [
                'category' => ['failure_recovery', 'failure_occurrence'],
                'take'     => config('forms.Index.topTake'),
            ]
        );

        return $this->formatFailureList($list);
    }

    /**
     * ページ設定取得
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.Index');

        return [
            'formData' => [
                'screenName' => $privateConfigs['screenName'],
                'breadcrumbs' => $privateConfigs['breadcrumbsParent'],
            ],
            'categoryType' => $privateConfigs['categoryType'],
        ];
    }

    /**
     * Set language
     *
     * @param string $languageCode
     */
    public function setLanguage($languageCode)
    {
        if (!array_key_exists($languageCode, config('forms.common.language.list'))) {
            $languageCode = config('forms.common.language.default');
        }

        request()->session()->set(
            config('forms.common.language.sessionName'),
            $languageCode
        );
    }
}
