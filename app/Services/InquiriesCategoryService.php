<?php
namespace App\Services;

use App\Models\Freegame\Application;
use App\Models\FreegameDeveloper\FreegameDeveloper;
use App\Models\FreegameDeveloper\InquiryCategoryRef;
use App\Models\FreegameDeveloper\InquiryCategory;
use App\Models\FreegameDeveloper\DeveloperApplication;

class InquiriesCategoryService extends CustomService
{

    protected $application;

    protected $developerApplication;

    public function __construct(
        Application $application,
        InquiryCategoryRef $inquiryCategoryRef,
        InquiryCategory $inquiryCategory,
        DeveloperApplication $developerApplication
    ) {
        $this->application = $application;
        $this->inquiryCategoryRef = $inquiryCategoryRef;
        $this->inquiryCategory = $inquiryCategory;
        $this->developerApplication = $developerApplication;
    }

    // ページネーション用メソッド
    public function getList($condition = [])
    {
        $condition['perPage'] = config('forms.InquiriesCategory.pager.index.perPage');
        $paginator = $this->application->getList($this->formatSearchQuery($condition));
        return $paginator;
    }

    public function formatSearchQuery($search = [])
    {
        $appTitleType = $this->getAppTitleType();
        if (empty($search['app_id']) || ! isset($appTitleType[$search['app_id']])) {
            if (auth_is_sap()) {
                $search['app_id'] = array_keys($appTitleType);
                if (empty($search['app_id'])) {
                    $search['app_id'] = '-1';
                }
            } else {
                $search['app_id'] = '';
            }
        }
        $search = array_only($search, [
            'app_id',
            'perPage'
        ]);

        return $search;
    }

    // 検索パラメータの保持と読み込み
    public function formatSearchCondition() {
        $sessionName = 'InquiriesCategory.index';
        if (request()->has('search')) {
            $search = session($sessionName, []);
            request()->merge($search);
        }

        request()->session()->set($sessionName, request()->all());

        return true;
    }
    // ここまでページネーション用

    public function getFormData()
    {
        return [
            'menuName'     => config('forms.InquiriesCategory.menuName'),
            'screenName'   => config('forms.InquiriesCategory.screenName'),
            'appTitleType' => $this->getAppTitleType(),
        ];
    }

    public function getAppTitleType()
    {
        if (isset($this->appTitleType)) {
            return $this->appTitleType;
        }
        if (auth_is_sap()) {
            $devAppList = $this->developerApplication->getApplicationAppIdList([
                'developer_id' => auth_user_id()
            ]);
            if (empty($devAppList->count())) {
                return [];
            } else {
                foreach ($devAppList as $data) {
                    $condition['id'][] = $data->app_id;
                }
                $list = $this->application->getApplicationTitleList($condition);
            }
        } else {
            $list = $this->application->getApplicationTitleList();
        }
        $opts = [];
        foreach ($list as $data) {
            $opts[$data->id] = $data->title;
        }
        $this->appTitleType = $opts;
        return $opts;
    }

    /**
     * 引数の文字列が格納されているinquiry_categoryテーブルのidを返す。
     *
     * @param  string  検索する文字列
     * @return integer 格納されているレコードid
     */
    public function getInquiryCategoryId($name)
    {
        $result = $this->inquiryCategory->getId($name);

        if (empty($result)) {
            // 存在しなければ新規作成してそのidを返す。
            $inquiryCategoryId = $this->inquiryCategory->store($name);
        } else {
            // 存在するのなら取得したidを返す
            $inquiryCategoryId = $result->id;
        }
        return $inquiryCategoryId;
    }

    /**
     * 引数のapp_idがアカウントで選択できる値か確認
     *
     * @param  integer タイトルid
     * @param  boolean フォームバリデーション用に呼んでいるか(yes=true)
     * @return integer 格納されているレコードid
     */
    public function checkPermitAppId($appId, $formValidation=null)
    {
        if (empty($appId) && $formValidation==true) {
            return true;    // フォームバリデーションで空文字なら空文字用メッセージを優先
        } elseif (empty($appId)) {
            return false;
        }
        $appTitleType = $this->getAppTitleType();
        return array_key_exists((integer)$appId, $appTitleType);
    }

    // inquiryCategoryRefに指定idのレコードが存在するか確認。
    public function existCategoryRefID($id, $relation=null)
    {
        if ( empty($id) ) {
            return false;
        }

        return empty( $this->inquiryCategoryRef->getCategoryRefID($id, $relation) ) ? false : true ;
    }

    // 対象タイトルの指定階層カテゴリに、指定のカテゴリが持つinquiry_category_idが存在しない事を確認。
    public function isNotInquiryCategoryId($request, $relation) {
        if ( empty($request) ){
            return false;
        }

        $condition = [
            'app_id'   => $request['target_app_id'],
            'relation' => $relation,
            'large_id' => $request['large_id'],
        ];

        return ( empty($this->inquiryCategoryRef->getExistInquiryCategoryId($condition)) ) ? true : false ;
    }

    // 特定の大カテゴリIDに所属するグループのみを取得
    public function getCategoryStructureLarge($param=[]) {
        if (empty($param['registCategory']) || empty($param['appId'])) {
            return [];
        }
        $categoryStructureParam = (empty($param['categoryStructureParam'])) ? [] : $param['categoryStructureParam'] ;
        $categoryStructureIndex  = $this->getCategoryStructure($param['registCategory'], $categoryStructureParam);
        $tableRow = [];
        foreach ($categoryStructureIndex[ $param['appId'] ] as $k => $v) {
            if ($v['keyLarge'] == $param['largeId'] ) {
                $tableRow[] = $v;
            }
        }

        return $tableRow;
    }

    /**
     * 文言指定に被りが発生しないか確認（大カテゴリ用）
     *
     * @param  array   確認用パラメータ
     * @param  array   新規追加・編集フォームのリクエスト
     * @return boolean 被りが無ければtrue
     */
    public function isNotSameValueLarge($param, $request=[]) {
        if (empty($param)) {
            return false;
        }
        $isNotSameValue = true;

        // 新規追加の場合はリクエスト内の大カテゴリ名被りを確認
        if ($param['mode'] == 'create') {
            foreach ($request as $keyRequest => $valRequest) {
                if ($keyRequest == $param['selfKey']) {
                    // 自分自身は比較しない
                    continue;
                }

                if (preg_match($param['pattern'], $keyRequest, $match)) {
                    if ($request[$match[1]."_".$match[2]."_".$match[3]] == $param['value']) {
                        $isNotSameValue = false;
                        break;
                    }
                }
            }
        }

        // 同じタイトル・同じ名前の大カテゴリが無いかDBを確認。
        if ($isNotSameValue) {
            $condition = [
                'app_id'        => $param['app_id'],
                'relation'      => 'large',
                'category_name' => $param['value'],
            ];
            foreach ($this->inquiryCategoryRef->getCategory($condition) as $v) {
                $isNotSameValue = false;    // 取得できるなら、同じタイトルに同じ名前の大カテゴリがある
                break;
            }
        }

        return $isNotSameValue;
    }

    /**
     * 登録リクエストに値じ文言指定が無いか確認（中カテゴリ用）
     *
     * @param  array   新規追加・編集フォームのリクエスト
     * @param  array   確認用パラメータ
     * @return boolean 被りが無ければtrue
     */
    public function isNotSameValueMiddle($request, $param) {
        if (empty($request) || empty($param)) {
            return false;
        }
        $isNotSameValue = true;
        foreach ($request as $keyRequest => $valRequest) {
            if ($keyRequest == $param['selfKey']) {
                // 自分自身は比較しない
                continue;
            }
            if (preg_match($param['pattern'], $keyRequest, $match)) {
                if ($request[$match[1]."_".$match[2]."_".$match[3]] == $param['value']) {
                    $isNotSameValue = false;
                    break;
                }
            }
        }

        return $isNotSameValue;
    }

    /**
     * 登録リクエストに値じ文言指定が無いか確認（小カテゴリ用）
     *
     * @param  array   新規追加・編集フォームのリクエスト
     * @param  array   確認用パラメータ
     * @return boolean 被りが無ければtrue
     */
    public function isNotSameValueSmall($request, $param) {
        if (empty($request) || empty($param)) {
            return false;
        }
        $isNotSameValue = true;

        foreach ($request as $keyRequest => $valRequest) {
            if ($keyRequest == $param['selfKey']) {
                continue;    // 自分自身は比較しない
            }
            if ($valRequest == $param['value']) {
                $isNotSameValue = false;
                break;
            }
        }

        return $isNotSameValue;
    }

    /**
     * リクエストを配列化
     *
     * @param  array   新規追加・編集フォームのリクエスト
     * @param  string  条件分岐用文字列
     * @return array   配列化したリクエスト
     */
    public function getInputCategory($condition, $mode='') {
        $editInput = [];
        foreach ($condition as $keyCondition => $valCondition) {
            if (preg_match('#(.+)_(\d+)_(\d+)#', $keyCondition, $match)) {

                switch ($match[1]) {
                    case 'large':
                        if ($mode == 'edit') {
                            $editInput['large'] = $valCondition;
                        } else {
                            $editInput[ $match[2] ]['large'] = $valCondition;
                        }
                        break;
                    case 'middle':
                        if ($mode == 'edit') {
                            $editInput['middle'][] = $valCondition;
                        } else {
                            $editInput[ $match[2] ]['middle'][ $match[3] ] = $valCondition;
                        }
                        break;
                    case 'small':
                        if ($mode == 'edit') {
                            $editInput['small'][] = $valCondition;
                        } else {
                            $editInput[ $match[2] ]['small'][ $match[3] ] = $valCondition;
                        }
                        break;
                    default:
                        break;
                }
            }
        }
        return $editInput;
    }

    /**
     * 各カテゴリの並び位置を決定し、ループを行う為の配列を作成する
     *
     * @param  array DBから取得したカテゴリ情報
     * @param  array 条件分岐用パラメータ（smallKeyAscend=>trueの時、小カテゴリ配列を昇順にする）
     * @return array ループ用配列
     */
    public function getCategoryStructure($registCategory, $param=[]) {
        if (empty($registCategory)) {
            return [];
        }

        $tableRow     = [];
        $largeEnd     = [];
        $countLarge   = 0;
        foreach ($registCategory['large'] as $keyLarge => $valLarge) {
            $appId = $valLarge['appId'];
            $countMiddle = 0;
            $startLarge  = (empty($tableRow[$appId])) ? 0 : count($tableRow[$appId]);

            foreach ($registCategory['middle'] as $keyMiddle => $valMiddle) {
                if ($valMiddle['parentId'] == $valLarge['refId']) {
                    // 小カテゴリ用配列の用意
                    $smallName  = [];
                    $smallParam = [];
                    foreach ($registCategory['small'] as $keySmall => $valSmall) {
                        if ($valSmall['parentId'] == $valMiddle['refId']) {
                            $smallName[ $valSmall['refId'] ] = $valSmall['categoryName'];
                            $smallParam[ $valSmall['refId']] = [
                                'refId'    => $valSmall['refId'],
                                'notBlank' => (! empty($valSmall['categoryName'])) ? true : false,
                            ];
                        }
                    }
                    if (! empty($smallName) && ! empty($param['smallKeyAscend'])) {
                        $smallName  = array_merge($smallName, []); 
                        $smallParam = array_merge($smallParam, []); 
                    }

                    $tableRow[$appId][] = [
                        'type'        => ($countMiddle == 0) ? 'large' : 'middle',
                        'keyLarge'    => $valLarge['refId'],
                        'nameLarge'   => $valLarge['categoryName'],
                        'countLarge'  => $countLarge,
                        'keyMiddle'   => $valMiddle['refId'],
                        'nameMiddle'  => $valMiddle['categoryName'],
                        'countMiddle' => $countMiddle,
                        'smallName'   => $smallName,
                        'smallParam'  => $smallParam,
                        'endLarge'    => false,
                        'notBlank'    => true,
                    ];
                    $countMiddle++;
                }
            }

            if ($countMiddle == 0) {
                // 中カテゴリ以下が存在しない場合
                $tableRow[$appId][] = [
                    'type'        => 'large',
                    'refId'       => $valLarge['refId'],
                    'keyLarge'    => $valLarge['refId'],
                    'nameLarge'   => $valLarge['categoryName'],
                    'countLarge'  => $countLarge,
                    'keyMiddle'   => 0,
                    'nameMiddle'  => '',
                    'countMiddle' => 0,
                    'smallName'   => [],
                    'smallParam'  => [],
                    'endLarge'    => true,
                    'notBlank'    => false,
                ];
            } else {
                $tableRow[$appId][$startLarge]['rowspan'] = (count($tableRow[$appId]) - $startLarge);    // 最初の大カテゴリ列にrowspan情報を追加
                $tableRow[$appId][ count($tableRow[$appId])-1 ]['endLarge'] = true;    // 大カテゴリの区切りとなる配列にフラグを追加
            }
            $countLarge++;
        }

        return $tableRow;
    }

    /**
     * 新規追加画面用配列を作成
     *
     * @param  array DBから取得したカテゴリ情報
     * @param  array 条件分岐用パラメータ（'smallKeyAscend'=trueの時、小カテゴリ配列のキーを昇順にする）
     * @return array 新規追加画面用配列
     */
    public function createCategoryStructure($request, $initialize=false) {

        // 新規追加の初期画面用配列を定義。
        $initializeCategory = function()
        {
            $categoryStructure = [];
            for ($countLarge=0; $countLarge<config('forms.InquiriesCategory.createBlank.large'); $countLarge++) {
                for ($trRow=0; $trRow<config('forms.InquiriesCategory.createBlank.middle'); $trRow++) {
                    $categoryStructure[] = [
                        'type'        => ($trRow == 0) ? 'large' : 'middle',
                        'nameLarge'   => '',
                        'countLarge'  => $countLarge,
                        'nameMiddle'  => '',
                        'countMiddle' => $trRow,
                        'smallName'   => array_fill(0, config('forms.InquiriesCategory.createBlank.small'), ''),
                    ];
                }
            }

            return $categoryStructure;
        };

        $categoryStructure = [];
        if (empty($request)) {
            // リクエストなし（初回表示）
            $categoryStructure = $initializeCategory();
            $this->setStatusEndRow($categoryStructure);
            return $categoryStructure;
        }

        // 初期化フラグがあるなら初期の空欄は復帰する。
        if ($initialize == 'initialize') {
            $categoryStructure = $initializeCategory();
        }

        $createInput = $this->getInputCategory($request, 'create');

        $countRow = 0;
        foreach ($createInput as $keyCreteInput => $valCreteInput) {

            $countMiddle = 0;
            foreach ($valCreteInput['middle'] as $keyMiddle => $valMiddle) {

                $smallName  = [];
                if (! empty($valCreteInput['small'][$keyMiddle]) ) {
                    // 復帰する空欄の数を数える
                    $countSmall = ( empty($categoryStructure[$countRow]['smallName']) ) ? 0 : count($categoryStructure[$countRow]['smallName']) ;
                    foreach ($valCreteInput['small'][$keyMiddle] as $keySmall => $valSmall) {
                        if (! $initialize && empty($valSmall)) {
                            continue;
                        }
                        $smallName[] = $valSmall;
                    }
                    // 必要があれば空欄の復帰
                    for ($i=count($smallName); $i<$countSmall; $i++) {
                        $smallName[] = '';
                    }
                }

                if ($countMiddle > 0 && empty($valCreteInput['middle'][$keyMiddle]) && empty($smallName)) {
                    // 大カテゴリを含まない列に何も入力されていないならスキップ
                    continue;
                }

                $categoryStructure[$countRow] = [
                    'type'        => ($countMiddle == 0) ? 'large' : 'middle',
                    'nameLarge'   => $valCreteInput['large'],
                    'countLarge'  => $keyCreteInput,
                    'nameMiddle'  => $valCreteInput['middle'][$keyMiddle],
                    'countMiddle' => $countMiddle,
                    'smallName'   => $smallName,
                ];
                $countMiddle++;
                $countRow++;
            }
        }

        if ($initialize == 'initialize' && $countRow == 0) {
            // 初期化フラグがあって空なら初期表示配列を取得。
            $categoryStructure = $initializeCategory();
        }
        $this->setStatusEndRow($categoryStructure);

        return $categoryStructure ; 
    }

    /**
     * 新規作成のカテゴリ配列に大カテゴリの区切りを示すステータスを付与する
     *
     * @param  array   カテゴリ配列
     * @return boolean 最後まで処理完了したときtrue
     */
    static function setStatusEndRow(&$categoryStructure)
    {
        if ( empty($categoryStructure) ) {
            return false;
        }

        $lastLarge=0;
        $endLarge=0;

        foreach ($categoryStructure as $keyRow => $valRow) {
            $categoryStructure[$keyRow]['lastLarge'] = false;
            $categoryStructure[$keyRow]['endLarge']  = false;

            if ($valRow['type'] == 'large') {
                $lastLarge = $keyRow;    // 最後の大カテゴリ情報が存在する位置を保持

                if ($keyRow>0) {
                    // 別の大カテゴリ情報になったら前の列に区切り情報をセット
                    $categoryStructure[$keyRow - 1]['endLarge'] = true;
                }
            }
        }

        $categoryStructure[count($categoryStructure) - 1]['endLarge'] = true;
        $categoryStructure[$lastLarge]['lastLarge'] = true;

        return true;
    }

    /**
     * 新規作成の実行(DB書込)
     *
     * @param  array   新規追加画面用配列
     * @param  integer タイトルid
     * @return boolean 最後まで処理完了したときtrue
     */
    public function storeCreate($condition, $appId)
    {
        if (empty($appId)) {
            return false;
        }

        FreegameDeveloper::beginTransaction();
        try {

            $parentLargeId  = 0;
            foreach ($condition as $keyCondition => $valCondition) {
                $parentMiddleId = 0;

                // 大カテゴリ
                if ( empty($valCondition['nameLarge']) ) {
                    continue;
                }
                if ( $valCondition['type'] == 'large' ) {
                    $param = [
                        'inquiry_category_id' => $this->getInquiryCategoryId($valCondition['nameLarge']),
                        'app_id'              => $appId,
                        'relation'            => 'large',
                    ];
                    $parentLargeId            = $this->inquiryCategoryRef->store($param);
                }

                // 中カテゴリ
                if (! empty($valCondition['nameMiddle']) && $parentLargeId != 0 ) {
                    $param = [
                        'inquiry_category_id' => $this->getInquiryCategoryId($valCondition['nameMiddle']),
                        'app_id'              => $appId,
                        'relation'            => 'middle',
                        'parent_id'           => $parentLargeId,
                    ];

                    $parentMiddleId = $this->inquiryCategoryRef->store($param);
                }
                // 小カテゴリ
                if (empty($valCondition['smallName']) || $parentMiddleId == 0 ) {
                    continue;
                }
                foreach ($valCondition['smallName'] as $keySmall => $valSmall) {
                    $param = [
                        'inquiry_category_id' => $this->getInquiryCategoryId($valSmall),
                        'app_id'              => $appId,
                        'relation'            => 'small',
                        'parent_id'           => $parentMiddleId,
                    ];

                    $this->inquiryCategoryRef->store($param);
                }
            }
            FreegameDeveloper::commit();

        } catch (Exception $e) {
            FreegameDeveloper::rollback();
            throw $e;

            return false;
        }

        return true;
    }

    /**
     * 編集画面で扱えるように、DBから取得したカテゴリに画面からリクエストされたカテゴリの情報を加える
     *
     * @param  array   DBから取得したカテゴリ配列（参照渡し）
     * @param  array   画面からリクエストされたカテゴリ
     * @param  string  モード(edit|confirm)
     * @return boolean 最後まで処理完了したときtrue
     */
    public function setEditData(&$categoryStructure=[], $condition=[]) {
        if (empty($categoryStructure) || empty($condition)) {
            return false;
        }
        $isAllDone = true;

        // リクエストを配列化
        $editInput = $this->getInputCategory($condition, 'edit');

        // 大カテゴリ
        $lageLine = 0;    // 編集の場合、大カテゴリ情報は配列の先頭のみに存在し編集のみ行える。新規追加は無い。
        if (! empty($editInput['large'])) {
            if ($categoryStructure[$lageLine]['nameLarge'] == $editInput['large']) {
                $categoryStructure[$lageLine]['editTypeLarge'] = 'noChange';
            } else {
                $categoryStructure[$lageLine]['editTypeLarge'] = 'update';
                $categoryStructure[$lageLine]['nameLarge'] = $editInput['large'];
            }
        } else {
            return false;
        }

        // 中カテゴリ
        $skipMiddleKey = [];
        if (! empty($editInput['middle'])) {
            $loop=0;
            foreach ($editInput['middle'] as $keyEditInput => $valEditInput) {

                if (empty($categoryStructure[$loop]['nameMiddle'])) {

                    if ( empty($valEditInput) && ! empty($editInput['small'][$keyEditInput]) && $this->isArrayOfAllEmpty($editInput['small'][$keyEditInput]) ) {
                        $skipMiddleKey[$keyEditInput] = true;
                        continue;
                    }

                    // DBから作ったcategoryStructureの無い列ならviewに必要な情報を用意する
                    if ( empty($categoryStructure[$loop]['type']) ) {
                        $refSuffix = $loop - 1;
                        $categoryStructure[$loop] = [
                            'type'        => 'middle',
                            'nameLarge'   => $categoryStructure[$refSuffix]['nameLarge'],
                            'countLarge'  => $categoryStructure[$lageLine]['countLarge'],    // 大カテゴリと同じ値に揃える
                            'keyLarge'    => $categoryStructure[$refSuffix]['keyLarge'],
                            'countMiddle' => $categoryStructure[$refSuffix]['countMiddle'] + 1,
                            'smallName'   => [],
                            'smallParam'  => [],
                            'endLarge'    => true,
                            'notBlank'    => false,
                        ];
                        $categoryStructure[$refSuffix]['endLarge'] = false;
                    }

                    $categoryStructure[$loop]['editTypeMiddle'] = 'insert';
                    $categoryStructure[$loop]['nameMiddle'] = $valEditInput;
                } else {
                    if ( empty($valEditInput) ) {
                        $isAllDone = false;
                        break;
                    }
                    if ($categoryStructure[$loop]['nameMiddle'] == $valEditInput) {
                        $categoryStructure[$loop]['editTypeMiddle'] = 'noChange';
                    } else {
                        $categoryStructure[$loop]['editTypeMiddle'] = 'update';
                        $categoryStructure[$loop]['nameMiddle'] = $valEditInput;

                    }
                }
                $loop++;
            }
        }
        if (!$isAllDone) {
            return false;
        }

        // 小カテゴリ
        if (! empty($editInput['small'])) {
            $loop=0;
            foreach ($editInput['small'] as $keyEditInput => $valEditInput) {
                if (isset($skipMiddleKey[$keyEditInput])) {
                    // 中カテゴリを読み飛ばした場合は小カテゴリも飛ばす
                    continue;
                }

                foreach ($valEditInput as $keySmall => $valSmall) {
                    if (empty($categoryStructure[$loop]['smallName'][$keySmall])) {
                        if ( empty($valSmall) ) {
                            if ( isset($categoryStructure[$loop]['type']) ) {
                                // 中カテゴリが存在するなら空欄情報を代入
                                $categoryStructure[$loop]['smallName'][$keySmall] = '';
                                $categoryStructure[$loop]['smallParam'][$keySmall]['editType'] = 'noChange';
                            }
                        } else {
                            $refSuffix = $loop - 1;
                            if ( empty($categoryStructure[$loop]['type']) ) {
                                // 新しい中カテゴリを作り、小カテゴリにだけ記述した場合
                                $categoryStructure[$loop] = [
                                    'type'           => 'middle',
                                    'nameLarge'      => $categoryStructure[$refSuffix]['nameLarge'],
                                    'countLarge'     => $categoryStructure[$lageLine]['countLarge'],    // 大カテゴリと同じ値に揃える
                                    'keyLarge'       => $categoryStructure[$refSuffix]['keyLarge'],
                                    'nameMiddle'     => '',
                                    'editTypeMiddle' => 'noChange',
                                    'countMiddle'    => $categoryStructure[$refSuffix]['countMiddle'] + 1,
                                    'endLarge'       => true,
                                    'notBlank'       => false,
                                ];
                                $categoryStructure[$refSuffix]['endLarge'] = false;
                            }

                            $categoryStructure[$loop]['smallParam'][$keySmall]['editType'] = 'insert';
                            $categoryStructure[$loop]['smallName'][$keySmall] = $valSmall;
                        }
                    } else {
                        if ( empty($valSmall) ) {
                            $isAllDone = false;
                            break;
                        }

                        if ($categoryStructure[$loop]['smallName'][$keySmall] == $valSmall) {
                            $categoryStructure[$loop]['smallParam'][$keySmall]['editType'] = 'noChange';
                        } else {
                            $categoryStructure[$loop]['smallParam'][$keySmall]['editType'] = 'update';
                            $categoryStructure[$loop]['smallName'][$keySmall] = $valSmall;
                        }
                    }
                }
                $loop++;
            }
        }
        if (!$isAllDone) {
            return false;
        }

        return true;
    }

    /**
     * 編集を実行する。DB内容を更新もしくは新規追加を行う。
     *
     * @param  array   編集用配列
     * @param  array   タイトルid
     * @return boolean 最後まで処理完了したときtrue
     */
    public function storeEdit($condition, $appId)
    {
        if (empty($condition) || empty($appId)) {
            return false;
        }

        // ひとつのレコードでしか使われていない文言を取得
        $onlyInquiryCategoryId = [];
        $updateRefId = [];
        foreach ($this->inquiryCategoryRef->getOnlyInquiryCategoryId() as $k => $v) {
            $onlyInquiryCategoryId[$v->id] = $v->inquiry_category_id;
        }

        FreegameDeveloper::beginTransaction();
        try {
            foreach ($condition as $keyCondition => $valCondition) {
                // 大カテゴリ
                if (isset($valCondition['keyLarge'])) {
                    // 既存の大カテゴリに中カテゴリを追加する場合
                    $parentLargeId = $valCondition['keyLarge'];
                }
                if ($valCondition['type'] == 'large') {
                    switch ($valCondition['editTypeLarge']) {
                        case 'insert':
                            $param = [
                                'inquiry_category_id' => $this->getInquiryCategoryId($valCondition['nameLarge']),
                                'app_id'              => $appId,
                                'relation'            => 'large',
                            ];
                            $parentLargeId            = $this->inquiryCategoryRef->store($param);
                            break;

                        case 'update':
                            $param = [
                                'id'                  => $valCondition['keyLarge'],
                                'inquiry_category_id' => $this->getInquiryCategoryId($valCondition['nameLarge']),
                                'app_id'              => $appId,
                            ];
                            $this->inquiryCategoryRef->updateToInquiryCategoryId($param);
                            $updateRefId[] = $valCondition['keyLarge'];
                            break;

                        default:    // noCange
                            break;
                    }
                }

                // 中カテゴリ
                if (empty($valCondition['nameMiddle'])) {
                    continue;
                }
                if (isset($valCondition['keyMiddle'])) {
                    // 既存の中カテゴリに小カテゴリを追加する場合
                    $parentMiddleId = $valCondition['keyMiddle'];
                }
                
                switch ($valCondition['editTypeMiddle']) {
                    case 'insert':
                        $param = [
                            'inquiry_category_id' => $this->getInquiryCategoryId($valCondition['nameMiddle']),
                            'app_id'              => $appId,
                            'relation'            => 'middle',
                            'parent_id'           => $parentLargeId,
                        ];
                        $parentMiddleId = $this->inquiryCategoryRef->store($param);
                        break;

                    case 'update':
                        $param = [
                            'id'                  => $valCondition['keyMiddle'],
                            'inquiry_category_id' => $this->getInquiryCategoryId($valCondition['nameMiddle']),
                            'app_id'              => $appId,
                        ];
                        $this->inquiryCategoryRef->updateToInquiryCategoryId($param);
                        $updateRefId[] = $valCondition['keyMiddle'];
                        break;

                    default:    // noCange
                        break;
                }

                // 小カテゴリ
                if (empty($valCondition['smallName'])) {
                    continue;
                }
                foreach ($valCondition['smallName'] as $keySmall => $valSmall) {
                    switch ($valCondition['smallParam'][$keySmall]['editType']) {
                        case 'insert':
                            $param = [
                                'inquiry_category_id' => $this->getInquiryCategoryId($valSmall),
                                'app_id'              => $appId,
                                'relation'            => 'small',
                                'parent_id'           => $parentMiddleId,
                            ];
                            $this->inquiryCategoryRef->store($param);
                            break;

                        case 'update':
                            $param = [
                                'id'                  => $valCondition['smallParam'][$keySmall]['refId'],
                                'inquiry_category_id' => $this->getInquiryCategoryId($valSmall),
                                'app_id'              => $appId,
                            ];
                            $this->inquiryCategoryRef->updateToInquiryCategoryId($param);
                            $updateRefId[] = $valCondition['smallParam'][$keySmall]['refId'];
                            break;

                        default:    // noCange
                            break;
                    }
                }
            }

            // 編集前のリファレンスでのみ使われていた文言が、今回の編集で別のカテゴリに使われていないのであれば削除する。
            $searchId = array_only($onlyInquiryCategoryId, $updateRefId);
            foreach ($searchId as $inquiryCategoryId) {
                if ( empty($this->inquiryCategoryRef->getUseInquiryCategoryId($inquiryCategoryId))) {
                    // 既に使用していない文言なので削除する
                    $this->inquiryCategory->deleteById( [$inquiryCategoryId] );
                }
            }
            FreegameDeveloper::commit();

        } catch (Exception $e) {
            FreegameDeveloper::rollback();
            throw $e;

            return false;
        }

        return true;
    }

    /**
     * カテゴリのコピーを実行
     *
     * @param  array   コピー用配列
     * @param  array   タイトルid
     * @return boolean 最後まで処理完了したときtrue
     */
    public function categoryCopyStore($categoryStructure, $appId) {

        FreegameDeveloper::beginTransaction();
        try {
            $lastNameLarge = "";
            foreach ($categoryStructure as $keyRow => $orderRow) {
                // 大カテゴリのDB書き込みは新しい大カテゴリグループに移ったタイミングで行う
                if ($lastNameLarge != $orderRow['nameLarge']) {
                    $param = [
                        'inquiry_category_id' => $this->getInquiryCategoryId($orderRow['nameLarge']),
                        'app_id'              => $appId,
                        'relation'            => 'large',
                    ];
                    $lastNameLarge = $orderRow['nameLarge'];
                    $parentLargeId = $this->inquiryCategoryRef->store($param);
                }
                if (empty($orderRow['nameMiddle'])) {
                    continue;
                }

                $param = [
                    'inquiry_category_id' => $this->getInquiryCategoryId($orderRow['nameMiddle']),
                    'app_id'              => $appId,
                    'relation'            => 'middle',
                    'parent_id'           => $parentLargeId,
                ];
                $parentMiddleId = $this->inquiryCategoryRef->store($param);
                if (empty($orderRow['smallName'])) {
                    continue;
                }
                foreach ($orderRow['smallName'] as $keySmall => $valSmall) {
                    $param = [
                        'inquiry_category_id' => $this->getInquiryCategoryId($valSmall),
                        'app_id'              => $appId,
                        'relation'            => 'small',
                        'parent_id'           => $parentMiddleId,
                    ];
                    $this->inquiryCategoryRef->store($param);
                }

            }
            FreegameDeveloper::commit();

        } catch (Exception $e) {
            FreegameDeveloper::rollback();
            throw $e;
            return false;
        }

        return true;
    }

    /**
     * 登録しているカテゴリをDBから取得する
     *
     * @param  配列でapp_idを指定
     * @return array 結果をrelation名のキーで分けて配列で返す
     */
    public function getRegistCategory($registAppID=[]){
        if (! is_array($registAppID)) {
            return $adjustEmptyRelation([]);
        }

        // 存在しないrelationの配列があれば空配列を設定する
        $adjustEmptyRelation = function ($registCategory)
        {
            $relation = ['large','middle','small'];
            foreach ($relation as $k => $v) {
                if (empty($registCategory[$v])) {
                    $registCategory[$v] = [];
                }
            }
            return $registCategory;
        };

        $registCategory = [];
        $param = [
            'app_id'   => $registAppID,
            'ordey_by' => [
                'refid_asc',
                'relation_desc'
            ],
        ];

        foreach ($this->inquiryCategoryRef->getCategory($param) as $k => $v) {
            $registCategory[$v->relation][] = array(
                'refId'        => $v->id,
                'categoryName' => $v->category_name,
                'appId'        => $v->app_id,
                'parentId'     => $v->parent_id,
            );
        }

        return $adjustEmptyRelation($registCategory);
    }

    /**
     * カテゴリ削除チェックボックスのname属性名とチェック状況を配列化
     *
     * @param  array カテゴリ構造配列
     * @return array 削除フラグのview用ID名と削除指定状況
     */
    public function getDeleteInput($categoryStructure)
    {
        // 名前を作成
        $delIdName=[];
        foreach ($categoryStructure as $k => $v) {
            if ($k == 0) {
                $delIdName[] = 'del-'.$v['keyLarge'];
            }
            if ($v['keyMiddle'] != 0) {
                $delIdName[] = 'del-'.$v['keyMiddle'];
                foreach ($v['smallName'] as $k2 => $v2) {
                    $delIdName[] = 'del-'.$k2;
                }
            }
        }

        // チェックボックス指定状況確認
        $delSet=[];
        foreach ($delIdName as $k => $v) {
            $delSet[] = ( empty(request($v)) ) ? false : true ;
        }

        return [
            'delIdName' => $delIdName,
            'delSet'    => $delSet
        ];
    }

    /**
     * 削除リクエストからid番号を取得
     *
     * @param  array 削除画面のリクエスト
     * @return array 削除指定されているid
     */
    public function pregmatchDeleteRequest ($request) {
        $deleteId=[];
        foreach ($request as $key => $value) {
            if (! empty($value) && preg_match('#del-(\d+)#', $key, $match)) {
                $deleteId[] = $match[1];
            }
        }
        return $deleteId;
    }

    /**
     * 削除指定されたカテゴリを親に持つ子カテゴリが、全て削除指定されているか確認
     *
     * @param  array   削除画面のリクエスト
     * @return boolean 全ての子カテゴリ（孫カテゴリ）が削除指定されているときtrue
     */
    public function checkUnderRequired($request) {

        $deleteId = $this->pregmatchDeleteRequest($request);

        $isAllRequired = true;
        foreach ($this->inquiryCategoryRef->getAppIdCategory($request['app_id']) as $key => $value) {
            // 削除指定されているカテゴリを親に持つカテゴリは、自分も削除指定されていなければならない
            if (in_array($value->parent_id, $deleteId) && ! in_array($value->id, $deleteId)){
                $isAllRequired = false;
                break;
            }
        }

        return $isAllRequired;
    }

    /**
     * カテゴリ削除の実行
     *
     * @param  array   削除用配列
     * @return boolean 最後まで処理完了したときtrue
     */
    public function setCategoryDelete($request) {
        if (empty($request['app_id'])) {
            return false;
        }

        // 1件のリファレンスでしか使われていない文言を取得
        $onlyInquiryCategoryId = [];
        foreach ($this->inquiryCategoryRef->getOnlyInquiryCategoryId() as $key => $value) {
            $onlyInquiryCategoryId[] = (integer)$value->inquiry_category_id;
        }

        FreegameDeveloper::beginTransaction();
        try {
            $deleteRequestId = $this->pregmatchDeleteRequest($request);
            $this->inquiryCategoryRef->delete($deleteRequestId);

            $intersectId = array_intersect($deleteRequestId , $onlyInquiryCategoryId );
            if (! empty($intersectId)) {
                // 文言を使用しているリファレンスが存在しないので削除する。
                $this->inquiryCategory->deleteById( $intersectId );
            }

            FreegameDeveloper::commit();

        } catch (Exception $e) {
            FreegameDeveloper::rollback();
            throw $e;

            return false;
        }

        return true;
    }

    /**
     * 入力された1次配列がすべてemptyかを判定
     *
     * @param  array   判定したい1次配列
     * @return boolean 値すべてがemptyの時true
     */
    public function isArrayOfAllEmpty($array) {
        if ( empty($array) ) {
            return true;
        }

        $isEmpty = true;
        foreach ($array as $v) {
            if (! empty($v)) {
                $isEmpty = false;
                break;
            }
        }
        return $isEmpty;
    }

}