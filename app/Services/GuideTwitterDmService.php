<?php

namespace App\Services;

use App\Models\FreegameGuide\GuideApplication;
use App\Models\FreegameDeveloper\DeveloperGuideApplication;
use App\Models\FreegameGuide\GuideTwitterDmSchedule;
use \Exception;

class GuideTwitterDmService extends CustomService
{
    protected $guideApp;
    protected $devGuideApp;
    protected $guideTwitterDmSchedule;

    public function __construct(
        GuideApplication $guideApp,
        DeveloperGuideApplication $devGuideApp,
        GuideTwitterDmSchedule $guideTwitterDmSchedule
    ) {
        $this->guideApp = $guideApp;
        $this->devGuideApp = $devGuideApp;
        $this->guideTwitterDmSchedule = $guideTwitterDmSchedule;
    }

    /**
     * Get guide_twitter_dm_schedule
     * @param  array $condition
     * @return array
     */
    public function getList($guideAppId, $condition)
    {
        $condition['guideAppId'] = $guideAppId;
        if (isset($condition['perPage']) === false) {
            $condition['perPage'] = config('forms.GuideTwitterDm.perPage');
        }

        // ---------
        // DB
        $scheduleLists = $this->guideTwitterDmSchedule->getList($condition);
        // ---------

        // ---------
        // Pagination Options
        if (empty($scheduleLists) === false) {
            // urlに検索条件を加える
            $appends = array();
            if (empty($condition['searchWord']) === false) {
                $appends['searchWord'] = $condition['searchWord'];
            }
            if (empty($condition['searchPriority']) === false) {
                $appends['searchPriority'] = $condition['searchPriority'];
            }
            $appends['perPage'] = $condition['perPage'];
            $scheduleLists->appends($appends);
        }
        // ---------

        // ---------
        // result
        return $scheduleLists;
    }

    /**
     * Get guide_twitter_dm_schedule by id
     * @param  integer $id
     * @param  integer $guideAppId
     * @return array
     */
    public function getOneById($id, $guideAppId)
    {
        $getList = $this->guideTwitterDmSchedule->getOneById($id, $guideAppId);
        return empty($getList) ? $getList : $getList->toArray();
    }

    /**
     * Get guide_application by id
     * @param  integer $id
     * @return array
     */
    public function getOneGuideApplication($id)
    {
        // ---------
        // DB
        $guideApplication = $this->guideApp->getOne($id);
        // ---------
        return empty($guideApplication) ? $guideApplication : $guideApplication->toArray();
    }

    /**
     * Insert guide_twitter_dm_schedule to database
     * @param  array $request
     * @return boolean
     */
    public function create($request)
    {
        // Request で値はチェック済み
        $newDmSchedule = [
            'guide_application_id' => $request['guideAppId'],
            'body'       => $request['body'],
            'send_at'    => $request['send_at'],
            'updated_at' => timestamp_to_sqldate(now_stamp()),
            'created_at' => timestamp_to_sqldate(now_stamp()),
        ];
        // ---------
        // DB
        $result = $this->guideTwitterDmSchedule->insert($newDmSchedule);
        // ---------

        return $result;
    }

    /**
     * Update guide_twitter_dm_schedule to database
     * @param  array $request
     * @return boolean
     */
    public function edit($request)
    {
        // ---------
        // DB
        $oldDmSchedule = $this->getOneById($request['id'], $request['guideAppId']);
        // ---------
        if (empty($oldDmSchedule)) {
            return false;
        }

        // Request で値はチェック済み
        $editDmSchedule = [
            'body'       => $request['body'],
            'send_at'    => $request['send_at'],
            'updated_at' => timestamp_to_sqldate(now_stamp()),
        ];
        // ---------
        // DB
        $result = $this->guideTwitterDmSchedule->edit($editDmSchedule, $request['id']);
        // ---------

        return $result;
    }

    /**
     * Delete content
     * @param  integer $id
     * @param  integer $guideAppId
     * @return boolean
     */
    public function deleteContent($id, $guideAppId)
    {
        // ---------
        // DB
        $oldDmSchedule = $this->getOneById($id, $guideAppId);
        // ---------
        if (empty($oldDmSchedule)) {
            return false;
        }

        // ---------
        // DB
        $this->guideTwitterDmSchedule->del($id);
        // ---------

        return true;
    }

    /**
     * save And get search condition
     * @return boolean
     */
    public function formatSearchCondition($search = [])
    {
        if (request()->has('search')) {
            $search = session('GuideTwitterDm.search', []);
            request()->merge($search);
        }
        $params = array_only($search, [
            'perPage',
            'page'
        ]);
        $search = array_filter($params, function ($item) {
            return is_array($item) || $item || is_numeric($item);
        });
        request()->session()->set('GuideTwitterDm.search', $search);
        return $search;
    }

    /**
     * Check Edit Permission
     * @return boolean
     */
    public function isEnableEdit($guideAppId)
    {
        $userId = auth_user_id();
        if (empty($userId)) {
            return false;
        }
        if (auth_is_pf()) {
            return true;
        } else {
            $idList = $this->devGuideApp->getListGuideAppIdByDevId($userId);
            if (in_array($guideAppId, $idList->toArray())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get values
     *
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.GuideTwitterDm');

        return [
            'screenName'  => $privateConfigs['screenName'],
            'menuName'  => $privateConfigs['menuName'],
            'subScreenName'  => $privateConfigs['subScreenName'],
        ];
    }
}
