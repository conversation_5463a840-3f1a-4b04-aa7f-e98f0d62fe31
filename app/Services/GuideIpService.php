<?php

namespace App\Services;

use App\Models\FreegameGuide\GuideApplication;
use App\Models\FreegameDeveloper\DeveloperGuideApplication;
use App\Models\FreegameGuide\GuideOuterIp;
use \Exception;

class GuideIpService extends CustomService
{
    protected $guideApp;
    protected $devGuideApp;
    protected $guideOuterIp;

    public function __construct(
        GuideApplication $guideApp,
        DeveloperGuideApplication $devGuideApp,
        GuideOuterIp $guideOuterIp
    ) {
        $this->guideApp = $guideApp;
        $this->devGuideApp = $devGuideApp;
        $this->guideOuterIp = $guideOuterIp;
    }

    /**
     * Get Outer IP List
     * @param  integer $guideAppId
     * @return array
     */
    public function getListOuterIp($guideAppId)
    {
        $getList = $this->guideOuterIp->getListByGuideAppId($guideAppId);

        return $getList->toArray();
    }

    /**
     * Get Outer IP by id
     * @param  integer $id
     * @param  integer $guideAppId
     * @return array
     */
    public function getOneOuterIp($id, $guideAppId)
    {
        $getList = $this->guideOuterIp->getOneByid($id, $guideAppId);
        return empty($getList) ? $getList : $getList->toArray();
    }

    /**
     * Get guide_application by id
     * @param  integer $id
     * @return array
     */
    public function getOneGuideApplication($id)
    {
        // ---------
        // DB
        $guideApplication = $this->guideApp->getOne($id);
        // ---------
        return empty($guideApplication) ? $guideApplication : $guideApplication->toArray();
    }

    /**
     * Insert guide_outer_ip to database
     * @param  array $request
     * @return boolean
     */
    public function create($request)
    {
        $ipAddress = $request['address1']
            . '.' . $request['address2']
            . '.' . $request['address3']
            . '.' . $request['address4'];
        // Request で値はチェック済み
        $newGuideOtherIp = [
            'guide_application_id' => $request['guideAppId'],
            'name'                 => $request['name'],
            'ip_address'           => $ipAddress,
            'stamp'                => timestamp_to_sqldate(now_stamp()),
        ];
        // ---------
        // DB
        $result = $this->guideOuterIp->createOuterIp($newGuideOtherIp);
        // ---------

        return $result;
    }

    /**
     * Update guide_outer_ip to database
     * @param  array $request
     * @return boolean
     */
    public function edit($request)
    {
        // ---------
        // DB
        $oldGuideOtherIp = $this->getOneOuterIp($request['id'], $request['guideAppId']);
        // ---------
        if (empty($oldGuideOtherIp)) {
            return false;
        }

        $ipAddress = $request['address1']
            . '.' . $request['address2']
            . '.' . $request['address3']
            . '.' . $request['address4'];
        // Request で値はチェック済み
        $editGuideOtherIp = [
            'guide_application_id' => $request['guideAppId'],
            'name'                 => $request['name'],
            'ip_address'           => $ipAddress,
            'stamp'                => timestamp_to_sqldate(now_stamp()),
        ];
        // ---------
        // DB
        $result = $this->guideOuterIp->edit($editGuideOtherIp, $request['id']);
        // ---------

        return $result;
    }

    /**
     * Delete content
     * @param  integer $id
     * @param  integer $guideAppId
     * @return boolean
     */
    public function deleteContent($id, $guideAppId)
    {
        // ---------
        // DB
        $oldGuideOtherIp = $this->getOneOuterIp($id, $guideAppId);
        // ---------
        if (empty($oldGuideOtherIp)) {
            return false;
        }

        // ---------
        // DB
        $this->guideOuterIp->del($id);
        // ---------
        return true;
    }

    /**
     * Check Edit Permission
     * @return boolean
     */
    public function isEnableEdit($guideAppId)
    {
        $userId = auth_user_id();
        if (empty($userId)) {
            return false;
        }
        if (auth_is_pf()) {
            return true;
        } else {
            $idList = $this->devGuideApp->getListGuideAppIdByDevId($userId);
            if (in_array($guideAppId, $idList->toArray())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get values
     *
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.GuideIp');

        return [
            'screenName'  => $privateConfigs['screenName'],
            'menuName'  => $privateConfigs['menuName'],
        ];
    }
}
