<?php

namespace App\Services;

use \Exception;
use Cache;
use Flysystem;
use Log;

use App\Models\FreegameSandbox\FreegameSandbox;
use App\Models\FreegameDeveloper\DeveloperParentage;
use App\Models\FreegameSandbox\Application;
use App\Models\FreegameSandbox\ApplicationDevice;
use App\Models\FreegameSandbox\ApplicationApk;
use App\Models\FreegameSandbox\ApplicationProtocol;
use App\Models\FreegameSandbox\ApplicationSpMenu;

class SbxApplicationsService extends CustomService
{
    protected $developerParentage;

    public function __construct(
        DeveloperParentage $developerParentage,
        Application $application,
        ApplicationDevice $applicationDevice,
        ApplicationApk $applicationApk,
        ApplicationProtocol $applicationProtocol,
        ApplicationSpMenu $applicationSpMenu
    ) {
        $this->developerParentage = $developerParentage;
        $this->application = $application;
        $this->applicationDevice = $applicationDevice;
        $this->applicationApk = $applicationApk;
        $this->applicationProtocol = $applicationProtocol;
        $this->applicationSpMenu = $applicationSpMenu;
    }

    protected $consumerKeyLength = 16;
    protected $consumerKeyChr = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    protected $consumerSecretLength = 32;
    protected $consumerSecretChr = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789#$-=?@[]_';
    protected $cacheKeyPrefix = 'sandbox_gadget_xml_';
    /**
     * app_idを取得(同じapp_idが存在しないことを確認)<br>
     * @return int uid
     */
    public function getAppId()
    {
        do {
            $app_id = mt_rand(1, 999999);
        } while (!empty($this->application->getOne($app_id)));

        return $app_id;
    }

    /**
     * consumaer_keyを取得(同じconsumaer_keyが存在しないことを確認)<br>
     * @return string consumaer_key
     */
    public function getConsumerKey()
    {
        do {
            $range = strlen($this->consumerKeyChr) - 1;
            $consumer_key = null;
            while ($this->consumerKeyLength > strlen($consumer_key)) {
                $consumer_key .= $this->consumerKeyChr[rand(0, $range)];
            }
        } while (!empty($this->application->getOneByConsumerKey($consumer_key)));

        return $consumer_key;
    }

    /**
     * consumaer_secretを取得
     * @return string consumaer_secret
     */
    public function getConsumerSecret()
    {
        $range = strlen($this->consumerSecretChr) - 1;
        $consumer_secret = null;
        while ($this->consumerSecretLength > strlen($consumer_secret)) {
            $consumer_secret .= $this->consumerSecretChr[rand(0, $range)];
        }
        return $consumer_secret;
    }

    /**
     * Get values for form select box,radio box
     *
     * @return array
     */
    public function getFormData()
    {
        return config('forms.SbxApplications');
    }

    /**
     * Get sbx user list
     * @param array $conditions
     * @return array
     */
    public function getSbxApplicationList($conditions)
    {
        // 表示件数の取得
        $offset = config('forms.SbxApplications.offset');
        if (! empty($conditions['perPage'])) {
            $offset = $conditions['perPage'];
        }

        $page = 1;
        if (! empty($conditions['page'])) {
            $page = $conditions['page'];
        }

        $developerIdList = [auth_user_id()];
        // admin以外の場合子デベロッパid取得
        if (! auth_is_user_admin()) {
            $sbxChildrenList = $this->developerParentage->getDeveloperIdList(auth_user_id());
            foreach ($sbxChildrenList as $id) {
                $developerIdList[] = $id;
            }
        }
        // デベロッパidからアプリデータを取得
        $sbxAppList = $this->application->getList($developerIdList, $offset);

        // app_idからデバイスと、application_apkの存在するか否かを取得
        foreach ($sbxAppList as &$app) {
            $app['deviceList'] = $this->getDeviceList($app['id']);
            $app['isApkRegist'] = ! empty($this->applicationApk->getOneByAppId($app['id'])) ? true : false;
        }

        // 表示件数の設定
        $sbxAppList->appends([
            'perPage' => $sbxAppList->perPage(),
            'page' => $page
        ]);
        return $sbxAppList;
    }

    /**
     * app_idからデバイスのリストを取得
     * @param int app_id
     * @return array $deviceList
     */
    public function getDeviceList($app_id)
    {
        $deviceArray = $this->applicationDevice->getListByAppId($app_id, 'active');
        $deviceList = [];
        foreach ($deviceArray as $device) {
            switch ($device['device']) {
                case 'mobile':
                    $deviceList['mobile'] = 'mobile';
                    break;
                case 'sp':
                    $deviceList['sp'] = 'sp';
                    break;
                case 'pc':
                    $deviceList['pc'] = 'pc';
                    break;
                case 'android_app':
                    $deviceList['android_app'] = 'android_app';
                    break;
                case 'emulator':
                    $deviceList['emulator'] = 'emulator';
                    break;
            }
        }

        $sortDeviceList = array_keys(config('forms.SbxApplications')['device']);
        $deviceList = array_sort($deviceList, function ($value) use ($sortDeviceList) {
            return array_search($value, $sortDeviceList);
        });
        return $deviceList;
    }

    /**
     * app_idからドメインタイプ / レシート課金対応を取得
     * @param int app_id
     * @return int $domainType
     */
    public function getDomainTypeAndBrowserSdk($app_id)
    {
        $deviceArray = $this->applicationDevice->getListByAppId($app_id, 'active');

        if (empty($deviceArray)) {
            return null;
        }

        return ['domainType' => $deviceArray[0]['domain_type'], 'browserSdk' => $deviceArray[0]['browser_sdk']];
    }

    /**
     * サンドボックステストゲーム登録
     *
     * @param array $request
     * @throws Exception
     * @return int
     */
    public function storeApplication($request)
    {
        if (!$this->isEnableDomainType()) {
            $request['domain_type'] = 0;
        }
        
        // 登録するデータを取得、生成
        // application
        $app['id'] = $this->getAppId();
        $app['developer_id'] = auth_user_id();
        $app['url'] = $request['url'];
        $app['consumer_key'] = $this->getConsumerKey();
        $app['consumer_secret'] = $this->getConsumerSecret();
        $app['title'] = $request['title'];
        if ($request['type'] == 'general') {
            $app['general'] = 1;
            $app['adult'] = 0;
        } else {
            $app['general'] = 0;
            $app['adult'] = 1;
        }

        // application_device
        $deviceList = array_unique($request['deviceList']);
        $appDevice['app_id'] = $app['id'];
        $appDevice['begin'] = date('Y-m-d H:i:s');
        $appDevice['end'] = '2038-01-01 10:00:00';
        $appDevice['status'] = 'active';
        $appDevice['domain_type'] = $request['domain_type'];
        $appDevice['browser_sdk'] = $request['browser_sdk'];

        // application_protocol
        if (in_array('pc', $deviceList) || in_array('sp', $deviceList)) {
            $appProtocol['app_id'] = $app['id'];
            $appProtocol['is_ssl'] = $request['is_ssl'];
        }

        // application_sp_menu
        if (in_array('sp', $deviceList)) {
            $appSpMenu['app_id'] = $app['id'];
            if (!empty($request['spLayout']) && $request['spLayout'] == 2) {
                $appSpMenu['portrait_position'] = $request['menu_position_portrait'];
                $appSpMenu['landscape_position'] = $request['menu_position_landscape'];
            } else {
                $appSpMenu['portrait_position'] = 0;
                $appSpMenu['landscape_position'] = 0;
            }
        }

        // application_apk
        // 対応デバイスにAndroidAppだけチェックされている場合のみAPKファイルアップロード可
        if (count($deviceList) === 1 && in_array('android_app', $deviceList)) {
            // 対応デバイスにAndroidAppだけチェックされている場合、APKファイルがアップロードできますが、
            // 新サンドボックスはAPKファイルがダウンロードできないので、旧サンドボックスに設定
            $appDevice['domain_type'] = 0;

            if ($request['require_apk_file'] == 1) {
                $domain = $request['type'] == 'general' ? 'com' : 'co.jp';
                $apk['app_id'] = $app['id'];
                $apk['apk_name'] = sprintf(
                    '%s.dmm.dmmlabo.%s.id%s.apk',
                    $domain,
                    $request['package_name'],
                    $app['id']
                );
                $apk['package_name'] = $request['package_name'];
            }else{
                $apk['app_id'] = $app['id'];
                $apk['apk_name'] = "";
                $apk['package_name'] = "";
            }
        }

        // 登録(トランザクション)開始
        FreegameSandbox::beginTransaction();
        try {
            // application
            $this->application->add($app);

            // application_device
            foreach ($deviceList as $device) {
                $appDevice['device'] = $device;
                $this->applicationDevice->add($appDevice);
            }

            // application_apk
            if (! empty($apk)) {
                $this->applicationApk->add($apk);
            }

            // application_protocol
            if(!empty($appProtocol)) {
                $this->applicationProtocol->add($appProtocol);
            }

            // application_sp_menu
            if(!empty($appSpMenu)) {
                $this->applicationSpMenu->add($appSpMenu);
            }

            // 登録(トランザクション)終了
            FreegameSandbox::commit();
        } catch (Exception $e) {
            // ロールバック
            FreegameSandbox::rollback();
            throw $e;
        }

        return $app['id'];
    }

    /**
     * app_idからアプリ&デバイスとapkのデータを取得
     * @param int $uid
     * @return array
     */
    public function getApplication($app_id)
    {
        $app = $this->application->getOne($app_id);
        $app['deviceList']  = $this->getDeviceList($app_id);
        $domainTypeAndBrowserSdk  = $this->getDomainTypeAndBrowserSdk($app_id);
        $app['domain_type']  = $domainTypeAndBrowserSdk['domainType'];
        $app['browser_sdk']  = $domainTypeAndBrowserSdk['browserSdk'];

        // デバイスにandroidがある場合apkの情報を取得
        if (in_array('android_app', $app['deviceList'])) {
            $apk = $this->applicationApk->getOneByAppId($app_id);
            $app['file_size']    = $apk['file_size'];
            $app['package_name'] = $apk['package_name'];
            $app['apk_name']     = $apk['apk_name'];
        }

        // デバイスにPCまたはSPがある場合 テストゲームSSL対応情報を取得
        if (in_array('pc', $app['deviceList']) || in_array('sp', $app['deviceList'])) {
            $appProtocol = $this->applicationProtocol->getOneByAppId($app_id);
            if (empty($appProtocol['is_ssl'])) {
                $app['is_ssl'] = 0;
            } else {
                $app['is_ssl'] = $appProtocol['is_ssl'];
            }
        }

        // SPレイアウトとメニューボタン位置はデバイスに依らずデフォルト値(従来画面設定・type2初期値)を持たせる
        $app['spLayout'] = 1;
        $app['menu_position_portrait']  = 4;  // 縦向き初期位置
        $app['menu_position_landscape'] = 4; // 横向き初期位置
        if (in_array('sp', $app['deviceList'])) {
            // デバイスにSPがある場合 SPメニューボタン位置情報のDB値取得を試みてtype2該当時のみ置き換える
            $appSpMenu = $this->applicationSpMenu->getByAppId($app_id);
            if (!empty($appSpMenu) && $appSpMenu['portrait_position'] != 0 && $appSpMenu['landscape_position'] != 0) {
                $app['spLayout'] = 2;
                $app['menu_position_portrait'] = $appSpMenu['portrait_position'];
                $app['menu_position_landscape'] = $appSpMenu['landscape_position'];
            }
        }

        return $app;
    }

    /**
     * サンドボックステストゲーム変更
     * @param array $request
     * @throws Exception
     * @return void
     */
    public function updateApplication($request)
    {
        if (!$this->isEnableDomainType()) {
            $request['domain_type'] = 0;
        }

        $app_id = $request['id'];
        $deviceList = array_unique($request['deviceList']);

        // 更新(トランザクション)開始
        FreegameSandbox::beginTransaction();
        try {
            // application
            $this->application->edit(
                [
                    'url'   => $request['url'],
                    'title' => $request['title'],
                ],
                $app_id
            );

            // application_protocol
            if (in_array('pc', $deviceList) || in_array('sp', $deviceList)) {
                $appProtocol = [
                    'app_id' => $request['id'],
                    'is_ssl' => $request['is_ssl'],
                ];
                $this->applicationProtocol->edit($appProtocol);
            }

            // application_sp_menu
            if (in_array('sp', $deviceList)) {
                $appSpMenu = [
                    'app_id' => $request['id'],
                    'portrait_position' => $request['menu_position_portrait'],
                    'landscape_position' => $request['menu_position_landscape'],
                ];
                $this->applicationSpMenu->edit($appSpMenu);
            }

            $allDeviceList = ['mobile', 'sp', 'pc', 'android_app', 'emulator'];
            foreach ($allDeviceList as $device) {
                // application_device
                $storeDevice = $this->applicationDevice->getOnetByAppIdAndDevice($app_id, $device);
                if (empty($storeDevice)) {
                    if (in_array($device, $deviceList)) {
                        // 未登録の場合
                        $this->applicationDevice->add(
                            [
                                'app_id' => $app_id,
                                'device' => $device,
                                'begin' => date('Y-m-d H:i:s'),
                                'end' => '2038-01-01 10:00:00',
                                'status' => 'active',
                                'domain_type' => $request['domain_type'],
                                'browser_sdk' => $request['browser_sdk'],
                            ]
                        );
                    }
                } else {
                    $updateDevice = [];

                    if ($storeDevice['status'] == 'active' && ! in_array($device, $deviceList)) {
                        // active状態から無効状態(デバイス削除)にする場合
                        $updateDevice = [
                            'end' => date('Y-m-d H:i:s'),
                            'status' => 'suspend',
                        ];
                    } elseif ($storeDevice['status'] != 'active' && in_array($device, $deviceList)) {
                        // suspend状態からactiveに復活する場合
                        $updateDevice = [
                            'begin' => date('Y-m-d H:i:s'),
                            'end' => '2038-01-01 10:00:00',
                            'status' => 'active',
                        ];
                    }

                    if (count($deviceList) === 1 && in_array('android_app', $deviceList)) {
                        // 対応デバイスにAndroidAppだけチェックされている場合、APKファイルがアップロードできますが、
                        // 新サンドボックスはAPKファイルがダウンロードできないので、旧サンドボックスに設定
                        $updateDevice['domain_type'] = 0;
                    } else if ($storeDevice['domain_type'] != $request['domain_type']) {
                        $updateDevice['domain_type'] = $request['domain_type'];
                    }

                    if ($storeDevice['browser_sdk'] != $request['browser_sdk']) {
                        $updateDevice['browser_sdk'] = $request['browser_sdk'];
                    }

                    if (!empty($updateDevice)) {
                        $this->applicationDevice->edit(
                            $updateDevice,
                            $app_id,
                            $device
                        );
                    }    
                }

                // application_apk
                // 対応デバイスにAndroidAppだけチェックされている場合のみAPKファイルアップロード可
                if (count($deviceList) === 1 && $device == 'android_app' && in_array($device, $deviceList)) {
                    if ($request['require_apk_file'] == 1) {
                        // general or adultの判別のためにapplicationテーブルのデータ取得
                        $app = $this->application->getOne($app_id);
                        $domain = $app['general'] == 1 ? 'com' : 'co.jp';
                        $apk['apk_name'] = sprintf(
                            '%s.dmm.dmmlabo.%s.id%s.apk',
                            $domain,
                            $request['package_name'],
                            $app_id
                        );
                        $apk['package_name'] = $request['package_name'];
                    }else{
                        $apk['apk_name'] = "";
                        $apk['package_name'] = "";
                    }

                    // application_apkの登録有無を確認
                    $storeApk = $this->applicationApk->getOneByAppId($app_id);
                    if (empty($storeApk)) {
                        // 既存の登録がない場合は登録
                        $apk['app_id'] = $app_id;
                        $this->applicationApk->add($apk);
                    } elseif (! empty($storeApk)) {
                        // 既存の登録がある場合は変更
                        $this->applicationApk->edit($apk, $app_id);
                    }
                }
            }

            // 更新(トランザクション)終了
            FreegameSandbox::commit();
        } catch (Exception $e) {
            // ロールバック
            FreegameSandbox::rollback();
            throw $e;
        }
    }
    /**
     * アプリのdeveloper_idが、利用中のアカウント
     * もしくは子アカウントであるか
     * @param int $app_id
     * @return boolean
     */
    public function isDeveloper($app_id)
    {
        $app = $this->application->getOne($app_id);
        $developerList = $this->developerParentage->getDeveloperIdList(auth_user_id());
        $developerList[auth_user_id()] = auth_user_id();
        foreach ($developerList as $developer_id) {
            if ($developer_id == $app['developer_id']) {
                return true;
            }
        }

         return false;
    }

    /**
     * サンドボックステストゲーム削除
     *
     * @return boolean
     */
    public function deleteApplication($app_id)
    {
        // デバイス取得
        $deviceList = $this->applicationDevice->getListByAppId($app_id, 'active');

        // 更新(トランザクション)開始
        FreegameSandbox::beginTransaction();
        try {
            foreach ($deviceList as $device) {
                $this->applicationDevice->edit(
                    [
                        'end' => date('Y-m-d H:i:s'),
                        'status' => 'suspend',
                    ],
                    $app_id,
                    $device['device']
                );
            }
            // 更新(トランザクション)終了
            FreegameSandbox::commit();
        } catch (Exception $e) {
            // ロールバック
            FreegameSandbox::rollback();
            throw $e;
        }
        return true;
    }

    /**
     * apkファイルアップロード
     *
     * @return boolean
     */
    public function apkFileUpdate($data, $id = 0)
    {
        if(empty($id)){
            $id = $data['id'];
        }

        if (! $this->isDeveloper($id)) {
            return ['apk_file' => config('forms.SbxApplications')['devIdErrorMessage']];
        }

        $one = $this->getApk($id);
        if (! $one) {
            return false;
        }

        // APKファイル名
        $app = $this->application->getOne($id);
        $domain = $app['general'] == 1 ? 'com' : 'co.jp';
        $data['apk_name'] = sprintf(
            '%s.dmm.dmmlabo.%s.id%s.apk',
            $domain,
            $data['package_name'],
            $id
        );

        // apk_nameの変更があるか確認する
        $apk_name = $one['apk_name'];
        if (!empty($apk_name) && $data['apk_name'] != $apk_name) {
            // apk_nameに変更がある場合は、S3にアップロードしてあるAPKファイルをリネーム(move)する
            $result = $this->apkFileRename($apk_name, $data['apk_name']);
            if (is_array($result)) {
                return $result;
            }
        }

        // APKファイル移動
        $result = $this->apkFileMove($data);
        if (is_array($result) || is_bool($result)) {
            return $result;
        }

        // APKファイルアップロード
        $result = $this->apkFileUpload($data);
        if (is_array($result) || is_bool($result)) {
            return $result;
        }
    }

    /**
     * apkのデータを取得
     *
     * @return boolean
     */
    public function getApk($id)
    {
        if (empty($id)) {
            return false;
        }

        // applicationテーブルの存在チェックとデータ取得
        $app = $this->application->getOne($id);
        if (empty($app->exists)) {
            return false;
        }

        // application_apkテーブルの存在チェックとデータ取得
        $apk = $this->applicationApk->getOneByAppId($id);
        if (empty($apk->exists)) {
            return false;
        }
        return $apk;
    }

    public function getApkFileBasePath()
    {
        return rtrim(env('DL_APP_SANDBOX_BASEPATH', '/public/android'), '/');
    }

    public function getApkFileTmpPath()
    {
        return rtrim(env('DL_APP_SANDBOX_TEMPORARYPATH', '/tmp/tmpuploadapk'), '/')
            . '/' . request()->session()->getId();
    }

    //　移動
    public function apkFileMove($data)
    {
        if (empty($data['apk_file']) || ! is_object($data['apk_file'])) {
            return true;
        }
        if (empty($data['apk_name'])) {
            return true;
        }
        // 一時ファイル削除
        $this->apkFileTmpClear();

        // APKファイル移動
        $tempPath = $this->getApkFileTmpPath();
        $fileName = $data['apk_name'];
        try {
            if (! $data['apk_file']->isValid()) {
                throw new Exception('File Not Found');
            }

            // 一時ファイルを保存するディレクトリがない場合、ディレクトリを作成
            if (!\File::exists($tempPath)) {
                \File::makeDirectory($tempPath, 0777, true, true);
            }

            $data['apk_file']->move($tempPath, $fileName);
        } catch (Exception $e) {
            Log::error('apkFileMoveError: ' . $e->getMessage());
            return [
                'apk_file' => 'APKの一時保存に失敗しました。'
            ];
        }

        request()->merge([
            'apk_name' => $fileName
        ]);

        return $fileName;
    }

    public function apkFileUpload($data)
    {
        if (empty($data['apk_name'])) {
            return true;
        }
        // APKアップロード
        $fileName = $data['apk_name'];
        $tempPath = $this->getApkFileTmpPath();
        $basePath = $this->getApkFileBasePath();
        $fromFilePath = $tempPath . '/' . $fileName;
        $toFilePath = $basePath . '/' . $fileName;
        try {
            $fileSystem = \Storage::disk('dl_app_sandbox_netgame_s3');

            $stream = fopen($fromFilePath, 'r');
            $fileSystem->put($toFilePath, $stream);
            @fclose($stream);

            if (! $fileSystem->has($toFilePath)) {
                throw new Exception('File Not Found');
            }
        } catch (Exception $e) {
            Log::error('apkFileUploadError: ' . $e->getMessage());
            return [
                'apk_file' => 'APKのアップロードに失敗しました。'
            ];
        }
        // 一時ファイル削除
        $this->apkFileTmpClear();
        return $fileName;
    }

    /**
     * S3にアップロードされているファイルをリネームする
     * 
     * @param $fromFileName 元ファイル名
     * @param $toFileName 新しいファイル名
     */
    public function apkFileRename($fromFileName, $toFileName)
    {
        $basePath = $this->getApkFileBasePath();
        $fromFilePath = $basePath . '/' . $fromFileName;
        $toFilePath = $basePath . '/' . $toFileName;
        try {
            $fileSystem = \Storage::disk('dl_app_sandbox_netgame_s3');

            // 新しいファイル名のファイルが既にある場合は、moveができないので、deleteする
            if ($fileSystem->has($toFilePath)) {
                $fileSystem->delete($toFilePath);
            }

            // 元ファイルのファイルがない場合は、moveの必要がないので、returnする
            if (!$fileSystem->has($fromFilePath)) {
                return;
            }

            // ファイルのリネーム(move)する
            $fileSystem->move($fromFilePath, $toFilePath);

            if (! $fileSystem->has($toFilePath)) {
                throw new Exception('Could not move files.');
            }
        } catch (Exception $e) {
            Log::error('apkFileRenameError: ' . $e->getMessage());
            return [
                'apk_file' => 'APKのリネームに失敗しました。'
            ];
        }
        return;
    }

    public function apkFileTmpClear($dir = null)
    {
        // 一時ファイル削除
        if (is_null($dir)) {
            $dir = $this->getApkFileTmpPath();
        }
        foreach (glob(rtrim($dir, '/') . '/*') as $path) {
            if (is_dir($path)) {
                $this->apkFileTmpClear($path);
            } else {
                @unlink($path);
            }
        }
        @rmdir($dir);
        return true;
    }

    /**
     * 検索条件セッション保持
     * @param array $search
     * @return array
     */
    public function formatSearchCondition($search = [])
    {
        if (request()->has('search')) {
            $search = session('SbxApplications.search', []);
            request()->merge($search);
        }
        $search = array_only($search, [
            'perPage',
            'page'
        ]);
        request()->session()->set('SbxApplications.search', $search);
        return $search;
    }

    /**
     * ドメインタイプの指定が有効かを返す。
     * @return boolean ドメインタイプの指定が有効か
     */
    public function isEnableDomainType()
    {
        $developerIds = config('forms.SbxApplications.enableDomainTypeDeveloperIds');

        if (empty($developerIds)) {
            // 設定自体がない場合は全開放
            return true;
        }

        return in_array(auth()->user()->id, explode(',', $developerIds));
    }
}
