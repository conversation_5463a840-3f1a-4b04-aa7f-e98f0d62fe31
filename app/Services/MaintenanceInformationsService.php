<?php
namespace App\Services;

use Carbon\Carbon;
use App\Models\FreegameDeveloper\MaintenanceInformation;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Services\Accessory\InformationFormat;

class MaintenanceInformationsService extends CustomService
{
    use InformationFormat;

    protected $maintenanceInformation;

    public function __construct(MaintenanceInformation $maintenanceInformation)
    {
        $this->maintenanceInformation = $maintenanceInformation;
    }

    /**
     * Get form data
     * @return array
     */
    public function getFormData()
    {
        return [
            'menuName' => config('forms.MaintenanceInformations.menuName'),
            'screenName' => config('forms.MaintenanceInformations.screenName'),
            'categoryType' => config('forms.MaintenanceInformations.categoryType'),
        ];
    }

    /**
     * Get list by condition list
     * @param array $condition
     * @param boolean $isPagination
     * @return array
     */
    public function getList($condition = [], $isPagination = true)
    {
        if ($isPagination) {
            if (empty($condition['perPage'])) {
                $condition['perPage'] = config('forms.MaintenanceInformations.perPage');
            }
        } else {
            $condition['take'] = config('forms.MaintenanceInformations.topTake');
        }

        $condition['today'] = Carbon::now()->format('Y-m-d');

        $paginator = $this->maintenanceInformation->getList($condition);

        $paginator = $this->formatMaintenanceList($paginator);
        $this->paginatorAppend($paginator, $condition);

        return $paginator;
    }

    /**
     * Paginator append search conditions
     * @param Illuminate\Pagination\LengthAwarePaginator $paginator
     * @param array $condition
     */
    public function paginatorAppend($paginator, $condition = [])
    {
        if (! $paginator instanceof LengthAwarePaginator) {
            return;
        }

        $paginator->appends([
            'perPage' => $paginator->perPage()
        ]);

        if (isset($condition['keyword'])) {
            $paginator->appends([
                'keyword' => $condition['keyword'],
            ]);
        }
    }

    /**
     * Get data by ID
     * @param integer $id
     * @return object
     */
    public function getData($id)
    {
        if (empty($id)) {
            return false;
        }

        return $this->maintenanceInformation->getOne($id);
    }

    /**
     * Format search condition
     * @param \Illuminate\Http\Request
     * @return array
     */
    public function formatSearchCondition($search = [])
    {
        $sessionName = 'MaintenanceInformations.maintenancelist.searchform';

        if (request()->has('search')) {
            $search = session($sessionName, []);
            request()->merge($search);
        }

        if (isset($search['keyword'])) {
            $search['keyword'] = $this->getReplaceKeyword($search['keyword']);
        }

        request()->session()->set($sessionName, $search);

        if (isset($search['keyword'])) {
            $search['keywordArray'] = explode(' ', $search['keyword']);
        }

        return $search;
    }

    /**
     * Get replace keyword
     * @param string
     * @return string|null
     */
    public function getReplaceKeyword($keyword)
    {
        if (isset($keyword)) {
            $keyword = preg_replace('/　/', ' ', $keyword);
            $keyword = trim($keyword);
            $keyword = preg_replace('/\s+/', ' ', $keyword);
        }

        if ($keyword == '' || $keyword == ' ') {
            $keyword = null;
        }

        return $keyword;
    }
}
