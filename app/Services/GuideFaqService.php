<?php
namespace App\Services;

use App\Models\FreegameGuide\GuideApplication;
use App\Models\FreegameDeveloper\DeveloperGuideApplication;
use App\Models\FreegameGuide\GuideFaq;
use App\Models\FreegameGuide\GuideFaqCategory;
use App\Models\FreegameGuide\GuideFaqCategoryGroup;
use App\Models\FreegameGuide\GuideFaqImage;
use App\Models\FreegameGuide\GuideFaqTmp;
use \Exception;
use App\Models\Freegame\Freegame;
use Log;

class GuideFaqService extends CustomService
{
    protected $guideApp;
    protected $devGuideApp;
    protected $guideFaq;
    protected $guideFaqCategory;
    protected $guideFaqCategoryGroup;
    protected $guideFaqImage;
    protected $guideFaqTmp;

    public function __construct(
        GuideApplication          $guideApp,
        DeveloperGuideApplication $devGuideApp,
        GuideFaq                  $guideFaq,
        GuideFaqCategory          $guideFaqCategory,
        GuideFaqCategoryGroup     $guideFaqCategoryGroup,
        GuideFaqImage             $guideFaqImage,
        GuideFaqTmp               $guideFaqTmp
    ) {
        $this->guideApp              = $guideApp;
        $this->devGuideApp           = $devGuideApp;
        $this->guideFaq              = $guideFaq;
        $this->guideFaqCategory      = $guideFaqCategory;
        $this->guideFaqCategoryGroup = $guideFaqCategoryGroup;
        $this->guideFaqImage         = $guideFaqImage;
        $this->guideFaqTmp           = $guideFaqTmp;
    }

    /**
     * Get guide_faq
     * @param  array $condition
     * @return array
     */
    public function getList($guideAppId, $condition)
    {
        $condition['guideAppId'] = $guideAppId;
        if (isset($condition['perPage']) === false) {
            $condition['perPage'] = config('forms.GuideFaq.perPage');
        }

        if (isset($condition['faqCategoryId']) === false) {
            $condition['faqCategoryId'] = '';
        }

        // ---------
        // DB
        $faqLists = $this->guideFaq->getList($condition);
        // ---------

        // ---------
        // Pagination Options
        if (empty($faqLists) === false) {
            // urlに検索条件を加える
            $appends = array();
            if (empty($condition['faqCategoryId']) === false) {
                $appends['faqCategoryId'] = $condition['faqCategoryId'];
            }
            $appends['guideAppId'] = $condition['guideAppId'];
            $appends['perPage'] = $condition['perPage'];
            $faqLists->appends($appends);
        }
        // ---------

        // ---------
        // result
        return $faqLists;
    }

    /**
     * Get guide_faq by id
     * @param  integer $id
     * @param  integer $guideAppId
     * @return array
     */
    public function getOneById($id, $guideAppId)
    {
        $getList = $this->guideFaq->getOneById($id, $guideAppId);
        return empty($getList) ? $getList : $getList->toArray();
    }

    /**
     * Get guide_faq_category List
     * @param  integer $guideAppId
     * @return array
     */
    public function getListFaqCategoryName($guideAppId, $isUserNone = true)
    {
        $result = array();
        if ($isUserNone) {
            $result = array('' => '未選択');
        }
        $getList = $this->guideFaqCategory->getListByGuideAppId($guideAppId);
        foreach ($getList as $faqCat) {
            $index = $faqCat['id'];
            $result[$index] = $faqCat['name'];
        }
        return $result;
    }

    /**
     * Get guide_faq_image List
     * @param  integer $faqId
     * @return array
     */
    public function getListFaqImage($faqId)
    {
        $getList = $this->guideFaqImage->getListByFaqId($faqId);
        return $getList->toArray();
    }

    /**
     * Get guide_application by id
     * @param  integer $id
     * @return array
     */
    public function getOneGuideApplication($id)
    {
        // ---------
        // DB
        $guideApplication = $this->guideApp->getOne($id);
        // ---------
        return empty($guideApplication) ? $guideApplication : $guideApplication->toArray();
    }

    /**
     * Get guide_faq by is_priority is '1'
     * @param  array $condition
     * @return array
     */
    public function getListPriority($condition = [])
    {
        return $this->guideFaq->getListPriority($condition)->toArray();
    }

    /**
     * Get guide_faq by priority_in_category is '1'
     * @param  integer $guideAppId
     * @param  integer $categoryId
     * @return array
     */
    public function getListPriorityCategory($guideAppId, $categoryId)
    {
        $getList = $this->guideFaq->getListPriorityCategory($guideAppId, $categoryId);

        return $getList->toArray();
    }

    /**
     * Insert guide_faq to database
     * @param  array $request
     * @return boolean
     */
    public function create($request)
    {

        // Request で値はチェック済み
        $newGuideFaq = [
            'guide_application_id'  => $request['guideAppId'],
            'question'              => $request['question'],
            'guide_faq_category_id' => $request['guide_faq_category_id'],
            'is_priority'           => empty($request['is_priority']) ? 0 : 1,
            'priority'              => empty($request['is_priority']) ? null : 1,
            'answer'                => $request['answer'],
            'view_status'           => $request['view_status'],
            'stamp'                 => timestamp_to_sqldate(now_stamp()),
        ];
        Freegame::beginTransaction();
        try {
            // ---------
            // DB
            if (empty($request['is_priority']) === false) {
                // 既存データのpriorityを+1
                $this->guideFaq->editCountUpPriority($request['guideAppId']);
            }
            $newId = $this->guideFaq->insertGetId($newGuideFaq);
            // ---------

            // guide_faq_idが確定したので、更新する
            $tmpImageList =
                $this->guideFaqImage->getListByWithInHour($request['guideAppId'], 0);
            if (count($tmpImageList)) {
                $idList = array();
                foreach ($tmpImageList as $image) {
                    $editFaqImage = [
                        'guide_faq_id' => $newId,
                        'stamp'   => timestamp_to_sqldate(now_stamp()),
                    ];
                    $this->guideFaqImage->edit($editFaqImage, $image['id']);
                }
            }
            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            Log::error(var_export($e->getMessage(), true));
        }

        return true;
    }

    /**
     * Update guide_faq to database
     * @param  array $request
     * @return boolean
     */
    public function edit($request)
    {
        // ---------
        // DB
        $oldGuideFaq = $this->getOneById($request['id'], $request['guideAppId']);
        // ---------
        if (empty($oldGuideFaq)) {
            return false;
        }

        // Request で値はチェック済み
        $editGuideFaq = [
            'guide_application_id'  => $request['guideAppId'],
            'question'              => $request['question'],
            'guide_faq_category_id' => $request['guide_faq_category_id'],
            'is_priority'           => empty($request['is_priority']) ? 0 : 1,
            'priority'              => empty($request['is_priority']) ? null : 1,
            'answer'                => $request['answer'],
            'view_status'           => $request['view_status'],
            'stamp'                 => timestamp_to_sqldate(now_stamp()),
        ];
        Freegame::beginTransaction();
        try {
            // 更新内容をチェック
            if (empty($oldGuideFaq['is_priority']) && $request['is_priority']) {
                // 登録しない→登録する の場合、priorityの初期値を入れる
                // 既存データのpriorityを+1
                $this->guideFaq->editCountUpPriority($request['guideAppId']);
                $editGuideFaq['priority'] = 1;
            }
            if (empty($oldGuideFaq['guide_faq_category_id']) != $request['guide_faq_category_id']) {
                // FAQカテゴリが変更された場合はpriority_in_categoryを初期値に
                $editGuideFaq['priority_in_category'] = null;
            }
            // ---------
            // DB
            $this->guideFaq->edit($editGuideFaq, $request['id']);
            // ---------

            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            Log::error(var_export($e->getMessage(), true));
        }

        return true;
    }

    /**
     * Update guide_faq to database
     * @param  integer $faqId
     * @param  integer $guideAppId
     * @return boolean
     */
    public function changeViewStatus($faqId, $guideAppId)
    {
        // ---------
        // DB
        $oldGuideFaq = $this->getOneById($faqId, $guideAppId);
        // ---------
        if (empty($oldGuideFaq)) {
            return false;
        }
 
        $viewStatus = 'display';
        if (strcmp($oldGuideFaq['view_status'], 'display') === 0) {
            $viewStatus = 'hide';
        }

        $editGuideFaq = [
            'view_status' => $viewStatus,
            'stamp'       => timestamp_to_sqldate(now_stamp()),
        ];
        // ---------
        // DB
        $this->guideFaq->edit($editGuideFaq, $faqId);
        // ---------

        return true;
    }

    /**
     * Delete content
     * @param  integer $id
     * @param  integer $guideAppId
     * @return boolean
     */
    public function deleteContent($id, $guideAppId)
    {
        // ---------
        // DB
        $oldGuideFaq = $this->getOneById($id, $guideAppId);
        // ---------
        if (empty($oldGuideFaq)) {
            return false;
        }

        // ---------
        // DB
        $this->guideFaq->del($id);
        // ---------

        return true;
    }

    /**
     * Edit  preview
     * @param  array $request
     * @return array $data
     */
    public function editPreview($request)
    {
        $guideAppId = $request['guideAppId'];
        $data = ['success' => false, 'preview_hash' => '', 'preview_url' => ''];
        $guideApplication = $this->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            $data['errors'][] = '運用サイト情報が取得できません。';
        } else {
            $data = $this->editFaqTmp($request, $guideApplication['domain']);
        }

        return $data;
    }
    /**
     * Edit  guide_faq_tmp table
     * @param  array $request
     * @param string $domain
     * @return boolean
     */
    private function editFaqTmp($request, $domain)
    {
        $guideAppId = $request['guideAppId'];

        // フォームの値にすでにハッシュがある場合、古いプレビューなので削除をする
        $previewHash = $request['preview_hash'];
        if (empty($previewHash) === false) {
            // ---------
            // DB
            $this->delPreview($guideAppId, $previewHash);
            // ---------
        }
        Freegame::beginTransaction();
        try {
            $newTmp = [
                'guide_application_id'  => $guideAppId,
                'question'              => $request['question'],
                'guide_faq_category_id' => $request['guide_faq_category_id'],
                'is_priority'           => isset($request['is_priority']) ? 1 : 0,
                'answer'                => $request['answer'],
                'view_status'           => $request['view_status'],
                'stamp'                 => timestamp_to_sqldate(now_stamp()),
            ];
            // ---------
            // DB
            $newId = $this->guideFaqTmp->insertGetId($newTmp);
            // ---------

            list($previewHash, $previewUrl) = $this->getPreviewHashAndUrl(
                'faq',
                $domain,
                '/id/' . $newId . '/type/faq/key'
            );

            // 作ったハッシュでさらに更新
            // ---------
            // DB
            $result = $this->guideFaqTmp->edit(
                ['preview_hash' => $previewHash],
                $newId
            );
            // ---------
            if ($result) {
                $data['success'] = true;
                $data['preview_hash'] = $previewHash;
                $data['preview_url'] = $previewUrl;
            } else {
                $data['success'] = false;
                $data['errors'][] = 'テンポラリデータの保存に失敗しました。';
            }
            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            Log::error(var_export($e->getMessage(), true));
            $data['success'] = false;
            $data['errors'][] = 'テンポラリデータの保存に失敗しました。';
        }
        return $data;
    }

    /**
     * Get  Preview Hash And Preview Url
     * @param  string $preSeed
     * @param  string $domain
     * @param  string $previewPath
     * @return array(
     *  string $previewHash
     *  string $previewUrl
     * )
     */
    private function getPreviewHashAndUrl($preSeed, $domain, $previewPath)
    {
        // SHA1ハッシュ化（ソルト値＋特定の値)
        $preHashSeed = $preSeed . date('YmdGis', now_stamp());
        $previewHash = hash(
            'sha1',
            env('AUTH_PASSWORD_SALT', 'SomeRandomString')
            . $preHashSeed
            . $preSeed
            . date('YmdGis', now_stamp())
        );
        // プレビューのurl生成
        $previewUrl = adjustment_path($domain);
        $previewUrl .= '/preview' . $previewPath . '/' . $previewHash . '/';
        return [$previewHash, $previewUrl];
    }

    /**
     * delete  preview to database
     * @param  integer $guideAppId
     * @param  string $previewHash
     * @return
     */
    public function delPreview($guideAppId, $previewHash)
    {
        return $this->guideFaqTmp->delByPreviewHash($guideAppId, $previewHash);
    }

    /**
     * Update priority of guide_faq to database
     * @param  array $request
     * @return boolean
     */
    public function priorityEdit($request)
    {
        $result = true;
        if (empty($request['faq'])) {
            return false;
        }

        Freegame::beginTransaction();
        try {
            foreach ($request['faq'] as $intKey => $faq) {
                // ---------
                // DB
                $oldGuideFaq = $this->getOneById($faq['id'], $request['guideAppId']);
                // ---------
                if (empty($oldGuideFaq)) {
                    continue;
                }

                $priority = $intKey + 1;
                $editGuideFaq = [
                    'priority' => $priority,
                    'stamp'    => timestamp_to_sqldate(now_stamp()),
                ];
                // ---------
                // DB
                $result = $this->guideFaq->edit($editGuideFaq, $faq['id']);
                // ---------
            }
            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            Log::error(var_export($e->getMessage(), true));
        }

        return $result;
    }

    /**
     * Update priority_in_category of guide_faq to database
     * @param  array $request
     * @return boolean
     */
    public function priorityCategoryEdit($request)
    {
        $result = true;
        if (empty($request['faq'])) {
            return false;
        }

        Freegame::beginTransaction();
        try {
            foreach ($request['faq'] as $intKey => $faq) {
                // ---------
                // DB
                $oldGuideFaq = $this->getOneById($faq['id'], $request['guideAppId']);
                // ---------
                if (empty($oldGuideFaq)) {
                    continue;
                }

                $priority = $intKey + 1;
                $editGuideFaq = [
                    'priority_in_category' => $priority,
                    'stamp'                => timestamp_to_sqldate(now_stamp()),
                ];
                // ---------
                // DB
                $result = $this->guideFaq->edit($editGuideFaq, $faq['id']);
                // ---------
            }
            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            Log::error(var_export($e->getMessage(), true));
        }

        return $result;
    }

    /**
     * upload image files
     *
     * @param array $request
     * @return array
     */
    public function uploadFileGuideFaq($request)
    {
        // 一時的なファイルを一旦アップロード作業用のフォルダに移動
        $objFile = $request['image_file'];
        $imagePath = $objFile->getRealPath();

        $fileName = $objFile->getClientOriginalName();
        $fileName = $this->getUploadFileName($fileName);

        $uploadPath = $this->imageFilePath($request['guideAppId']);

        // img-freegame にアップ
        $resultFile = $this->uploadFile($imagePath, $fileName, $uploadPath);

        // pics にアップ
        $resultFileToPics = $this->uploadFileToPics($imagePath, $fileName, $uploadPath);

        // ローカルの作業用ファイルを削除
        \File::delete($imagePath);

        // 両方に成功していたら 処理成功
        $success = false;
        if ($resultFile && $resultFileToPics) {
            $success = true;
        } else {
            $msg = '$imagePath=' . var_export($imagePath, true)
                    . ', $fileName=' . var_export($fileName, true)
                    . ', $uploadPath=' . var_export($uploadPath, true)
                    . ', $resultFile=' . var_export($resultFile, true)
                    . ', $resultFileToPics=' . var_export($resultFileToPics, true);
            Log::error($msg);
        }

        // ファイルがアップ成功していたらDBにも保存
        $imgId = '';
        if ($success) {
            $newFaqImage = [
                'guide_application_id' => $request['guideAppId'],
                'guide_faq_id' => (empty($request['id'])) ? 0 : $request['id'],
                'image' => $fileName,
                'stamp'   => timestamp_to_sqldate(now_stamp()),
            ];
            $imgId = $this->guideFaqImage->insertGetId($newFaqImage);
        }

        $data = [
            'files' => [
                'file' => [
                    'name' => $fileName,
                    'id' => $imgId,
                    'guideappid' => $request['guideAppId'],
                    'success' => $success,
                ]
            ]
        ];
        return $data;
    }

    /**
     * delete image files
     *
     * @param array $request
     * @return array
     */
    public function delFileGuideFaq($request)
    {
        $imgName = $request['image'];
        $uploadPath = $this->imageFilePath($request['guideAppId']);

        $success = $this->deleteFileGuideFaq($request['image'], $request['guideAppId']);
        if ($success) {
            $imgId = $request['imgId'];
            if (empty($imgId) == false) {
                // id がある場合は guide_image も消しておく
                $this->guideFaqImage->del($imgId);
            }
        }

        $data = [
            'success' => $success,
        ];
        return $data;
    }

    /**
     * delete image files after hour
     *
     * @return integer
     */
    public function delFileTmpImage()
    {
        $faqImage = $this->guideFaqImage->getListByAfterHour();
        if (count($faqImage)) {
            $idList = [];
            foreach ($faqImage as $image) {
                $this->deleteFileGuideFaq($image['image'], $image['guide_application_id']);
                $idList[] = $image['id'];
            }
            $this->guideFaqImage->delByIdList($idList);
        }
        return count($faqImage);
    }

    private function imageFilePath($guideAppId)
    {
        return 'guide/' . $guideAppId . '/faq/';
    }
    private function getUploadFileName($originFileName)
    {
        $str = preg_split("/\./", $originFileName);
        return sprintf('%s.%s', $this->makeRandImageName(), $str[1]);
    }

    private function deleteFileGuideFaq($image, $guideAppId)
    {
        // img-freegame から削除
        $uploadFile = $this->imageFilePath($guideAppId) . $image;
        $resultFile = $this->deleteFile($uploadFile);

        // pics から削除
        $uploadFile = $this->imageFilePath($guideAppId) . $image;
        $resultFileToPics = $this->deleteFileToPics($uploadFile);

        // 両方に成功していたら 削除成功 とする
        $success = false;
        if ($resultFile && $resultFileToPics) {
            $success = true;
        } else {
            $msg = '$uploadFile=' . var_export($uploadFile, true)
                    . ', $resultFile=' . var_export($resultFile, true)
                    . ', $resultFileToPics=' . var_export($resultFileToPics, true);
            Log::error($msg);
        }
        return $success;
    }

    /**
     * save And get search condition
     * @return boolean
     */
    public function formatSearchCondition($search = [])
    {
        if (request()->has('search')) {
            $search = session('GuideFaq.search', []);
            request()->merge($search);
        }
        $params = array_only($search, [
            'faqCategoryId',
            'perPage',
            'page'
        ]);
        $search = array_filter($params, function ($item) {
            return is_array($item) || $item || is_numeric($item);
        });
        request()->session()->set('GuideFaq.search', $search);
        return $search;
    }

    /**
     * Check Edit Permission
     * @return boolean
     */
    public function isEnableEdit($guideAppId)
    {
        $userId = auth_user_id();
        if (empty($userId)) {
            return false;
        }
        if (auth_is_pf()) {
            return true;
        } else {
            $idList = $this->devGuideApp->getListGuideAppIdByDevId($userId);
            if (in_array($guideAppId, $idList->toArray())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get values
     *
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.GuideFaq');

        return [
            'screenName'  => $privateConfigs['screenName'],
            'menuName'  => $privateConfigs['menuName'],
            'frontFaqDetailPath' => $privateConfigs['frontFaqDetailPath'],
            'imageBasePath' => env('HTTP_IMG_FREEGAMES_URL', 'http://localhost') . '/guide',
        ];
    }

    /**
     * Get guide_faq_category_group List
     * @param  integer $guideAppId
     * @return array   $list
     */
    public function getGuideFaqCategoryGroupList($guideAppId)
    {
        $list = [];

        $getList = $this->guideFaqCategoryGroup->getList([
            'guide_application_id' => $guideAppId
        ]);

        foreach ($getList as $key => $val) {
            $list[$val->id] = $val->name;
        }

        return $list;
    }
}
