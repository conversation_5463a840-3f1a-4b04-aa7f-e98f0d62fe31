<?php
namespace App\Services;

use App\Models\FreegameDeveloper\InquiryMessage;
use App\Models\FreegameDeveloper\InquiryCategorizedRef;
use App\Models\FreegameDeveloper\InquiryCategoryRef;
use App\Models\FreegameDeveloper\InquiryReplayStaff;
use App\Models\FreegamePlatformMessageMongo\MessageMongo;
use App\Models\Freegame\Application;
use App\Models\FreegameDeveloper\DeveloperApplication;
use App\Models\Freegame\User;
use App\Models\FreegameCommunity\Community;
use Carbon\Carbon;
use Exception;

use App\Models\FreegameDeveloper\FreegameDeveloper;
use App\Models\FreegameDeveloper\ApplicationDetail;

/**
 * お問い合わせ
 */
class InquiriesService extends CustomService
{
    protected $inquiryMessage;

    protected $inquiryCategorizedRef;

    protected $inquiryCategoryRef;

    protected $inquiryReplayStaff;

    protected $messageMongo;

    protected $application;

    protected $developerApplication;

    protected $user;

    protected $community;

    protected $appTitleType;

    protected $applicationDetail;

    public function __construct(
        InquiryMessage $inquiryMessage,
        InquiryCategorizedRef $inquiryCategorizedRef,
        InquiryCategoryRef $inquiryCategoryRef,
        InquiryReplayStaff $inquiryReplayStaff,
        MessageMongo $messageMongo,
        Application $application,
        DeveloperApplication $developerApplication,
        User $user,
        Community $community,
        ApplicationDetail $applicationDetail
    ) {
        $this->inquiryMessage = $inquiryMessage;
        $this->inquiryCategorizedRef = $inquiryCategorizedRef;
        $this->inquiryCategoryRef = $inquiryCategoryRef;
        $this->inquiryReplayStaff = $inquiryReplayStaff;
        $this->messageMongo = $messageMongo;
        $this->application = $application;
        $this->developerApplication = $developerApplication;
        $this->user = $user;
        $this->community = $community;
        $this->applicationDetail = $applicationDetail;
    }

    /**
     * Get Message
     * @param  string $code
     * @return array
     */
    public function getMessage($code = null)
    {
        $list = [
            'status.beforeUpdate' => '対応状況' . config('forms.common.suffixBeforeUpdate'),
            'status.afterUpdate' => '対応状況' . config('forms.common.suffixAfterUpdate'),
            'replyStatus.beforeUpdate' => '返信状況' . config('forms.common.suffixBeforeUpdate'),
            'replyStatus.afterUpdate' => '返信状況' . config('forms.common.suffixAfterUpdate'),
            'category.beforeUpdate' => 'カテゴリ' . config('forms.common.suffixBeforeUpdate'),
            'category.afterUpdate' => 'カテゴリ' . config('forms.common.suffixAfterUpdate'),
            'memo.beforeUpdate' => 'メモ' . config('forms.common.suffixBeforeUpdate'),
            'memo.afterUpdate' => 'メモ' . config('forms.common.suffixAfterUpdate')
        ];
        if (isset($code)) {
            return $list[$code];
        }
        return $list;
    }

    /**
     * Get form data
     * @return array
     */
    public function getFormData()
    {
        return [
            'menuName' => config('forms.Inquiries.menuName'),
            'screenName' => config('forms.Inquiries.screenName'),
            'statusType' => config('forms.Inquiries.statusType'),
            'replyStatusType' => config('forms.Inquiries.replyStatusType'),
            'deviceType' => config('forms.Inquiries.deviceType'),
            'kind1Type' => config('forms.Inquiries.kind1Type'),
            'kind2Type' => config('forms.Inquiries.kind2Type'),
            'needReply' => config('forms.Inquiries.needReply'),
            'isMemoType' => config('forms.Inquiries.isMemoType'),
            'userStatusType' => config('forms.Inquiries.userStatusType'),
            'appTitleType' => $this->getAppTitleType(),
            'csvTake' => config('forms.Inquiries.CsvTake'),
            'message' => $this->getMessage()
        ];
    }

    /**
     * Get application title type
     * @return array
     */
    public function getAppTitleType()
    {
        if (isset($this->appTitleType)) {
            return $this->appTitleType;
        }
        if (auth_is_sap()) {
            $devAppList = $this->developerApplication->getApplicationAppIdList([
                'developer_id' => auth_user_id()
            ]);
            if (empty($devAppList->count())) {
                return [];
            } else {
                foreach ($devAppList as $data) {
                    $condition['id'][] = $data->app_id;
                }
                $list = $this->application->getApplicationTitleList($condition);
            }
        } else {
            $list = $this->application->getApplicationTitleList();
        }
        $opts = [];
        foreach ($list as $data) {
            $opts[$data->id] = $data->title;
        }
        $this->appTitleType = $opts;
        return $opts;
    }

    /**
     * Format search condition
     * @param array $search
     * @return array
     */
    public function formatSearchCondition($search = [])
    {
        if (request()->has('search')) {
            $search = session('Inquiries.search', []);
            request()->merge($search);
        }
        $search = array_only($search, [
            'from_last_send_date',
            'to_last_send_date',
            'status',
            'reply_status',
            'app_id',
            'device',
            'user_id',
            'inquiry_id',
            'kind1',
            'kind2',
            'keyword',
            'staff_name',
            'is_memo',
            'sort',
            'large_id',
            'middle_id',
            'small_id',
            'perPage',
            'page',
            'tab'
        ]);
        // 選択されたタブ条件を検索条件に入れる
        $search = $this->formatSearchTab($search);
        request()->merge($search);
        request()->session()->set('Inquiries.search', $search);
        return $search;
    }

    /**
     * Format search query
     * 
     * @param array $search
     * @return array
     */
    public function formatSearchQuery($search = [])
    {
        if (empty($search['from_last_send_date'])) {
            $search['from_last_send_date'] = Carbon::now()->subWeek()->format('Y/m/d')." 00:00:00";
        } else {
            $search['from_last_send_date'] = date('Y-m-d 00:00:00', strtotime($search['from_last_send_date']));
        }
        if (empty($search['to_last_send_date'])) {
            $search['to_last_send_date'] = Carbon::now()->format('Y/m/d')." 23:59:00";
        } else {
            $search['to_last_send_date'] = date('Y-m-d 23:59:59', strtotime($search['to_last_send_date']));
        }
        if (!isset($search['status'])) {
            $search['status'] = 'unprocessed';
        }
        if (empty($search['reply_status'])) {
            $search['reply_status'] = '';
        }
        $appTitleType = $this->getAppTitleType();
        if (empty($search['app_id']) || ! isset($appTitleType[$search['app_id']])) {
            if (auth_is_sap()) {
                $search['app_id'] = array_keys($appTitleType);
                if (empty($search['app_id'])) {
                    $search['app_id'] = '-1';
                }
            } else {
                $search['app_id'] = '';
            }
        }
        if (empty($search['device'])) {
            $search['device'] = '';
        }
        if (! isset($search['user_id']) || ! is_numeric($search['user_id'])) {
            $search['user_id'] = '';
        }
        if (empty($search['inquiry_id'])) {
            $search['inquiry_id'] = '';
        }
        if (empty($search['kind1'])) {
            $search['kind1'] = '';
        }
        if (empty($search['kind2'])) {
            $search['kind2'] = '';
        }
        if (empty($search['keyword'])) {
            $search['keyword'] = '';
        }
        if (empty($search['staff_name'])) {
            $search['staff_name'] = '';
        }
        if (! isset($search['is_memo']) || ! is_numeric($search['is_memo'])) {
            $search['is_memo'] = '';
        }
        if (empty($search['sort'])) {
            $search['sort'] = '';
        }
        if (empty($search['large_id'])) {
            $search['large_id'] = '';
        }
        if (empty($search['middle_id'])) {
            $search['middle_id'] = '';
        }
        if (empty($search['small_id'])) {
            $search['small_id'] = '';
        }
        if (empty($search['page'])) {
            $search['page'] = 1;
        }
        // 選択されたタブ条件を検索条件に入れる
        $search = $this->formatSearchTab($search);
        $search = array_only($search, [
            'from_last_send_date',
            'to_last_send_date',
            'status',
            'reply_status',
            'app_id',
            'device',
            'user_id',
            'inquiry_id',
            'kind1',
            'kind2',
            'keyword',
            'staff_name',
            'is_memo',
            'sort',
            'large_id',
            'middle_id',
            'small_id',
            'perPage',
            'take',
            'count',
            'tab',
            'page'
        ]);
        return $search;
    }

    /**
     * Format search tab
     * @param array $search
     * @return array
     */
    private function formatSearchTab($search = [])
    {
        if (isset($search['tab'])) {
            if ($search['tab'] == 'list') {
                // タブで「一覧」を選択された場合は、検索条件に「対応状況」と「返信状況」をリセットする（全て）
                $search['status'] = '';
                $search['reply_status'] = '';
            } elseif ($search['tab'] == 'unprocessed') {
                // タブで「未対応」を選択された場合は、検索条件に「対応状況」を「未対応」、「返信状況」を「全て」にする
                $search['status'] = 'unprocessed';
                $search['reply_status'] = '';
            } elseif ($search['tab'] == 'ongoing') {
                // タブで「対応中」を選択された場合は、検索条件に「対応状況」を「対応中」、「返信状況」を「全て」にする
                $search['status'] = 'ongoing';
                $search['reply_status'] = '';
            } elseif ($search['tab'] == 'reply') {
                // タブで「返信あり」を選択された場合は、検索条件に「対応状況」を「全て」、「返信状況」を「返信あり」にする
                $search['reply_status'] = 'reply';
                $search['status'] = '';
            } elseif ($search['tab'] == 'expired') {
                // タブで「返信期限切れ」を選択された場合は、検索条件に「対応状況」を「全て」、「返信状況」を「返信期限切れ」にする
                $search['reply_status'] = 'expired';
                $search['status'] = '';
            }
        }
        return $search;
    }

    /**
     * Format search url
     * @param array $search
     * @return array
     */
    public function formatSearchUrl($search = [])
    {
        if (empty($search['from_last_send_date'])) {
            unset($search['from_last_send_date']);
        }
        if (empty($search['to_last_send_date'])) {
            unset($search['to_last_send_date']);
        }
        if (empty($search['status'])) {
            unset($search['status']);
        }
        if (empty($search['reply_status'])) {
            unset($search['reply_status']);
        }
        if (empty($search['app_id'])) {
            unset($search['app_id']);
        }
        if (empty($search['device'])) {
            unset($search['device']);
        }
        if (empty($search['user_id'])) {
            unset($search['user_id']);
        }
        if (empty($search['inquiry_id'])) {
            unset($search['inquiry_id']);
        }
        if (empty($search['kind1'])) {
            unset($search['kind1']);
        }
        if (empty($search['kind2'])) {
            unset($search['kind2']);
        }
        if (empty($search['keyword'])) {
            unset($search['keyword']);
        }
        if (empty($search['staff_name'])) {
            unset($search['staff_name']);
        }
        if (! isset($search['is_memo']) || ! is_numeric($search['is_memo'])) {
            unset($search['is_memo']);
        }
        if (empty($search['sort'])) {
            unset($search['sort']);
        }
        if (empty($search['tab'])) {
            unset($search['tab']);
        }
        if (empty($search['large_id'])) {
            unset($search['large_id']);
        }
        if (empty($search['middle_id'])) {
            unset($search['middle_id']);
        }
        if (empty($search['small_id'])) {
            unset($search['small_id']);
        }
        $search = array_only($search, [
            'from_last_send_date',
            'to_last_send_date',
            'status',
            'reply_status',
            'app_id',
            'device',
            'user_id',
            'inquiry_id',
            'kind1',
            'kind2',
            'keyword',
            'staff_name',
            'is_memo',
            'sort',
            'large_id',
            'middle_id',
            'small_id',
            'perPage',
            'tab',
            'page'
        ]);
        return $search;
    }

    /**
     * Get tab list
     * @param array $condition
     * @return array
     */
    public function getTabList($condition = [])
    {
        $list = [];
        $condition['perPage'] = '';
        $condition['count'] = true;

        foreach (config('forms.Inquiries.tabType') as $key => $val) {
            $condition['tab'] = $key;
            $count = $this->inquiryMessage->getList($this->formatSearchQuery($condition));
            $list[$key] = sprintf('%s：%d件', $val, $count);
        }
        return $list;
    }

    /**
     * Get csv file name
     * @return array
     */
    public function getCsvFileName()
    {
        return sprintf(config('forms.Inquiries.CsvFileName'), date('YmdHis'));
    }

    /**
     * Get csv header
     * @return array
     */
    public function getCsvHeader()
    {
        return [
            'title'                  => 'ゲーム',
            'format_inquiry_date'    => '初回問合せ日時',
            'format_send_date'       => '送受信日時',
            'format_body_id'         => '本文ID',
            'user_id'                => 'ユーザーID',
            'format_last_send_date'  => '最新送受信日時',
            'format_sender'          => '送信者',
            'body'                   => '本文',
            'format_reply_wanted'    => '返信希望',
            'staff_name'             => '担当者',
            'format_status'          => '対応状況',
            'format_reply_status'    => '返信状況',
            'format_category_large'  => '大カテゴリ',
            'format_category_middle' => '中カテゴリ',
            'format_category_small'  => '小カテゴリ',
            'format_kind1'           => '分類',
            'format_kind2'           => '区分',
            'memo'                   => 'メモ',
            'format_device'          => '問合せデバイス',
            'inquiry_id'             => '問合せID',
        ];
    }

    /**
     * Get list inquiry with search params
     * @param array $condition
     * @param boolean $csv
     * @return mixed
     */
    public function getList($condition = [], $csv = false)
    {
        //タイトル取得
        $appTitleType = $this->getAppTitleType();
        $configInquiry = config('forms.Inquiries');
        $configDatetimeFormat = config('forms.Inquiries.datetimeFormat');

        /*
         * 検索条件から問い合わせ取得
         */
        if ($csv) {
            $condition['perPage'] = '';
            $condition['take'] = $configInquiry['CsvTake'];
        } elseif (!$csv && empty($condition['perPage'])) {
            $condition['perPage'] = $configInquiry['perPage'];
        }

        $listData = $this->inquiryMessage->getList($this->formatSearchQuery($condition), $csv);

        if (! $csv) {
            $appends = $this->formatSearchUrl($condition);
            $appends['perPage'] = $listData->perPage();
            $listData->appends($appends);
        }

        if ($listData) {
            //一覧表示用カテゴリ名取得
            $inquiryList = $csv ? $listData->toArray() : $listData->toArray()['data'];

            //Get category data
            $categoryData = $this->getCategoryData($inquiryList);

            //Get user list
            $userIdsList = $this->getUserList($inquiryList);

            foreach ($listData as $key => &$inquiry) {
                if ($inquiry['is_parent'] == 0) {
                    $parentData = $this->inquiryMessage->getParent($inquiry['inquiry_id']);

                    $inquiry['kind1'] = $parentData['kind1'];//分類
                    $inquiry['kind2'] = $parentData['kind2'];//区分
                    $inquiry['status'] = $parentData['status'];//対応状況
                    $inquiry['reply_status'] = $parentData['reply_status'];//対応状況
                    $inquiry['is_memo'] = $parentData['is_memo'];//メモ
                    $inquiry['memo'] = $parentData['memo'];//メモ
                    $inquiry['format_inquiry_date'] = date($configDatetimeFormat, strtotime($parentData['send_date']));//問合せ日時
                    $threads = $this->getThreadList($inquiry['inquiry_id'])->toArray();
                    $bodyId = array_search($inquiry['id'], array_pluck($threads, 'id')) + 2;
                } else {
                    $inquiry['format_inquiry_date'] = date($configDatetimeFormat, strtotime($inquiry['send_date']));//問合せ日時

                    // Dev / お問い合わせ：CSV出力時に「返信要」「返信不要」の出力データに齟齬がある対応
                    if (strpos($inquiry['body'], "返信 : 必要\n")) {
                        $inquiry['need_reply'] = $configInquiry['needReply']['need'];
                        $inquiry['need_reply_status'] = true;
                        $inquiry['format_reply_wanted'] = $configInquiry['replyWantedStatus']['need'];//返信要
                    } else {
                        $inquiry['need_reply'] = $configInquiry['needReply']['none'];
                        $inquiry['need_reply_status'] = false;
                        $inquiry['format_reply_wanted'] = $configInquiry['replyWantedStatus']['none'];//返信不要
                    }

                    $bodyId = 1;//初期本文ID

                    $threads = $this->getThreadList($inquiry['inquiry_id'])->toArray();//お問い合わせの一覧を取得
                }

                // 最新のスレッドの送受信日時を取得
                foreach ($threads as $value) {
                    if ($inquiry['inquiry_id'] == $value['inquiry_id']) {
                        $inquiry['last_send_date'] = $value['send_date'];
                    }
                }

                $inquiry['user'] = array_get($userIdsList, $inquiry['user_id'], '---');
                $inquiry['title'] = array_get($appTitleType, $inquiry['app_id'], '---');//タイトル
                $inquiry['format_status'] = array_get($configInquiry['statusType'], $inquiry['status'], '---');//対応状況
                $inquiry['format_reply_status'] = array_get($configInquiry['replyStatusType'], $inquiry['reply_status'], '---');//返信状況
                $inquiry['format_category_large'] = (!empty($inquiry['large_id'])) ? array_get($categoryData, $inquiry['large_id'], '未設定') : '未設定';//大カテゴリ
                $inquiry['format_category_middle'] = (!empty($inquiry['large_id'])) ? array_get($categoryData, $inquiry['middle_id'], '未設定') : '未設定';//中カテゴリ
                $inquiry['format_category_small'] = (!empty($inquiry['large_id'])) ? array_get($categoryData, $inquiry['small_id'], '未設定') : '未設定';//小カテゴリ
                $inquiry['format_kind1'] = array_get($configInquiry['kind1Type'], $inquiry['kind1'], '---');//分類
                $inquiry['format_kind2'] = array_get($configInquiry['kind2Type'], $inquiry['kind1'] . '.' . $inquiry['kind2'], '---');//区分
                $inquiry['format_device'] = (!empty($inquiry['device'])) ? array_get($configInquiry['deviceType'], $inquiry['device'], '---') : '---';//問合せデバイス
                $inquiry['format_body_id'] = 'No.' . $bodyId;//本文ID
                $inquiry['format_send_date'] = date($configDatetimeFormat, strtotime($inquiry['send_date']));//送信日時
                $inquiry['format_last_send_date'] = date($configDatetimeFormat, strtotime($inquiry['last_send_date']));//最終問合せ日時
                $inquiry['format_sender'] = array_get($configInquiry['inOut'], $inquiry['in_out'], '---');//送信者
            }
            return $listData;
        }
        return [];
    }

    /**
     * Get category data
     * @param array $params
     * @return array
     */
    protected function getCategoryData($params)
    {
        $categoryIds = array_column($params, 'large_id');
        $categoryIds = array_merge($categoryIds, array_column($params, 'middle_id'));
        $categoryIds = array_merge($categoryIds, array_column($params, 'small_id'));
        $data = $this->inquiryCategoryRef->getCategory(array('id' => array_unique($categoryIds)));
        if (! empty($data)) {
            $ids = array_column($data->toArray(), 'id');
            $names = array_column($data->toArray(), 'category_name');
            $data = array_combine($ids, $names);
        }
        return $data;
    }

    /**
     * Get user list
     * @param array $params
     * @return array
     */
    protected function getUserList($params)
    {
        $userIds = array_unique(array_pluck($params, 'user_id'));
        $userList = $this->user->getListByIds($userIds);

        $list = [];
        foreach ($userList as $user) {
            $list[$user->id] = $user;
        }
        return $list;
    }

    /**
     * Get parent inquiry message
     * @param int $id
     * @return  object | bool
     */
    public function getParent($id)
    {
        if (empty($id)) {
            return false;
        }
        $data = $this->inquiryMessage->getParent($id);
        if (empty($data->exists)) {
            return $data;
        }

        $applicationDetail = $this->applicationDetail->getLastById($data->app_id);
        if ($applicationDetail) {
            $configInqSign = config('forms.Inquiries.formatInquirySignatureTime');
            $applicationDetail->inquiry_signature = str_replace($configInqSign['text'], date($configInqSign['format']), $applicationDetail->inquiry_signature);
        }
        $data->applicationDetail = $applicationDetail;

        $appTitleType = $this->getAppTitleType();
        if (! isset($appTitleType[$data->app_id])) {
            $data->exists = false;
            return $data;
        }
        $official = $this->getOfficial($data->app_id);
        if (empty($official->exists)) {
            $data->exists = false;
            return $data;
        }
        $data->official = $official;
        $user = $this->getUser($data->user_id);
        if (empty($user->exists)) {
            $data->exists = false;
            return $data;
        }
        $data->user = $user;
        return $data;
    }

    /**
     * Get thread list
     * @param string $id
     * @return array | bool
     */
    public function getThreadList($id)
    {
        if (empty($id)) {
            return false;
        }
        return $this->inquiryMessage->getThreadList($id);
    }

    /**
     * Get user
     * @param int $id
     * @return object | bool
     */
    public function getUser($id)
    {
        if (empty($id)) {
            return false;
        }
        return $this->user->getOne($id);
    }

    /**
     * Format search condition
     * @param int $app_id
     * @return bool | object
     */
    public function getOfficial($app_id)
    {
        if (empty($app_id)) {
            return false;
        }
        return $this->community->getOfficial([
            'app_id' => $app_id
        ]);
    }

    /**
     * Update status
     * @param array $data
     * @return int | bool
     * @throws Exception
     */
    public function statusUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        $id = $data['id'];
        $data = array_only($data, [
            'status'
        ]);
        $parent = $this->getParent($id);
        if (empty($parent->exists)) {
            return false;
        }
        FreegameDeveloper::beginTransaction();
        try {
            $result = $this->inquiryMessage->editParent($id, $data);
            FreegameDeveloper::commit();
        } catch (Exception $e) {
            FreegameDeveloper::rollback();
            throw $e;
        }

        return $result;
    }

    /**
     * Update reply status
     * @param array $data
     * @return int | bool
     * @throws Exception
     */
    public function replyStatusUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        $id = $data['id'];
        $data = array_only($data, [
            'reply_status'
        ]);
        $parent = $this->getParent($id);
        if (empty($parent->exists)) {
            return false;
        }
        FreegameDeveloper::beginTransaction();
        try {
            $result = $this->inquiryMessage->editParent($id, $data);
            FreegameDeveloper::commit();
        } catch (Exception $e) {
            FreegameDeveloper::rollback();
            throw $e;
        }
        return $result;
    }

    /**
     * カテゴリの更新
     * @param $data
     * @return bool
     */
    public function categoryUpdate($data)
    {
        if (empty($data) || empty($data['category'])) {
            return false;
        }

        $large_id  = (empty($data['category']['large_id'])  || $data['category']['large_id'] == 'not_set')  ? 0 : $data['category']['large_id'];
        $middle_id = (empty($data['category']['middle_id']) || $data['category']['middle_id'] == 'not_set') ? 0 : $data['category']['middle_id'];
        $small_id  = (empty($data['category']['small_id'])  || $data['category']['small_id'] == 'not_set')  ? 0 : $data['category']['small_id'];

        $id = $data['id'];
        $update_data = array(
            'inquiry_id' => $id,
            'large_id' => $large_id,
            'middle_id' => $middle_id,
            'small_id' => $small_id,
        );
        $parent = $this->getParent($id);
        if (empty($parent->exists)) {
            return false;
        }
        return $this->inquiryCategorizedRef->updateCategory($id, $update_data);
    }

    /**
     * Update memo
     * @param array $data
     * @return int | bool
     * @throws Exception
     */
    public function memoUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        if (isset($data['memo']) && strlen($data['memo'])) {
            $data['is_memo'] = '1';
        } else {
            $data['memo'] = '';
            $data['is_memo'] = '0';
        }
        $id = $data['id'];
        $data = array_only($data, [
            'memo',
            'is_memo'
        ]);
        $parent = $this->getParent($id);
        if (empty($parent->exists)) {
            return false;
        }
        FreegameDeveloper::beginTransaction();
        try {
            $result = $this->inquiryMessage->editParent($id, $data);
            FreegameDeveloper::commit();
        } catch (Exception $e) {
            FreegameDeveloper::rollback();
            throw $e;
        }
        return $result;
    }

    /**
     * Store reply
     * @param array $data
     * @param bool $onlyone
     * @return bool | array
     * @throws Exception
     */
    public function replyStore($data, $onlyone = true)
    {
        if (empty($data)) {
            return false;
        }
        $id = $data['id'];
        $parent = $this->getParent($id);
        if (empty($parent->exists)) {
            return false;
        }
        $now = date('Y-m-d H:i:s');
        // メッセージ追加
        $attr = [
            'user_id' => intval($parent->user_id),
            'partner_id' => intval($parent->official->manager_user_id),
            'app_id' => intval($parent->app_id),
            'inquiry_id' => $id,
            'type' => 'inquiry',
            'subject' => $parent->subject,
            'body' => $data['body'],
            'staff_name' => $data['staff_name'],
            'in_out' => 'in',
            'is_read' => false,
            'is_delete' => false,
            'create_date' => $now,
            'update_date' => $now
        ];
        $lastInsertId = $this->messageMongo->addGetId($attr);

        FreegameDeveloper::beginTransaction();
        try {
            // スレッド追加
            $attr = [
                'id' => $lastInsertId,
                'app_id' => $parent->app_id,
                'inquiry_id' => $id,
                'user_id' => $parent->user_id,
                'is_parent' => '0',
                'in_out' => 'out',
                'kind1' => '',
                'kind2' => '',
                'subject' => $parent->subject,
                'body' => $data['body'],
                'return_message_id' => $parent->id,
                'send_date' => $now,
                'last_send_date' => $now,
                'memo' => ''
            ];
            $this->inquiryMessage->add($attr);
            // 担当者名追加
            $attr = [
                'inquiry_message_id' => $lastInsertId,
                'staff_name' => $data['staff_name'],
                'stamp' => $now,
            ];

            $this->inquiryReplayStaff->add($attr);
            // ステータス更新
            if ($onlyone) {
                $data['reply_status'] = 'finish';
            }
            $attr = array_only($data, [
                'status',
                'reply_status'
            ]);
            $this->inquiryMessage->editParent($id, $attr);

            FreegameDeveloper::commit();
        } catch (Exception $e) {
            FreegameDeveloper::rollback();
            throw $e;
        }

        // メール送信
        if (env('MAIL_SEND_AVAILABLE', true)) {
            try {
                $this->sendXmlRpcData([
                    'message' => 'Developer_Mail.SendInquiryMail',
                    'params' => [
                        'user_id' => $parent->user_id,
                        'subject' => $parent->subject,
                        'app_id' => $parent->app_id,
                    ]
                ]);
            } catch (Exception $e) {
                return [
                    'message' => 'メールの送信に失敗しました。'
                ];
            }
        }
        return true;
    }

    /**
     * Store all reply
     * @param array $data
     * @return bool | array
     * @throws Exception
     */
    public function replyAllStore($data)
    {
        if (empty($data)) {
            return false;
        }
        $errors = [];
        $inquiry_id = $data['inquiry_id'];
        if (isset($inquiry_id) && is_array($inquiry_id)) {
            $attr['body'] = $data['body'];
            $attr['staff_name'] = $data['staff_name'];
            if (! empty($data['status'])) {
                $attr['status'] = $data['status'];
            }
            if (! empty($data['reply_status'])) {
                $attr['reply_status'] = $data['reply_status'];
            }
            foreach ($inquiry_id as $no => $id) {
                $attr['id'] = $id;
                $result = $this->replyStore($attr, false);
                if (is_array($result)) {
                    foreach ($result as $key => $msg) {
                        $errors[$key . '.' . $no] = sprintf('問合せ【%s】：%s', $id, $msg);
                    }
                }
            }
        }
        if (! empty($errors)) {
            return $errors;
        }
        return true;
    }

    /**
     * Get last reply
     * @param string $inquiry_id
     * @param string $in_out
     * @return object
     */
    public function getLastReply($inquiry_id, $in_out = 'in')
    {
        $lastReply = $this->inquiryMessage->getLastReply($inquiry_id, $in_out);
        if (empty($lastReply)) {
            $lastReply = '';
        } else {
            $lastReply = $lastReply->body;
        }
        return $lastReply;
    }

    /**
     * Get create confirm text
     * @param string $id
     * @return array
     */
    public function getCreateConfirmText($id)
    {
        $text = $this->getLastReply($id, 'in');
        $pattern = '/^(お問い合わせ種別 : )(.*)\n(返信 : )(.*)\n\n(.*)/u';

        if (preg_match($pattern, $text, $match)) {
            $return = array(
                'kind'  => $match[2],
                'reply' => $match[4],
                'text'  => $match[5]
            );
        } else {
            $return = array( 'text' => $text );
        }
        return $return;
    }

    /**
     * 指定カテゴリリファレンスIDの子カテゴリの選択肢を取得
     * @param $id
     * @return array
     */
    public function getChildrenCategoryOption($id)
    {
        if (empty($id)) {
            return;
        }
        $category_option = array();
        $categories = $this->inquiryCategoryRef->getCategory(['parent_id' => $id]);
        if (!empty($categories)) {
            //idとカテゴリ名の配列にする
            foreach ($categories->toArray() as $category) {
                $category_option[$category['id']] = $category['category_name'];
            }
        }
        return $category_option;
    }

    /**
     * 大中小カテゴリの選択肢を取得
     * ・検索用の場合、参照権限のあるアプリの大カテゴリを取得し、ゲームごとにグループ化する
     * ・検索用ではない場合(詳細用)、指定アプリIDの大カテゴリを取得する
     * @param $condition
     * @param bool $search
     * @return array
     */
    public function getCategoryOptions($condition, $search = false)
    {
        $category_option = array(
            'large'  => array(),
            'middle' => array(),
            'small'  => array(),
        );

        //大カテゴリの取得
        $app_list = $this->getAppTitleType();
        $categories = $this->inquiryCategoryRef->getCategory(array(
            'app_id' => $search ? array_keys($app_list) : $condition['app_id'],
            'relation' => 'large',
        ));
        $category_per_game = array();
        if (! empty($app_list)) {
            if ($search) {
                foreach ($categories as $category) {
                    $app_title = $app_list[$category->app_id];
                    $category_per_game[$app_title][$category->id] = $category->category_name;
                }
            } else {
                foreach ($categories as $category) {
                    $category_per_game[$category->id] = $category->category_name;
                }
            }
        }
        $category_option['large'] = $category_per_game;

        //中小カテゴリ
        if (!empty($condition['large_id'])) {
            $category_option['middle'] = $this->getChildrenCategoryOption($condition['large_id']);
        }
        if (!empty($condition['middle_id'])) {
            $category_option['small']  = $this->getChildrenCategoryOption($condition['middle_id']);
        }

        //共通値等設定
        $common_option = [0 => '全て', 'not_set' => '未設定'];
        if (!$search) {
            unset($common_option[0]);
        }
        $category_option['large']  = ['大カテゴリ' => []] + $common_option + $category_option['large'];
        $category_option['middle'] = ['中カテゴリ' => []] + $common_option + $category_option['middle'];
        $category_option['small']  = ['小カテゴリ' => []] + $common_option + $category_option['small'];

        return $category_option;
    }

    /**
     * Get category
     * @param array $condition
     * @return array
     */
    public function getCategory($condition)
    {
        return $this->inquiryCategoryRef->getCategory($condition);
    }
}
