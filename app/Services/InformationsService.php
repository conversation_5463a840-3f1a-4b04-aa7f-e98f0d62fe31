<?php
namespace App\Services;

use App\Models\FreegameDeveloper\Information;
use App\Services\Accessory\InformationFormat;

/**
 * お知らせ
 */
class InformationsService extends CustomService
{
    use InformationFormat;

    protected $information;

    public function __construct(Information $information)
    {
        $this->information = $information;
    }

    public function getType($category = 'info')
    {
        switch ($category) {
            case 'info':
            case 'info_important':
            case 'required_important':
            case 'required_info':
                return 'info';
            case 'failure_occurrence':
            case 'failure_recovery':
                return 'failure';
        }
    }

    public function getFormData()
    {
        return [
            'menuName' => config('forms.Informations.menuName'),
            'screenName' => config('forms.Informations.screenName'),
            'categoryType' => config('forms.Informations.categoryType'),
            'categoryTitlePrefix' => config('forms.Informations.categoryTitlePrefix'),
        ];
    }

    public function getList($condition = [])
    {
        if (empty($condition['perPage'])) {
            $condition['perPage'] = config('forms.Informations.perPage');
        }

        $paginator = $this->information->getList($condition);

        $paginator->appends([
            'perPage' => $paginator->perPage()
        ]);
        if (isset($condition['keyword'])) {
            $paginator->appends([
                'keyword' => $condition['keyword']
            ]);
        }
        return $paginator;
    }

    public function getInfoList($condition = [])
    {
        $condition['category'] = [
            'info',
            'info_important',
            'required_important',
            'required_info'
        ];
        return $this->formatInfoList($this->getList($condition));
    }

    public function getFailureList($condition = [])
    {
        $condition['category'] = [
            'failure_occurrence',
            'failure_recovery',
        ];

        return $this->formatFailureList($this->getList($condition));
    }

    public function getData($id)
    {
        if (empty($id)) {
            return false;
        }
        return $this->information->getOne($id);
    }

    /**
     * save And get search condition
     * @param request
     * @param boolean
     * @return boolean
     */
    public function formatSearchCondition($search = [], $isInfoList)
    {
        $sessionName = ( $isInfoList == true ) ? 'Informations.infolist.searchform' : 'Informations.failurelist.searchform' ;

        if (request()->has('search')) {
            $search = session($sessionName, []);
            request()->merge($search);
        }

        // キーワードを整える
        if (isset($search['keyword'])) {
            $search['keyword'] = $this->getReplaceKeyword($search['keyword']);
        }

        request()->session()->set($sessionName, $search);    // sessionに格納

        // キーワードが有効なら検索用配列を用意
        if (isset($search['keyword'])) {
            $search['keywordArray'] = explode(' ', $search['keyword']);
        }
        return $search;
    }

    /**
     * キーワード整形
     * @param string
     * @return string|null
     */
    public function getReplaceKeyword($keyword)
    {
        if (isset($keyword)) {
            $keyword = preg_replace('/　/', ' ', $keyword);    // 全角スペースを半角に
            $keyword = trim($keyword);
            $keyword = preg_replace('/\s+/', ' ', $keyword);    // 連続するスペースをひとつに
        }

        // 有効な検索キーワードが残らなければnull
        if ($keyword == '' || $keyword == ' ') {
            $keyword = null;
        }
        return $keyword;
    }
}
