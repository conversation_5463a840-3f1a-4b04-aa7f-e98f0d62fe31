<?php
namespace App\Services;

use App\Models\Freegame\MonthlyService;
use App\Models\FreegameDeveloper\DeveloperApplication;
use App\Models\FreegameDeveloper\MonthlyServiceMismatchedPrice;
use App\Models\FreegameDeveloper\MonthlyServiceTotalPrice;

/**
 * 月額課金比較API
 */
class MonthlyServicePriceLogApisService extends CustomService
{
    protected $MonthlyService;
    protected $DeveloperApplication;
    protected $MonthlyServiceMismatchedPrice;
    protected $MonthlyServiceTotalPrice;

    public function __construct(
        MonthlyService                $MonthlyService,
        DeveloperApplication          $DeveloperApplication,
        MonthlyServiceMismatchedPrice $MonthlyServiceMismatchedPrice,
        MonthlyServiceTotalPrice      $MonthlyServiceTotalPrice
    ) {
        $this->MonthlyService                = $MonthlyService;
        $this->DeveloperApplication          = $DeveloperApplication;
        $this->MonthlyServiceMismatchedPrice = $MonthlyServiceMismatchedPrice;
        $this->MonthlyServiceTotalPrice      = $MonthlyServiceTotalPrice;
    }

    /**
     * CSVファイル名取得
     *
     * @param  object $condition
     *
     * @return string
     *
     */
    public function getCsvFileName($condition = [])
    {
        if (empty($condition['begin'])) {
            $condition['begin'] = '1970/01/01';
        }
        if (empty($condition['end'])) {
            $condition['end'] = date('Y/m/d');
        }
        $date = date('Y-m-d', strtotime($condition['begin']));
        if ($condition['begin'] != $condition['end']) {
            $date = $date.'_'.date('Y-m-d', strtotime($condition['end']));
        }

        return sprintf(
            config('forms.MonthlyServicePriceLogApis.CsvFileName'),
            $condition['monthly_service_id'],
            $date
        );
    }

    /**
     * CSVヘッダー取得
     *
     * @return array  $header
     *
     */
    public function getCsvHeader()
    {
        $header      = [];
        $header['1'] = '月額サービスID';
        $header['2'] = 'ペイメントID(送信データ)';
        $header['3'] = '単価(送信データ)';
        $header['4'] = 'ステータス(送信データ)';
        $header['5'] = 'ペイメントID(弊社データ)';
        $header['6'] = '単価(弊社データ)';
        return $header;
    }

    /**
     * CSV出力データ取得
     *
     * @param  object $condition
     *
     * @return array  $list
     *
     */
    public function getCsvList($condition = [])
    {
        $list = [];

        $listStatus = config('forms.MonthlyServicePriceLogApis.listStatus');

        $search = [
            'monthly_service_id' => $condition['monthly_service_id'],
            'begin'              => $condition['begin'].' 00:00:00',
            'end'                => $condition['end'].' 23:59:59',
        ];

        $dataTotalPrice = $this->MonthlyServiceTotalPrice->getTotal($search);

        $list[] = [
            '1' => '',
            '2' => '合計',
        ];
        $list[] = [
            '1' => 'SAP側のみ',
            '2' => array_get($dataTotalPrice, 'game_total_price', 0)
        ];
        $list[] = [
            '1' => 'DMM側のみ',
            '2' => array_get($dataTotalPrice, 'dmm_total_price', 0)
        ];
        $list[] = [
            '1' => '双方に存在',
            '2' => array_get($dataTotalPrice, 'matched_total_price', 0)
        ];
        $list[] = [];
        $list[] = $this->getCsvHeader();

        $listMismatchedPrice = $this->MonthlyServiceMismatchedPrice->getList($search);

        foreach ($listMismatchedPrice as $val) {
            $list[] = [
                '1' => $val->monthly_service_id,
                '2' => $val->game_payment_id,
                '3' => $val->game_unit_price,
                '4' => $listStatus[$val->compare_status],
                '5' => $val->dmm_payment_id,
                '6' => $val->dmm_unit_price,
            ];
        }

        return $list;
    }

    /**
     * 月額サービス一覧取得
     *
     * @param  array $conditions
     *
     * @return array $list
     *
     */
    public function getServiceList($conditions = [])
    {
        $list = [];

        $search = [];
        if (auth_is_sap()) {
            // SAPの場合は権限のあるアプリケーションのみ
            $listTmp = $this->DeveloperApplication->getApplicationAppIdList(['developer_id' => auth_user_id()]);
            foreach ($listTmp as $val) {
                $search['app_id'][] = $val->app_id;
            }
        }

        $listTmp = $this->MonthlyService->getListWithApplication($search);
        foreach ($listTmp as $val) {
            $list[$val->id] = $val->service_name.'（'.$val->title.'）';
        }

        return $list;
    }
}
