<?php

namespace App\Services;

use App\Models\Freegame\MonthlyService;
use App\Models\Freegame\MonthlyPaymentOrderLog;
use App\Models\FreegameDeveloper\DeveloperApplication;
use Validator;

class MonthlyServicePriceLogsService extends CustomService
{
    protected $monthlyService;
    protected $monthlyPaymentOrderLog;
    protected $developerApplication;

    // sap_logのcsvのファイルハンドラ＆ポインタ格納配列
    protected $sapLogList = [];
    protected $pointerList = [];

    // ファイル名
    protected $fileName = 'MonthlyServicePriceLogApi_%s_%s.csv';

    // ヘッダー行
    protected $headerParent = ['DMM側', '', '', '', '', 'SAP側','',];
    protected $headerChild = ['device', 'monthly_service_id', 'pay_type', 'payment_id', 'unit_price(単価)', '比較ステータス', 'payment_id', 'unit_price(単価)'];

    protected $notOpenSapLog = 'SAP側課金ログのファイルを開けませんでした。';
    protected $sapLogLineErr = 'SAP側課金ログの%d行目が正しくありません。';

    // 状態
    protected $status = [
        'dmm'  => 'DMM側のみ',
        'game' => 'SAP側のみ',
        'both'  => '双方に存在',
        'double' => '双方(重複)',
    ];

    public function __construct(
        MonthlyService $monthlyService,
        MonthlyPaymentOrderlog $monthlyPaymentOrderLog,
        DeveloperApplication $developerApplication
    ) {
        $this->monthlyService = $monthlyService;
        $this->monthlyPaymentOrderLog = $monthlyPaymentOrderLog;
        $this->developerApplication = $developerApplication;
    }

    /**
     * Get values for form select box,radio box
     *
     * @return array
     */
    public function getFormData()
    {
        return config('forms.MonthlyServicePriceLogs');
    }

    /**
     * セレクトボックスのタイトルのリストを取得
     * @return object
     */
    public function getMonthlyAppTitleList()
    {
        $appTitleList = [];
        $developerId = null;
        $list = $this->monthlyService->getListWithApplication(['kind' => 'social']);
        if (auth_is_sap()) {
            $tmpList = $this->developerApplication->getListByDeveloperId(['developer_id' => auth_user_id()]);
            $sapApp = [];
            foreach ($tmpList as $tmp) {
                $sapApp[$tmp->app_id] = $tmp->app_id;
            }
            foreach ($list as $data) {
                if (isset($sapApp[$data->app_id])) {
                    $appTitleList[$data->app_id] = $data->title;
                }
            }
        } else {
            foreach ($list as $data) {
                $appTitleList[$data->app_id] = $data->title;
            }
        }
        return $appTitleList;
    }

    /**
     * セレクトボックスのタイトルのリストを取得
     * @return object
     */
    public function getMonthlyServiceList()
    {
        $serviceList = [];
        $developerId = null;
        $list = $this->monthlyService->getList(['kind' => 'social']);
        if (auth_is_sap()) {
            $tmpList = $this->developerApplication->getListByDeveloperId(['developer_id' => auth_user_id()]);
            $sapApp = [];
            foreach ($tmpList as $tmp) {
                $sapApp[$tmp->app_id] = $tmp->app_id;
            }
            foreach ($list as $data) {
                if (isset($sapApp[$data->app_id])) {
                    $serviceList[$data->id] = $data->service_name;
                }
            }
        } else {
            foreach ($list as $data) {
                $serviceList[$data->id] = $data->service_name;
            }
        }
        return $serviceList;
    }

    /**
     * csv1行出力
     * $lineList 1行分の配列
     */
    public function outputLine($lineList) {
        echo mb_convert_encoding(implode(',', $lineList), 'sjis-win', 'UTF-8')."\n";
    }

    /**
     * ファイル名
     */
    public function getFileName($request) {
        // 期間が同日以外の場合のみbegin、endの日付設定
        $targetDate = date('Y-m-d', strtotime($request->get('begin') . '00:00:00'));
        if ($request->get('begin') != $request->get('end')) {
            $targetDate .= '_' . date('Y-m-d', strtotime($request->get('end') . '00:00:00'));
        }
        return sprintf($this->fileName, $request->get('monthly_service_id'), $targetDate);
    }

    /**
     * monthly_payment_order_logから最小id＆最大idを取得
     * @param object $request
     */
    public function getMinAndMaxId($request) {
        $params['begin'] = date('Y-m-d H:i:s', strtotime($request->get('begin') . '000000'));
        $params['end'] = date('Y-m-d H:i:s', strtotime($request->get('end') . '235959'));
        $params['monthly_service_id'] = $request->get('monthly_service_id');
        $params['app_id'] = $request->get('app_id');

        $minMaxId['minId'] = $this->monthlyPaymentOrderLog->getLimitId(false, $params);
        $minMaxId['maxId'] = $this->monthlyPaymentOrderLog->getLimitId(true, $params);

        return $minMaxId;
    }

    /**
     * csvのファイルハンドラとポインタの配列を設定<br>
     * ファイルに誤りがある場合はエラーメッセージを配列で返す
     * @param object $request
     * @return array $errorMsg
     */
    public function setPointerList($request)
    {
        $lineNumber = 0;
        $errorMsgkey = 0;
        $errorMsg = [];
        $this->pointerList = [];

        // 1行目が項目名ではない場合はポインタを最初に戻す
        $line = fgets($this->sapLogList);
        if (false !== strpos($line, ',')) {
            list($payment_id,) = explode(',', $line, 2);
            if (preg_match("/^[\d\-]+$/", $payment_id)) {
                rewind($this->sapLogList);
            }
        } else {
            ++$lineNumber;
        }

        while (!feof($this->sapLogList)) {
            ++$lineNumber;
            $line = fgets($this->sapLogList);

            if (0 === strlen($line)) {
                continue;
            }

            // SAPのcsvバリデーション、さらにエラー時は$this->pointerListをクリア
            $payment_id = null;
            if (strpos($line, ',')) {
                list($payment_id,) = explode(',', $line, 2);
            }
            if (empty($payment_id)) {
                $errorMsg['sap_log.' . $errorMsgkey] = sprintf($this->sapLogLineErr, $lineNumber);
                ++$errorMsgkey;
                $this->pointerList = [];
                continue;
            }

            // payment_idをキーにしたポインタの配列を作成
            if (count($errorMsg) < 1) {
                if (! isset($this->pointerList[$payment_id])) {
                    $this->pointerList[$payment_id] = [];
                }
                $this->pointerList[$payment_id][] = ftell($this->sapLogList) - strlen($line);
            }
        }
        return $errorMsg;
    }

    /**
     * csvのダウンロード
     * @param object $request
     */
    public function download($request)
    {
        // sapファイルを送信時のみファイルのデータを取得
        if (! empty($request->get('sap_log'))) {
            $sapLog = $request->file('sap_log');
            if (! isset($sapLog) || ! is_object($sapLog) || ! $sapLog->isValid()) {
                $errorMsg['sap_log.0'] = $this->notOpenSapLog;
                return $errorMsg;
            }

            // sapファイルオープンとポインタリストの設定
            $this->sapLogList = fopen($sapLog, 'r');
            $errorMsg = $this->setPointerList($request);

            if (count($errorMsg) > 0) {
                fclose($this->sapLogList);
                return $errorMsg;
            }
        }

        // ヘッダ(＆見出し行)送信
        header('Content-Type: application/csv');
        header('Content-Disposition: attachment; filename=' . $this->getFileName($request));
        $this->outputLine($this->headerParent);
        $this->outputLine($this->headerChild);

        // monthly_payment_order_logから対象となる最小、最大のidを取得
        $minMaxId = $this->getMinAndMaxId($request);
        if (empty($minMaxId['minId']) || empty($minMaxId['maxId'])) {
            if (! empty($request->get('sap_log'))) {
                fclose($this->sapLogList);
            }
            return;
        }
        $params['monthly_service_id'] = $request->get('monthly_service_id');
        $params['app_id'] = $request->get('app_id');

        // 最小、最大のidから、対象となるレコードを取得
        // 現在のidを含めた件数の為一回分の件数-1
        $chunkCnt = 20000 - 1;

        $fromId = $minMaxId['minId'];
        $lastId = $minMaxId['maxId'];

        $sapDobleList = [];
        while ($fromId <= $lastId) {
            // 先頭+まとめて取得する件数から後尾id設定、最大超えたら最大を設定
            $toId = $fromId + $chunkCnt;
            if ($toId > $lastId) {
                $toId = $lastId;
            }

            // 対象のidの範囲でレコード取得
            $params['fromId'] = $fromId;
            $params['toId'] = $toId;

            $dbList = $this->monthlyPaymentOrderLog->getMatchPaymentList($params);

            // dbとcsvと比較
            foreach ($dbList as $dbObj) {
                if (isset($this->pointerList[$dbObj->payment_id])) {
                    /*--------------------*/
                    /* dmmとsap双方あり   */
                    /*--------------------*/
                    // ステータス判定、csvで同payment_idが重複の場合statusを重複に
                    $status = $this->status['both'];
                    if (count($this->pointerList[$dbObj->payment_id]) > 1) {
                        $status = $this->status['double'];
                        $sapDobleList[$dbObj->payment_id] = $dbObj;
                    }
                    // ファイルハンドラを該当するpaiment_idのポインタに移動
                    fseek($this->sapLogList, $this->pointerList[$dbObj->payment_id][0], SEEK_SET);
                    $sapRow = fgetcsv($this->sapLogList);

                    $lineList = [
                        $dbObj->regist_device,
                        $dbObj->monthly_service_id,
                        $dbObj->pay_type,
                        $dbObj->payment_id,
                        $dbObj->price + $dbObj->tax,
                        $status,
                        trim($sapRow[0]),
                        trim($sapRow[1]),
                    ];

                    // 双方あった場合はポインタ削除
                    unset($this->pointerList[$dbObj->payment_id][0]);
                    if (empty($this->pointerList[$dbObj->payment_id])) {
                        unset($this->pointerList[$dbObj->payment_id]);
                    }
                } else {
                    /*--------------------*/
                    /* dmmのみ            */
                    /*--------------------*/
                    $status = $this->status['dmm'];
                    $sapData['payment_id'] = null;
                    $sapData['unit_price'] = null;

                    $lineList = [
                        $dbObj->regist_device,
                        $dbObj->monthly_service_id,
                        $dbObj->pay_type,
                        $dbObj->payment_id,
                        $dbObj->price + $dbObj->tax,
                        $status,
                        '',
                        '',
                    ];
                }
                $this->outputLine($lineList);
            }
            // 次のidの先頭設定
            $fromId = $toId + 1;
        }
        /*--------------------*/
        /* sapのみ            */
        /*--------------------*/
        reset($this->pointerList);
        foreach ($this->pointerList as $paymentIdList) {
            // ステータスを重複の場合と切り替え
            $status = $this->status['game'];
            if (isset($paymentIdList[1])) {
                $status = $this->status['double'];
            }
            foreach ($paymentIdList as $pointer) {
                fseek($this->sapLogList, $pointer, SEEK_SET);
                $sapRow = fgetcsv($this->sapLogList);
                $sapPaymentId = trim($sapRow[0]);
                $sapUnitPrice = trim($sapRow[1]);

                // sapリストが重複なおかつ双方に存在の場合、dmm側の情報も記載
                $dmmDevice = '';
                $dmmPayType = '';
                $dmmPaymentId = '';
                $dmmUnitPrice = '';
                if (isset($sapDobleList[$sapPaymentId])) {
                    $dmmDevice = $sapDobleList[$sapPaymentId]->regist_device;
                    $dmmPayType = $sapDobleList[$sapPaymentId]->pay_type;
                    $dmmPaymentId = $sapDobleList[$sapPaymentId]->payment_id;
                    $dmmUnitPrice = $sapDobleList[$sapPaymentId]->price + $sapDobleList[$sapPaymentId]->tax;
                }

                $lineList = [
                    $dmmDevice,
                    $dmmPayType,
                    $dmmPaymentId,
                    $dmmUnitPrice,
                    $status,
                    $sapPaymentId,
                    $sapUnitPrice,
                ];
                $this->outputLine($lineList);
            }
        }

        // sapファイルクローズ(sapファイル送信時のみ)
        if (! empty($request->get('sap_log'))) {
            fclose($this->sapLogList);
        }
    }
}
