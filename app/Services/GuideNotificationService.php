<?php

namespace App\Services;

use App\Models\FreegameGuide\GuideApplication;
use App\Models\FreegameDeveloper\DeveloperGuideApplication;
use App\Models\FreegameGuide\GuideNotification;
use App\Models\FreegameGuide\GuideNotificationCategory;
use App\Models\FreegameGuide\GuideNotificationImage;
use App\Models\FreegameGuide\GuideNotificationTopImage;
use App\Models\FreegameGuide\GuideNotificationTmp;
use App\Models\FreegameGuide\GuideNotificationTag;
use App\Models\FreegameGuide\GuideNotificationTagRef;
use \Exception;
use App\Models\Freegame\Freegame;
use Log;
use Carbon\Carbon;

class GuideNotificationService extends CustomService
{
    /**
     * 画像ファイルの保存ディレクトリ
     */
    const IMAGES_DIRECTORY = 'notification';
    const TOP_IMAGE_DIRECTORY = 'notification_top';

    protected $guideApp;
    protected $devGuideApp;
    protected $guideNotic;
    protected $guideNoticCategory;
    protected $guideNoticImage;
    protected $guideNoticTopImage;
    protected $guideNoticTmp;
    protected $guideNoticTag;
    protected $guideNoticTagRef;

    public function __construct(
        GuideApplication $guideApp,
        DeveloperGuideApplication $devGuideApp,
        GuideNotification $guideNotic,
        GuideNotificationCategory $guideNoticCategory,
        GuideNotificationImage $guideNoticImage,
        GuideNotificationTopImage $guideNoticTopImage,
        GuideNotificationTmp $guideNoticTmp,
        GuideNotificationTag $guideNoticTag,
        GuideNotificationTagRef $guideNoticTagRef
    ) {
        $this->guideApp = $guideApp;
        $this->devGuideApp = $devGuideApp;
        $this->guideNotic = $guideNotic;
        $this->guideNoticCategory = $guideNoticCategory;
        $this->guideNoticImage = $guideNoticImage;
        $this->guideNoticTopImage = $guideNoticTopImage;
        $this->guideNoticTmp = $guideNoticTmp;
        $this->guideNoticTag = $guideNoticTag;
        $this->guideNoticTagRef = $guideNoticTagRef;
    }

    /**
     * Get guide_notification
     * @param  array $condition
     * @return array
     */
    public function getList($guideAppId, $condition)
    {
        $condition['guideAppId'] = $guideAppId;
        if (isset($condition['perPage']) === false) {
            $condition['perPage'] = config('forms.GuideNotification.perPage');
        }

        if (isset($condition['searchWord']) === false) {
            $condition['searchWord'] = '';
        }
        if (isset($condition['searchPriority']) === false) {
            $condition['searchPriority'] = 0;
        }

        // ---------
        // DB
        $noticLists = $this->guideNotic->getList($condition);
        // ---------

        // ---------
        // Pagination Options
        if (empty($noticLists) === false) {
            // urlに検索条件を加える
            $appends = array();
            if (empty($condition['searchWord']) === false) {
                $appends['searchWord'] = $condition['searchWord'];
            }
            if (empty($condition['searchPriority']) === false) {
                $appends['searchPriority'] = $condition['searchPriority'];
            }
            $appends['perPage'] = $condition['perPage'];
            $noticLists->appends($appends);
        }
        // ---------

        // ---------
        // result
        return $noticLists;
    }

    /**
     * Get guide_notification by id
     * @param  integer $id
     * @param  integer $guideAppId
     * @return array
     */
    public function getOneById($id, $guideAppId)
    {
        $getList = $this->guideNotic->getOneById($id, $guideAppId);

        if (empty($getList)) {
            return $getList;
        }

        $getList = $getList->toArray();
        $getList['tag'] = collect($getList['tag'])->keyBy('tag_id')->toArray();
        return $getList;
    }

    /**
     * Get guide_notification_category List
     * @param  integer $guideAppId
     * @return array
     */
    public function getListNoticCategoryName($guideAppId, $isUserNone = true)
    {
        $result = array();
        if ($isUserNone) {
            $result = array('' => '未選択');
        }
        $getList = $this->guideNoticCategory->getListByGuideAppId($guideAppId);
        foreach ($getList as $noticCat) {
            $index = $noticCat['id'];
            $result[$index] = $noticCat['name'];
        }
        return $result;
    }

    /**
     * Get guide_notification_image List
     * @param  integer $noticId
     * @return array
     */
    public function getListNoticImage($noticId)
    {
        $getList = $this->guideNoticImage->getListByNoticId($noticId);
        return $getList->toArray();
    }

    /**
     * Get guide_notification_top_image
     * @param  integer $guideNotificationId
     * @return array
     */
    public function getTopImage($guideNotificationId)
    {
        $topImage = $this->guideNoticTopImage->getOneByNoticId($guideNotificationId);
        return ($topImage) ? $topImage->toArray() : null;
    }

    /**
     * Get guide_application by id
     * @param  integer $id
     * @return array
     */
    public function getOneGuideApplication($id)
    {
        // ---------
        // DB
        $guideApplication = $this->guideApp->getOne($id);
        // ---------
        return empty($guideApplication) ? $guideApplication : $guideApplication->toArray();
    }

    /**
     * Get guide_notification by is_priority is '1'
     * @param  integer $guideAppId
     * @return array
     */
    public function getListPriority($guideAppId)
    {
        $getList = $this->guideNotic->getListPriority($guideAppId);

        return $getList->toArray();
    }

    /**
     * Insert guide_notification to database
     * @param  array $request
     * @return boolean
     */
    public function create($request)
    {
        // Request で値はチェック済み
        $newGuideNotic = [
            'guide_application_id'           => $request['guideAppId'],
            'title'                          => $request['title'],
            'guide_notification_category_id' => $request['guide_notification_category_id'],
            'is_priority'                    => empty($request['is_priority']) ? 0 : 1,
            'body'                           => $request['body'],
            'device'                         => $request['device'],
            'published_datetime'             => $request['published_datetime'],
            'view_status'                    => 'hide',
            'stamp'                          => timestamp_to_sqldate(now_stamp()),
        ];
        Freegame::beginTransaction();
        try {
            // ---------
            // DB
            if (empty($request['is_priority']) === false) {
                // 既存データのpriorityを+1
                $this->guideNotic->editCountUpPriority($request['guideAppId']);
            }
            $newId = $this->guideNotic->insertGetId($newGuideNotic);
            // ---------

            // guide_notification_idが確定したので、更新する
            if (isset($request['images'])) {
                $idList = array();
                foreach ($request['images'] as $image) {
                    $editNoticImage = [
                        'guide_notification_id' => $newId,
                        'stamp'   => timestamp_to_sqldate(now_stamp()),
                    ];
                    $this->guideNoticImage->edit($editNoticImage, $image['id']);
                }
            }

            if (!empty($request['top_image']['id'])) {
                $this->guideNoticTopImage
                     ->edit(['guide_notification_id' => $newId], $request['top_image']['id']);
            }

            // タグ付与
            if (isset($request['tag'])) {
                $tagData = [];
                foreach ($request['tag'] as $tagId => $val) {
                    $tagData[] = [
                        'tag_id' => $tagId,
                        'notification_id' => $newId,
                    ];
                }
                $this->guideNoticTagRef->insert($tagData);
            }

            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            Log::error(var_export($e->getMessage(), true));
        }

        return true;
    }

    /**
     * Update guide_notification to database
     * @param  array $request
     * @return boolean
     */
    public function edit($request)
    {
        // ---------
        // DB
        $oldGuideNotic = $this->getOneById($request['id'], $request['guideAppId']);
        // ---------
        if (empty($oldGuideNotic)) {
            return false;
        }

        // Request で値はチェック済み
        $editGuideNotic = [
            'guide_application_id'           => $request['guideAppId'],
            'title'                          => $request['title'],
            'guide_notification_category_id' => $request['guide_notification_category_id'],
            'is_priority'                    => empty($request['is_priority']) ? 0 : 1,
            'body'                           => $request['body'],
            'device'                         => $request['device'],
            'published_datetime'             => $request['published_datetime'],
            'stamp'                          => timestamp_to_sqldate(now_stamp()),
        ];
        Freegame::beginTransaction();
        try {
            // 更新内容をチェック
            if (empty($oldGuideNotic['is_priority']) && $request['is_priority']) {
                // 登録しない→登録する の場合、priorityの初期値を入れる
                // 既存データのpriorityを+1
                $this->guideNotic->editCountUpPriority($request['guideAppId']);
                $editGuideNotic['priority'] = 1;
            }
            // ---------
            // DB
            $this->guideNotic->edit($editGuideNotic, $request['id']);
            // ---------

            //　トップ画像更新
            $notificationId = $request['id'];
            $existingTopImage = $this->guideNoticTopImage->getOneByNoticId($notificationId);
            if (!empty($request['top_image']['id'])) {
                $topImageId = $request['top_image']['id'];

                // 既存の画像があり、既存の画像と送られてきた画像のidが異なる場合、既存の画像を削除
                if (isset($existingTopImage) && $existingTopImage->id != $topImageId) {
                    $this->deleteFileGuideNotic($existingTopImage->file_name, $request['guideAppId'], true);
                    $this->guideNoticTopImage->deleteByNotificationId($notificationId);
                }
                $this->guideNoticTopImage->edit(['guide_notification_id' => $notificationId], $topImageId);
            } elseif (isset($existingTopImage)) {
                // 既存の画像がある場合既存の画像を削除
                $this->deleteFileGuideNotic($existingTopImage->file_name, $request['guideAppId'], true);
                $this->guideNoticTopImage->deleteByNotificationId($notificationId);
            }

            // タグ付与
            $this->guideNoticTagRef->deleteByNotificationId($notificationId);
            if (isset($request['tag'])) {
                $tagData = [];
                foreach ($request['tag'] as $tagId => $val) {
                    $tagData[] = [
                        'tag_id' => $tagId,
                        'notification_id' => $notificationId,
                    ];
                }
                $this->guideNoticTagRef->insert($tagData);
            }

            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            Log::error(var_export($e->getMessage(), true));
        }

        return true;
    }

    /**
     * Update guide_notification to database
     * @param  integer $noticId
     * @param  integer $guideAppId
     * @return boolean
     */
    public function changeViewStatus($noticId, $guideAppId)
    {
        // ---------
        // DB
        $oldGuideNotic = $this->getOneById($noticId, $guideAppId);
        // ---------
        if (empty($oldGuideNotic)) {
            return false;
        }

        $viewStatus = 'display';
        if (strcmp($oldGuideNotic['view_status'], 'display') === 0) {
            $viewStatus = 'hide';
        }

        $editGuideNotic = [
            'view_status' => $viewStatus,
            'stamp'       => timestamp_to_sqldate(now_stamp()),
        ];
        // ---------
        // DB
        $this->guideNotic->edit($editGuideNotic, $noticId);
        // ---------

        return true;
    }

    /**
     * Delete content
     * @param  integer $id
     * @param  integer $guideAppId
     * @return boolean
     */
    public function deleteContent($id, $guideAppId)
    {
        // ---------
        // DB
        $oldGuideNotic = $this->getOneById($id, $guideAppId);
        // ---------
        if (empty($oldGuideNotic)) {
            return false;
        }

        Freegame::beginTransaction();
        try {
            // ---------
            // DB
            $this->guideNotic->del($id);
            // ---------

            // トップ画像の削除
            $topImg = $this->guideNoticTopImage->getOneByNoticId($id);
            if (isset($topImg)) {
                $this->deleteFileGuideNotic($topImg->file_name, $guideAppId, true);
                $this->guideNoticTopImage->deleteByNotificationId($id);
            }

            // タグ紐付け削除
            $this->guideNoticTagRef->deleteByNotificationId($id);

            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            Log::error(var_export($e->getMessage(), true));
        }

        return true;
    }

    /**
     * Edit  preview
     * @param  array $request
     * @return array $data
     */
    public function editPreview($request)
    {
        $guideAppId = $request['guideAppId'];
        $data = ['success' => false, 'preview_hash' => '', 'preview_url' => ''];
        $guideApplication = $this->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            $data['errors'][] = '運用サイト情報が取得できません。';
        } else {
            $data = $this->editNoticTmp($request, $guideApplication['domain']);
        }

        return $data;
    }
    /**
     * Edit  guide_notification_tmp table
     * @param  array $request
     * @param string $domain
     * @return boolean
     */
    private function editNoticTmp($request, $domain)
    {
        $guideAppId = $request['guideAppId'];

        // フォームの値にすでにハッシュがある場合、古いプレビューなので削除をする
        $previewHash = $request['preview_hash'];
        if (empty($previewHash) === false) {
            // ---------
            // DB
            $this->delPreview($guideAppId, $previewHash);
            // ---------
        }
        Freegame::beginTransaction();
        try {
            $topImageId = isset($request['top_image']['id']) ? $request['top_image']['id'] : null;
            $newTmp = [
                'guide_application_id'           => $guideAppId,
                'title'                          => $request['title'],
                'guide_notification_category_id' => $request['guide_notification_category_id'],
                'is_priority'                    => empty($request['is_priority']) ? 0 : 1,
                'body'                           => $request['body'],
                'device'                         => $request['device'],
                'published_datetime'             => $request['published_datetime'],
                'view_status'                    => $request['view_status'],
                'top_image_id'                   => $topImageId,
                'stamp'                          => timestamp_to_sqldate(now_stamp()),
            ];
            // ---------
            // DB
            $newId = $this->guideNoticTmp->insertGetId($newTmp);
            // ---------

            list($previewHash, $previewUrl) = $this->getPreviewHashAndUrl(
                'notification',
                $domain,
                '/id/' . $newId . '/type/notification/key'
            );

            // 作ったハッシュでさらに更新
            // ---------
            // DB
            $result = $this->guideNoticTmp->edit(
                ['preview_hash' => $previewHash],
                $newId
            );
            // ---------
            if ($result) {
                $data['success'] = true;
                $data['preview_hash'] = $previewHash;
                $data['preview_url'] = $previewUrl;
            } else {
                $data['success'] = false;
                $data['errors'][] = 'テンポラリデータの保存に失敗しました。';
            }
            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            Log::error(var_export($e->getMessage(), true));
            $data['success'] = false;
            $data['errors'][] = 'テンポラリデータの保存に失敗しました。';
        }
        return $data;
    }

    /**
     * Get  Preview Hash And Preview Url
     * @param  string $preSeed
     * @param  string $domain
     * @param  string $previewPath
     * @return array(
     *  string $previewHash
     *  string $previewUrl
     * )
     */
    private function getPreviewHashAndUrl($preSeed, $domain, $previewPath)
    {
        // SHA1ハッシュ化（ソルト値＋特定の値)
        $preHashSeed = $preSeed . date('YmdGis', now_stamp());
        $previewHash = hash(
            'sha1',
            env('AUTH_PASSWORD_SALT', 'SomeRandomString')
            . $preHashSeed
            . $preSeed
            . date('YmdGis', now_stamp())
        );
        // プレビューのurl生成
        $previewUrl = adjustment_path($domain);
        $previewUrl .= '/preview' . $previewPath . '/' . $previewHash . '/';
        return [$previewHash, $previewUrl];
    }

    /**
     * delete  preview to database
     * @param  integer $guideAppId
     * @param  string $previewHash
     * @return
     */
    public function delPreview($guideAppId, $previewHash)
    {
        return $this->guideNoticTmp->delByPreviewHash($guideAppId, $previewHash);
    }

    /**
     * Update priority of guide_notification to database
     * @param  array $request
     * @return boolean
     */
    public function priorityEdit($request)
    {
        $result = true;
        if (empty($request['notic'])) {
            return false;
        }

        Freegame::beginTransaction();
        try {
            foreach ($request['notic'] as $intKey => $notic) {
                $priority = $intKey + 1;
                $editGuideNotic = [
                    'priority'         => $priority,
                    'stamp' => timestamp_to_sqldate(now_stamp()),
                ];
                // ---------
                // DB
                $result = $this->guideNotic->edit($editGuideNotic, $notic['id']);
                // ---------
            }
            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            Log::error(var_export($e->getMessage(), true));
        }

        return $result;
    }

    /**
     * upload image files
     *
     * @param array $request
     * @return array
     */
    public function uploadFileGuideNotic($request)
    {
        $isTop = isset($request['top_image_file']);
        // 一時的なファイルを一旦アップロード作業用のフォルダに移動
        $objFile = ($isTop) ? $request['top_image_file'] : $request['image_file'];
        $imagePath = $objFile->getRealPath();

        $fileName = $objFile->getClientOriginalName();
        $fileName = $this->getUploadFileName($fileName);

        $uploadPath = $this->imageFilePath($request['guideAppId'], $isTop);

        // img-freegame にアップ
        $resultFile = $this->uploadFile($imagePath, $fileName, $uploadPath);

        // pics にアップ
        $resultFileToPics = $this->uploadFileToPics($imagePath, $fileName, $uploadPath);

        // ローカルの作業用ファイルを削除
        \File::delete($imagePath);

        // 両方に成功していたら 処理成功
        $success = false;
        if ($resultFile && $resultFileToPics) {
            $success = true;
        } else {
            $msg = '$imagePath=' . var_export($imagePath, true)
                    . ', $fileName=' . var_export($fileName, true)
                    . ', $uploadPath=' . var_export($uploadPath, true)
                    . ', $resultFile=' . var_export($resultFile, true)
                    . ', $resultFileToPics=' . var_export($resultFileToPics, true);
            Log::error($msg);
        }

        // ファイルがアップ成功していたらDBにも保存
        $imgId = '';
        if ($success) {
            $newNoticImage['guide_application_id'] = $request['guideAppId'];

            if ($isTop) {
                // 編集時も お知らせID 0で一時保存
                $newNoticImage['guide_notification_id'] = 0;
                $newNoticImage['file_name'] = $fileName;
                $newNoticImage['created_at'] = Carbon::now();
                $imgId = $this->guideNoticTopImage->insertGetId($newNoticImage);
            } else {
                $newNoticImage['guide_notification_id'] = (empty($request['id'])) ? 0 : $request['id'];
                $newNoticImage['image'] = $fileName;
                $newNoticImage['stamp'] = timestamp_to_sqldate(now_stamp());
                $imgId = $this->guideNoticImage->insertGetId($newNoticImage);
            }
        }

        $data = [
            'files' => [
                'file' => [
                    'name' => $fileName,
                    'id' => $imgId,
                    'guideappid' => $request['guideAppId'],
                    'success' => $success,
                ]
            ]
        ];
        return $data;
    }

    /**
     * delete image files
     *
     * @param array $request
     * @return array
     */
    public function delFileGuideNotic($request)
    {
        $isTop = ($request['isTop'] == true) ? true : false;
        $imgName = $request['image'];

        if ($isTop) {
            $topImage = $this->guideNoticTopImage->getOneById($request['imgId']);
            // 一時保存画像の場合のみ削除
            if (isset($topImage) && $topImage->guide_notification_id == 0) {
                $success = $this->deleteFileGuideNotic($request['image'], $request['guideAppId'], $isTop);
            } else {
                $success = true;
            }
        } else {
            $success = $this->deleteFileGuideNotic($request['image'], $request['guideAppId'], $isTop);
        }

        if ($success && isset($request['imgId'])) {
            // テーブルから削除
            $imgId = $request['imgId'];
            if ($isTop) {
                // 一時保存画像の場合のみ削除
                $this->guideNoticTopImage->deleteTmpById($imgId);
            } else {
                $this->guideNoticImage->del($imgId);
            }
        }

        $data = [
            'success' => $success,
        ];
        return $data;
    }

    /**
     * delete image files after hour
     *
     * @return integer
     */
    public function delFileTmpImage()
    {
        $noticImage = $this->guideNoticImage->getListByAfterHour();
        if (count($noticImage)) {
            $idList = [];
            foreach ($noticImage as $image) {
                $this->deleteFileGuideNotic($image['image'], $image['guide_application_id'], false);
                $idList[] = $image['id'];
            }
            $this->guideNoticImage->delByIdList($idList);
        }

        $noticImage = $this->guideNoticTopImage->getListByAfterHour();
        if (count($noticImage)) {
            $idList = [];
            foreach ($noticImage as $image) {
                $this->deleteFileGuideNotic($image['file_name'], $image['guide_application_id'], true);
                $idList[] = $image['id'];
            }
            $this->guideNoticTopImage->deleteByIdList($idList);
        }
        return count($noticImage);
    }

    private function imageFilePath($guideAppId, $isTop)
    {
        return 'guide/' . $guideAppId . '/' . (($isTop) ? self::TOP_IMAGE_DIRECTORY : self::IMAGES_DIRECTORY) . '/';
    }
    private function getUploadFileName($originFileName)
    {
        $str = preg_split("/\./", $originFileName);
        return sprintf('%s.%s', $this->makeRandImageName(), $str[1]);
    }

    private function deleteFileGuideNotic($image, $guideAppId, $isTop)
    {
        // img-freegame から削除
        $uploadFile = $this->imageFilePath($guideAppId, $isTop) . $image;
        $resultFile = $this->deleteFile($uploadFile);

        // pics から削除
        $uploadFile = $this->imageFilePath($guideAppId, $isTop) . $image;
        $resultFileToPics = $this->deleteFileToPics($uploadFile);

        // 両方に成功していたら 削除成功 とする
        $success = false;
        if ($resultFile && $resultFileToPics) {
            $success = true;
        } else {
            $msg = '$uploadFile=' . var_export($uploadFile, true)
                    . ', $resultFile=' . var_export($resultFile, true)
                    . ', $resultFileToPics=' . var_export($resultFileToPics, true);
            Log::error($msg);
        }
        return $success;
    }

    /**
     * save And get search condition
     * @return boolean
     */
    public function formatSearchCondition($search = [])
    {
        if (request()->has('search')) {
            $search = session('GuideNotification.search', []);
            request()->merge($search);
        }
        $params = array_only($search, [
            'searchWord',
            'searchPriority',
            'perPage',
            'page',
        ]);
        $search = array_filter($params, function ($item) {
            return is_array($item) || $item || is_numeric($item);
        });
        request()->session()->set('GuideNotification.search', $search);
        return $search;
    }

    /**
     * Check Edit Permission
     * @return boolean
     */
    public function isEnableEdit($guideAppId)
    {
        $userId = auth_user_id();
        if (empty($userId)) {
            return false;
        }
        if (auth_is_pf()) {
            return true;
        } else {
            $idList = $this->devGuideApp->getListGuideAppIdByDevId($userId);
            if (in_array($guideAppId, $idList->toArray())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get values
     *
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.GuideNotification');

        return [
            'screenName'  => $privateConfigs['screenName'],
            'menuName'  => $privateConfigs['menuName'],
            'frontNoticDetailPath' => $privateConfigs['frontNoticDetailPath'],
            'imageBasePath' => env('HTTP_IMG_FREEGAMES_URL', 'http://localhost') . '/guide',
            'imagesDirectory' => self::IMAGES_DIRECTORY,
            'topImageDirectory' => self::TOP_IMAGE_DIRECTORY,
        ];
    }

    /**
     * タグ一覧取得
     * @param integer $guideAppId
     * @return array
     */
    public function getTagList($guideAppId)
    {
        return $this->guideNoticTag->getListByGuideAppId($guideAppId)
            ->keyBy('id')
            ->toArray();
    }

    /**
     * タグ一覧検索
     * @param array $params
     * @return array
     */
    public function searchTagList($params)
    {
        if (!isset($params['perPage'])) {
            $params['perPage'] = config('forms.GuideNotificationTag.perPage');
        }
        return $this->guideNoticTag->search($params);
    }

    /**
     * データ表示順テーブル取得
     * @param integer $guideAppId
     * @param integer $editId 編集中データID(新規登録時はnull)
     * @return array
     */
    public function getNotificationTagOrderList($guideAppId, $editId = null)
    {
        $currentOrderList = $this->guideNoticTag->getOrderList($guideAppId)->keyBy('order_no');

        $orderList = [];
        // 新規登録の場合
        if (is_null($editId)) {
            $orderList[1] = '先頭に追加';

            $currentOrderList = $currentOrderList->toArray();
            if (empty($currentOrderList)) {
                return $orderList;
            }

            foreach ($currentOrderList as $orderNo => $orderTag) {
                if ($orderNo != 1) {
                    $orderList[$orderNo] = $orderTag['name'] . 'の前に追加';
                }
            }
            $orderList[-1] = '末尾に追加';
        } else { // 編集の場合
            $lastNo = $currentOrderList->last()->toArray()['order_no'];
            $editTag = $currentOrderList->where('id', $editId)->first()->toArray();
            $currentOrderList = $currentOrderList->toArray();

            foreach ($currentOrderList as $orderNo => $orderTag) {
                if ($orderTag['id'] == $editTag['id']) {
                    $orderList[0] = '移動しない';
                    continue;
                }
    
                if ($orderNo == ($editTag['order_no'] + 1)) {
                    continue;
                }

                if ($orderNo == 1) {
                    $orderList[1] = '先頭に移動';
                } else {
                    $orderList[$orderNo] = $orderTag['name'] . 'の前に移動';
                }
            }

            if ($editTag['order_no'] != $lastNo) {
                $orderList[-1] = '末尾に移動';
            }
        }
        return $orderList;
    }

    /**
     * タグ名のユニーク確認
     * @param integer $guideAppId
     * @param string $name
     * @param integer $ignoreId
     * @return boolean
     */
    public function isUniqueTagName($guideAppId, $name, $ignoreId = null)
    {
        return !$this->guideNoticTag->existsName($guideAppId, $name, $ignoreId);
    }

    /**
     * タグキーのユニーク確認
     * @param integer $guideAppId
     * @param string $tagKey
     * @param integer $ignoreId
     * @return boolean
     */
    public function isUniqueTagKey($guideAppId, $tagKey, $ignoreId = null)
    {
        return !$this->guideNoticTag->existsTagKey($guideAppId, $tagKey, $ignoreId);
    }

    /**
     * お知らせタグー新規登録
     * @param Request $request
     * @return boolean
     */
    public function createTag($request)
    {
        GuideNotificationTag::beginTransaction();
        try {
            $order = $request['order_no'];
            $guideAppId = $request['guideAppId'];

            $data = [
                'guide_application_id' => $guideAppId,
                'name' => $request['name'],
                'tag_key' => $request['tag_key'],
            ];

            // 末尾へ追加する場合
            if ($order == -1) {
                $data['order_no'] = $this->guideNoticTag->getLastOrder($guideAppId) + 1;
            } else { // 末尾以外に追加する場合
                $data['order_no'] = $order;
                $this->guideNoticTag->incrementOrder($guideAppId, $order);
            }
            $this->guideNoticTag->insert($data);
        } catch (Exception $e) {
            GuideNotificationTag::rollback();
            Log::error(var_export($e->getMessage(), true));
            throw $e;
        }
        GuideNotificationTag::commit();

        return true;
    }

    /**
     * お知らせタグ更新
     * @param Request $request
     * @return boolean
     */
    public function updateTag($request)
    {
        GuideNotificationTag::beginTransaction();
        try {
            $guideAppId = $request['guideAppId'];

            $data = [
                'name' => $request['name'],
                'tag_key' => $request['tag_key'],
            ];

            $newOrder = $request['order_no'];
            if ($newOrder != 0) {
                // 末尾へ移動する場合
                if ($newOrder == -1) {
                    $newOrder = $this->guideNoticTag->getLastOrder($guideAppId) + 1;
                }

                $tag = $this->guideNoticTag->getOne($request['id']);
                $currentOrder = $tag['order_no'];

                // 前に移動する場合
                if ($newOrder < $currentOrder) {
                    $this->guideNoticTag->incrementOrder($guideAppId, $newOrder, $currentOrder);
                    $data['order_no'] = $newOrder;
                } else { // 後ろに移動する場合
                    $this->guideNoticTag->decrementOrder($guideAppId, $currentOrder, $newOrder);
                    $data['order_no'] = $newOrder -1;
                }
            }

            $this->guideNoticTag->edit($data, $request['id']);
        } catch (Exception $e) {
            GuideNotificationTag::rollback();
            Log::error(var_export($e->getMessage(), true));
            throw $e;
        }
        GuideNotificationTag::commit();

        return true;
    }

    /**
     * お知らせタグ情報取得
     * @param integer $masterId
     * @return array
     */
    public function getTagData($tagId)
    {
        $collection = collect($this->guideNoticTag->getOne($tagId));
        return $collection->toArray();
    }

    /**
     * お知らせタグ削除
     * @param integer $tagId
     * @return boolean
     */
    public function deleteTag($tagId)
    {
        GuideNotificationTag::beginTransaction();
        GuideNotificationTagRef::beginTransaction();
        try {
            $tagData = $this->getTagData($tagId);
            $this->guideNoticTag->destroy($tagId);
            $this->guideNoticTag->decrementOrder($tagData['guide_application_id'], $tagData['order_no']);
            $this->guideNoticTagRef->deleteByTagId($tagId);
        } catch (Exception $e) {
            GuideNotificationTag::rollback();
            GuideNotificationTagRef::rollback();
            Log::error(var_export($e->getMessage(), true));
            throw $e;
        }
        GuideNotificationTag::commit();
        GuideNotificationTagRef::commit();

        return true;
    }
}
