<?php

namespace App\Services;

use App\Models\FreegameGuide\GuideApplication;
use App\Models\FreegameDeveloper\DeveloperGuideApplication;
use App\Models\FreegameGuide\GuidePreregistrationUser;
use App\Models\FreegameGuide\GuidePreregistrationTwitterUser;
use App\Models\FreegameGuide\GuideTwitterApplication;
use App\Models\FreegameGuide\GuidePreregistrationUserCondition;
use \Exception;

class GuidePreregistrationService extends CustomService
{
    protected $guideApp;
    protected $devGuideApp;
    protected $guidePreregistUser;
    protected $guidePreregistTwitterUser;
    protected $guideTwitterApplication;
    protected $guidePreregistUserCondition;

    public function __construct(
        GuideApplication $guideApp,
        DeveloperGuideApplication $devGuideApp,
        GuidePreregistrationUser $guidePreregistUser,
        GuidePreregistrationTwitterUser $guidePreregistTwitterUser,
        GuideTwitterApplication $guideTwitterApplication,
        GuidePreregistrationUserCondition $guidePreregistUserCondition
    ) {
        $this->guideApp = $guideApp;
        $this->devGuideApp = $devGuideApp;
        $this->guidePreregistUser = $guidePreregistUser;
        $this->guidePreregistTwitterUser = $guidePreregistTwitterUser;
        $this->guideTwitterApplication = $guideTwitterApplication;
        $this->guidePreregistUserCondition = $guidePreregistUserCondition;
    }

    /**
     * Get count by preregist users
     * @param  integer $guideAppId
     * @return array
     */
    public function getCountUser($guideAppId)
    {
        $preregistUserCnt = $this->guidePreregistUser->getCountByUser($guideAppId);
        $preregistTwitterUserCnt = $this->guidePreregistTwitterUser->getCountByUser($guideAppId);

        return [
            'userCnt' => $preregistUserCnt,
            'twitterUserCnt' => $preregistTwitterUserCnt,
        ];
    }

    /**
     * Get guide_preregistration_user_condition by guide_application_id
     * @param  integer $guideAppId
     * @return array
     */
    public function getOneGuidePreregistCondition($guideAppId)
    {
        // ---------
        // DB
        $guidePreregistUserCondition = $this->guidePreregistUserCondition->getOneByGuideAppId($guideAppId);
        // ---------
        return empty($guidePreregistUserCondition) ?
            $guidePreregistUserCondition : $guidePreregistUserCondition->toArray();
    }

    /**
     * Get guide_twitter_application by guide_application_id
     * @param  integer $guideAppId
     * @return array
     */
    public function getOneGuideTwitterApplication($guideAppId)
    {
        // ---------
        // DB
        $guideTwitterApp = $this->guideTwitterApplication->getOneByGuideAppId($guideAppId);
        // ---------
        return empty($guideTwitterApp) ? $guideTwitterApp : $guideTwitterApp->toArray();
    }

    /**
     * Get guide_application by id
     * @param  integer $id
     * @return array
     */
    public function getOneGuideApplication($id)
    {
        // ---------
        // DB
        $guideApplication = $this->guideApp->getOne($id);
        // ---------
        return empty($guideApplication) ? $guideApplication : $guideApplication->toArray();
    }

    /**
     * Update guide_twitter_application to database
     * @param  array $request
     * @return boolean
     */
    public function twitterapiEdit($request)
    {
        // Request で値はチェック済み
        $editParams = [
            'is_use_twitter_application' => $request['is_use_twitter_application'],
            'guide_application_id' => $request['guideAppId'],
            'twitter_api_key' => $request['twitter_api_key'],
            'twitter_secret_key' => $request['twitter_secret_key'],
            'owner' => $request['owner'],
            'owner_id' => $request['owner_id'],
            'start_at' => $request['start_at'],
            'end_at' => $request['end_at']
        ];
        // ---------
        // DB
        if (empty($request['id'])) {
            $editParams['created_at'] = timestamp_to_date(now_stamp());
        }
        $result = $this->guideTwitterApplication->edit($editParams, $request['guideAppId']);
        // ---------

        return $result;
    }

    /**
     * Update guide_preregistration_user_condition to database
     * @param  array $request
     * @return boolean
     */
    public function userconditionEdit($request)
    {
        // Request で値はチェック済み
        $editParams = [
            'is_regist' => $request['is_regist'],
            'guide_application_id' => $request['guideAppId'],
            'start_at' => $request['start_at'],
            'end_at' => $request['end_at']
        ];
        // ---------
        // DB
        if (empty($request['id'])) {
            $editParams['created_at'] = timestamp_to_date(now_stamp());
        }
        $result = $this->guidePreregistUserCondition->edit($editParams, $request['guideAppId']);
        // ---------

        return $result;
    }

    /**
     * download csv file for guide_preregistration_user
     *
     * @param integer $guideAppId
     * @param string $fileName
     * @return array
     */
    public function downloadCsvPreregistUser($guideAppId, $fileName)
    {
        $column = [
            'user_id' => 'ユーザーID',
            'created_at' => '登録日',
        ];
        // ---------
        // DB
        $detailList = $this->guidePreregistUser->getListByGuideAppId($guideAppId);
       // ---------

        $contents = [];
        foreach ($detailList as $row) {
            $data = [
                'user_id' => $row['user_id'],
                'created_at' => $row['created_at'],
            ];
            $contents[] = $data;
        }

        return $this->downloadCsv($fileName, $contents, $column, true);
    }

    /**
     * Check Edit Permission
     * @return boolean
     */
    public function isEnableEdit($guideAppId)
    {
        $userId = auth_user_id();
        if (empty($userId)) {
            return false;
        }
        if (auth_is_pf()) {
            return true;
        } else {
            $idList = $this->devGuideApp->getListGuideAppIdByDevId($userId);
            if (in_array($guideAppId, $idList->toArray())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get values
     *
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.GuidePreregistration');

        return [
            'screenName'  => $privateConfigs['screenName'],
            'menuName'  => $privateConfigs['menuName'],
        ];
    }
}
