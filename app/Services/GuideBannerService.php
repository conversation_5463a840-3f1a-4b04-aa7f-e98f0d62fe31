<?php

namespace App\Services;

use App\Models\FreegameGuide\GuideApplication;
use App\Models\FreegameDeveloper\DeveloperGuideApplication;
use App\Models\FreegameGuide\GuideBanner;
use App\Models\FreegameGuide\GuideFixedBanner;
use App\Models\FreegameGuide\GuideRotatingBanner;
use \Exception;
use App\Models\Freegame\Freegame;

class GuideBannerService extends CustomService
{
    protected $guideApp;
    protected $devGuideApp;
    protected $guideBanner;
    protected $guideFixedBanner;
    protected $guideRotatingBanner;

    public function __construct(
        GuideApplication $guideApp,
        DeveloperGuideApplication $devGuideApp,
        GuideBanner $guideBanner,
        GuideFixedBanner $guideFixedBanner,
        GuideRotatingBanner $guideRotatingBanner
    ) {
        $this->guideApp = $guideApp;
        $this->devGuideApp = $devGuideApp;
        $this->guideBanner = $guideBanner;
        $this->guideFixedBanner = $guideFixedBanner;
        $this->guideRotatingBanner = $guideRotatingBanner;
    }

    /**
     * Get guide_banner List
     * @param  integer $guideAppId
     * @return array
     */
    public function getList($guideAppId)
    {
        $guideBannerList = $this->guideBanner->getListByGuideAppId($guideAppId);

        return $guideBannerList->toArray();
    }

    /**
     * Get guide_banner by id
     * @param  integer $id
     * @param  integer $guideAppId
     * @return array
     */
    public function getOne($id, $guideAppId)
    {
        $getList = $this->guideBanner->getOne($id, $guideAppId);
        return empty($getList) ? $getList : $getList->toArray();
    }

    /**
     * Get guide_application by id
     * @param  integer $id
     * @return array
     */
    public function getOneGuideApplication($id)
    {
        // ---------
        // DB
        $guideApplication = $this->guideApp->getOne($id);
        // ---------
        return empty($guideApplication) ? $guideApplication : $guideApplication->toArray();
    }

    /**
     * Insert guide_banner to database
     * @param  array $request
     * @return boolean
     */
    public function create($request)
    {
        // アップロード処理
        $request['image'] = $this->uploadFileGuideBanner($request);

        // Request で値はチェック済み
        $newGuideBanner = [
            'guide_application_id' => $request['guideAppId'],
            'name'                 => $request['name'],
            'image'                => $request['image'],
            'link'                 => $request['link'],
            'start_datetime'       => $request['start_datetime'],
            'end_datetime'         => $request['end_datetime'],
        ];
        // ---------
        // DB
        $result = $this->guideBanner->insert($newGuideBanner);
        // ---------

        return $result;
    }

    /**
     * Update guide_banner to database
     * @param  array $request
     * @return boolean
     */
    public function edit($request)
    {
        // ---------
        // DB
        $oldGuideBanner = $this->getOne($request['id'], $request['guideAppId']);
        // ---------
        if (empty($oldGuideBanner)) {
            return false;
        }

        // アップロードファイルがある場合、アップロード処理
        if (empty($request['uploadImage_encode']) === false) {
            $this->deleteFileGuideBanner($oldGuideBanner['image'], $request['guideAppId']);
            $request['image'] = $this->uploadFileGuideBanner($request);
        }

        // Request で値はチェック済み
        $editGuideBanner = [
            'name'           => $request['name'],
            'link'           => $request['link'],
            'start_datetime' => $request['start_datetime'],
            'end_datetime'   => $request['end_datetime'],
        ];
        // アップロードファイルがある場合、更新
        if (empty($request['image']) === false) {
            $editGuideBanner['image'] = $request['image'];
        }
        // ---------
        // DB
        $result = $this->guideBanner->edit($editGuideBanner, $request['id']);
        // ---------

        return $result;
    }

    /**
     * Delete content
     * @param  integer $id
     * @param  integer $guideAppId
     * @return boolean
     */
    public function deleteContent($id, $guideAppId)
    {
        // ---------
        // DB
        $oldGuideBanner = $this->getOne($id, $guideAppId);
        // ---------
        if (empty($oldGuideBanner)) {
            return false;
        }

        // ---------
        // DB
        Freegame::beginTransaction();
        try {
            $this->guideBanner->del($id);

            // 紐付く表示設定も一緒に消す
            $this->guideFixedBanner->delByGuideBannerId($id);
            $this->guideRotatingBanner->delByGuideBannerId($id);
            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            throw $e;
        }
        // ---------

        $this->deleteFileGuideBanner($oldGuideBanner['image'], $guideAppId);

        return true;
    }

    /**
     * upload image files
     *
     * @param array $request
     * @return $fileName
     */
    public function uploadFileGuideBanner($request)
    {
        $tmpFile = tmpfile();
        $tmpPath = stream_get_meta_data($tmpFile)['uri'];
        $contents = base64_decode($request['uploadImage_encode']);
        fwrite($tmpFile, $contents);

        $extension = $request['uploadImage_extension'];
        $fileName = $this->makeRandImageName() . '.' . $extension;

        // img-freegame にアップ
        $uploadPath = $this->imageFilePath($request['guideAppId']);
        $this->uploadFile($tmpPath, $fileName, $uploadPath);

        // pics にアップ
        $uploadPath = $this->imageFilePath($request['guideAppId']);
        $this->uploadFileToPics($tmpPath, $fileName, $uploadPath);

        fclose($tmpFile);

        return $fileName;
    }

    /**
     * delete image files
     *
     * @param string $image
     * @param integer $guideAppId
     * @return void
     */
    public function deleteFileGuideBanner($image, $guideAppId)
    {
        // img-freegame からさ駆除
        $uploadFile = $this->imageFilePath($guideAppId) . $image;
        $this->deleteFile($uploadFile);

        // pics から削除
        $uploadFile = $this->imageFilePath($guideAppId) . $image;
        $this->deleteFileToPics($uploadFile);
    }
    private function imageFilePath($guideAppId)
    {
        return 'guide/' . $guideAppId . '/banner/';
    }

    /**
     * Check Edit Permission
     * @return boolean
     */
    public function isEnableEdit($guideAppId)
    {
        $userId = auth_user_id();
        if (empty($userId)) {
            return false;
        }
        if (auth_is_pf()) {
            return true;
        } else {
            $idList = $this->devGuideApp->getListGuideAppIdByDevId($userId);
            if (in_array($guideAppId, $idList->toArray())) {
                return true;
            }
        }
        return false;
    }

    /**
     * upload files encoding
     *
     * @param  array $files
     * @return boolean
     */
    public function encodeUploadFiles($files = [])
    {
        foreach ($files as $key => $file) {
            if (isset($file) && is_object($file) && $file->isValid()) {
                $tmpFile = fopen($file->getRealPath(), 'rb');
                $contents = fread($tmpFile, $file->getSize());
                fclose($tmpFile);
                $contents = base64_encode($contents);
                request()->merge([
                    $key . '_encode'    => $contents,
                    $key . '_extension' => $file->getClientOriginalExtension(),
                    $key . '_mimetype'  => $file->getClientMimeType(),
                ]);
            }
        }
        return true;
    }

    /**
     * Get values
     *
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.GuideBanner');

        return [
            'screenName'  => $privateConfigs['screenName'],
            'menuName'  => $privateConfigs['menuName'],
        ];
    }
}
