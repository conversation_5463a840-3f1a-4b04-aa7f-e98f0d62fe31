<?php
namespace App\Services;

use App\Models\Freegame\Application;
use App\Models\Freegame\ApplicationApk;
use App\Models\Freegame\ApplicationApkCloud;
use App\Models\Freegame\ApplicationEmulatorApk;
use App\Models\Freegame\ApplicationEmulatorKeyMapping;
use App\Models\Freegame\ApplicationEmulatorOfficialSiteUrl;
use App\Models\Freegame\ApplicationCdnGameStore;
use App\Models\Freegame\ApplicationCdnEmulator;
use App\Models\Freegame\ApplicationDevice;
use App\Models\Freegame\ApplicationLatestInfo;
use App\Models\Freegame\ApplicationTwitter;
use App\Models\Freegame\ApplicationGenreRef;
use App\Models\Freegame\ApplicationMainGenre;
use App\Models\Freegame\ApplicationSpMenu;
use App\Models\Freegame\ApplicationSubGenre;
use App\Models\Freegame\ApplicationTagRef;
use App\Models\Freegame\ApplicationTag;
use App\Models\Freegame\ApplicationProducerLabelRef;
use App\Models\Freegame\Producer;
use App\Models\Freegame\ApplicationPwaSettings;
use App\Models\Freegame\Label;
use App\Models\Freegame\ApplicationPolicy;
use App\Models\FreegameDeveloper\ApplicationDetail;
use App\Models\FreegameDeveloper\ApplicationImage;
use App\Models\FreegameDeveloper\DeveloperApplication;
use App\Models\Freegame\Freegame;
use App\Models\Freegame\ApplicationProtocol;
use App\Models\Freegame\ApplicationTitleGroupBySite;
use App\Models\Freegame\ApplicationMetaData;
use App\Exceptions\FileUploadException;
use stdClass;
use Exception;
use Log;
use Carbon\Carbon;
use Storage;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use \ApkParser\Parser as ApkParser;

/**
 * ゲーム登録情報
 */
class GamesService extends CustomService
{

    protected $application;

    protected $applicationApk;

    protected $applicationApkCloud;

    protected $applicationEmulatorApk;

    protected $applicationEmulatorKeyMapping;

    protected $applicationEmulatorOfficialSiteUrl;

    protected $applicationCdnGameStore;

    protected $applicationCdnEmulator;

    protected $applicationDevice;

    protected $applicationLatestInfo;

    protected $applicationTwitter;

    protected $applicationGenreRef;

    protected $applicationMainGenre;

    protected $applicationSpMenu;

    protected $applicationSubGenre;

    protected $applicationTagRef;

    protected $applicationTag;

    protected $applicationProducerLabelRef;

    protected $producer;

    protected $applicationPwaSettings;

    protected $label;

    protected $applicationPolicy;

    protected $applicationDetail;

    protected $applicationImage;

    protected $developerApplication;

    protected $appTitleType;

    protected $genreNameType;

    protected $producerNameType;

    protected $labelNameType;

    protected $tagNameType;

    protected $applicationProtocol;

    protected $applicationTitleGroupBySite;

    /** @var ReceiptApiService */
    protected $receiptApiService;

    /** @var GamePlayerApiService */
    protected $gameplayerApiService;

    protected $applicationMetaData;

    public function __construct(
        Application $application,
        ApplicationApk $applicationApk,
        ApplicationApkCloud $applicationApkCloud,
        ApplicationEmulatorApk $applicationEmulatorApk,
        ApplicationEmulatorKeyMapping $applicationEmulatorKeyMapping,
        ApplicationEmulatorOfficialSiteUrl $applicationEmulatorOfficialSiteUrl,
        ApplicationCdnGameStore $applicationCdnGameStore,
        ApplicationCdnEmulator $applicationCdnEmulator,
        ApplicationDevice $applicationDevice,
        ApplicationLatestInfo $applicationLatestInfo,
        ApplicationTwitter $applicationTwitter,
        ApplicationGenreRef $applicationGenreRef,
        ApplicationMainGenre $applicationMainGenre,
        ApplicationSubGenre $applicationSubGenre,
        ApplicationTagRef $applicationTagRef,
        ApplicationTag $applicationTag,
        ApplicationProducerLabelRef $applicationProducerLabelRef,
        Producer $producer,
        ApplicationPwaSettings $applicationPwaSettings,
        Label $label,
        ApplicationPolicy $applicationPolicy,
        ApplicationDetail $applicationDetail,
        ApplicationImage $applicationImage,
        DeveloperApplication $developerApplication,
        ApplicationProtocol $applicationProtocol,
        ApplicationSpMenu $applicationSpMenu,
        ReceiptApiService $receiptApiService,
        GamePlayerApiService $gameplayerApiService,
        ApplicationTitleGroupBySite $applicationTitleGroupBySite,
        ApplicationMetaData $applicationMetaData
    ) {
        $this->application                        = $application;
        $this->applicationApk                     = $applicationApk;
        $this->applicationApkCloud                = $applicationApkCloud;
        $this->applicationEmulatorApk             = $applicationEmulatorApk;
        $this->applicationEmulatorKeyMapping      = $applicationEmulatorKeyMapping;
        $this->applicationEmulatorOfficialSiteUrl = $applicationEmulatorOfficialSiteUrl;
        $this->applicationCdnGameStore            = $applicationCdnGameStore;
        $this->applicationCdnEmulator             = $applicationCdnEmulator;
        $this->applicationDevice                  = $applicationDevice;
        $this->applicationLatestInfo              = $applicationLatestInfo;
        $this->applicationTwitter                 = $applicationTwitter;
        $this->applicationGenreRef                = $applicationGenreRef;
        $this->applicationMainGenre               = $applicationMainGenre;
        $this->applicationSubGenre                = $applicationSubGenre;
        $this->applicationTagRef                  = $applicationTagRef;
        $this->applicationTag                     = $applicationTag;
        $this->applicationProducerLabelRef        = $applicationProducerLabelRef;
        $this->producer                           = $producer;
        $this->applicationPwaSettings             = $applicationPwaSettings;
        $this->label                              = $label;
        $this->applicationPolicy                  = $applicationPolicy;
        $this->applicationDetail                  = $applicationDetail;
        $this->applicationImage                   = $applicationImage;
        $this->developerApplication               = $developerApplication;
        $this->applicationProtocol                = $applicationProtocol;
        $this->applicationSpMenu                  = $applicationSpMenu;
        $this->receiptApiService                  = $receiptApiService;
        $this->gameplayerApiService               = $gameplayerApiService;
        $this->applicationTitleGroupBySite        = $applicationTitleGroupBySite;
        $this->applicationMetaData                = $applicationMetaData;
    }

    public function getMessage($code = null)
    {
        $list = [
            'apkcdn.beforeDelete' => 'CDNキャッシュ' . config('forms.common.suffixBeforeDelete'),
            'apkcdn.afterDelete' => 'CDNキャッシュ' . config('forms.common.suffixAfterDelete'),
            'emulatorapkcdn.beforeDelete' => 'EmulatorCDNキャッシュ' . config('forms.common.suffixBeforeDelete'),
            'emulatorapkcdn.afterDelete' => 'EmulatorCDNキャッシュ' . config('forms.common.suffixAfterDelete'),
            'latest.beforeDelete' => '新着情報' . config('forms.common.suffixBeforeDelete'),
            'latest.afterDelete' => '新着情報' . config('forms.common.suffixAfterDelete')
        ];
        if (isset($code)) {
            return $list[$code];
        }
        return $list;
    }

    public function getFormData($action = 'index')
    {
        $list = [
            'menuName' => config('forms.Games.menuName'),
            'screenName' => config('forms.Games.screenName'),
            'subScreenName' => config('forms.Games.subScreenName'),
            'siteType' => config('forms.Games.siteType'),
            'twitterFollowType' => config('forms.Games.twitterFollowType'),
            'lusterType' => config('forms.Games.lusterType'),
            'emulatorKeyMappingIsEnable' => config('forms.Games.emulatorKeyMappingIsEnable'),
            'qrCodeQuery' => config('forms.Games.qrCodeQuery'),
            'appTitleType' => $this->getAppTitleType(),
            'message' => $this->getMessage(),
            'defaultSignature' => config('forms.Games.defaultSignature'),
            'fileSizeMax' => config('forms.Games.fileSizeMax'),
            'tabList' => config('forms.Games.tabList'),
        ];
        if ($action == 'index') {
            $list += [
                'genreNameType' => $this->getGenreNameType(),
                'producerNameType' => $this->getProducerNameType(),
                'labelNameType' => $this->getLabelNameType(),
                'tagNameType' => $this->getTagNameType()
            ];
        } elseif (str_is('basic*', $action)) {
            $list += [
                'genreNameType' => $this->getGenreNameType()
            ];
        }
        return $list;
    }

    public function getAppTitleType()
    {
        if (isset($this->appTitleType)) {
            return $this->appTitleType;
        }
        if (auth_is_sap()) {
            $devAppList = $this->developerApplication->getApplicationAppIdList([
                'developer_id' => auth_user_id()
            ]);
            if (empty($devAppList->count())) {
                return [];
            } else {
                foreach ($devAppList as $data) {
                    $condition['id'][] = $data->app_id;
                }
                $list = $this->application->getApplicationTitleList($condition);
            }
        } else {
            $list = $this->application->getApplicationTitleList();
        }
        $opts = [];
        foreach ($list as $data) {
            $opts[$data->id] = $data->title;
        }
        $this->appTitleType = $opts;
        return $opts;
    }

    public function getGenreNameType()
    {
        if (isset($this->genreNameType)) {
            return $this->genreNameType;
        }
        $list = $this->applicationMainGenre->getNameList();
        $subList = $this->applicationSubGenre->getNameList();
        $opts = [];
        foreach ($list as $data) {
            $opts[$data->id] = $data->name;
        }
        foreach ($subList as $data) {
            if (isset($opts[$data->main_genre_id])) {
                $opts[$data->main_genre_id . '-' . $data->id] = sprintf(
                    '%s（%s）',
                    $opts[$data->main_genre_id],
                    $data->name
                );
            }
        }
        $this->genreNameType = $opts;
        return $opts;
    }

    public function getProducerNameType()
    {
        if (isset($this->producerNameType)) {
            return $this->producerNameType;
        }
        $list = $this->producer->getNameList();
        $opts = [];
        foreach ($list as $data) {
            $opts[$data->id] = $data->name;
        }
        $this->producerNameType = $opts;
        return $opts;
    }

    public function getLabelNameType()
    {
        if (isset($this->labelNameType)) {
            return $this->labelNameType;
        }
        $list = $this->label->getNameList();
        $opts = [];
        foreach ($list as $data) {
            $opts[$data->id] = $data->name;
        }
        $this->labelNameType = $opts;
        return $opts;
    }

    public function getTagNameType()
    {
        if (isset($this->tagNameType)) {
            return $this->tagNameType;
        }
        $list = $this->applicationTag->getNameList();
        $opts = [];
        foreach ($list as $data) {
            $opts[$data->id] = $data->name;
        }
        $this->tagNameType = $opts;
        return $opts;
    }

    public function formatSearchCondition($search = [])
    {
        if (request()->has('search')) {
            $search = session('Games.search', []);
            request()->merge($search);
        }
        $search = array_only($search, [
            'app_id',
            'page'
        ]);
        request()->session()->set('Games.search', $search);
        return $search;
    }

    public function formatSearchQuery($search = [])
    {
        $appTitleType = $this->getAppTitleType();
        if (empty($search['app_id']) || ! isset($appTitleType[$search['app_id']])) {
            if (auth_is_sap()) {
                $search['app_id'] = array_keys($appTitleType);
                if (empty($search['app_id'])) {
                    $search['app_id'] = '-1';
                }
            } else {
                $search['app_id'] = '';
            }
        }
        $search = array_only($search, [
            'app_id',
            'perPage',
            'page'
        ]);

        return $search;
    }

    public function getList($condition = [])
    {
        $condition['perPage'] = config('forms.Games.perPage');
        $configGuestPlay = config('forms.Games.guestPlay');
        $configBrowserSdk = config('forms.Games.browserSdk');
        $condition['page']    = 1;
        $paginator = $this->application->getList($this->formatSearchQuery($condition));
        foreach ($paginator as &$data) {
            $data->genre = $this->applicationGenreRef->getSocialById($data->id);
            $data->producerlabel = $this->applicationProducerLabelRef->getOneById($data->id);
            $data->twitter = $this->applicationTwitter->getActiveById($data->id);
            $data->tag = $this->applicationTagRef->getSocialListById($data->id);
            $data->detail = $this->applicationDetail->getLastById($data->id);
            $data->image = $this->applicationImage->getPostThumbnailList($data->id);
            $data->apk = $this->applicationApk->getOne($data->id);
            $data->emulatorapk = $this->applicationEmulatorApk->getOne($data->id);
            $data->emulatorKeyMapping = $this->applicationEmulatorKeyMapping->getOne($data->id);
            if (!empty($data->apk)) {
                $data->apk->file_timestamp = $this->getAPKFileTimestamp($data->apk['apk_name']);
            }
            if (!empty($data->emulatorapk)) {
                $data->emulatorapk->file_timestamp = $this->getEmulatorAPKFileTimestamp($data->emulatorapk['apk_name']);
                $data->emulatorapk->url = $this->getEmulatorSiteUrl($data->id);
            }
            if (!empty($data->emulatorKeyMapping)) {
                $data->emulatorKeyMapping->key_mapping_file_name = '';
                if ($data->emulatorKeyMapping->cfg_version_code > 0 && $this->existsEmulatorKeyMappingFile($data->id)) {
                    $data->emulatorKeyMapping->key_mapping_file_name = "{$data->emulatorapk->package_name}.cfg";
                }
            }
            $data->cdn = $this->applicationCdnGameStore->getDataByAppId($data->id);
            $data->emulatorCdn = $this->applicationCdnEmulator->getDataByAppId($data->id);
            $data->metaData = $this->applicationMetaData->getOne($data->id);
            $deviceList = $this->applicationDevice->getListById($data->id);
            $data->device = new stdClass();
            foreach ($deviceList as $device) {
                $data->device->{$device->device} = $device;
                if ($device->device == 'pc' || $device->device == 'sp') {
                    $data->device->{$device->device}->image =
                        $this->applicationImage->getPostGameImageList($data->id, strtoupper($device->device));
                    $data->device->{$device->device}->guest_play = $configGuestPlay[$data->device->{$device->device}->guest_play];
                    $data->device->{$device->device}->browser_sdk = $configBrowserSdk[$data->device->{$device->device}->browser_sdk];
                } elseif (in_array($device->device,  ['android_app', 'emulator', 'apk_cloud'])) {
                    $data->device->{$device->device}->image =
                        $this->applicationImage->getPostGameImageList($data->id, 'SP');
                }

                if ($device->device == 'sp'){
                    $spMenu = $this->applicationSpMenu->getById($data->id);
                    // レコードがない場合は従来レイアウト、片側のみも存在しない状態のため従来レイアウトによせる
                    $data->device->sp->isSettingSpMenu =
                        (!is_null($spMenu) && $spMenu->portrait_position != 0 && $spMenu->landscape_position != 0);
                }

                if ($device->device == 'apk_cloud'){
                    $apkCloud = $this->applicationApkCloud->getOne($data->id);
                    $data->device->apk_cloud->maintenance_begin =
                        (empty($apkCloud)) ? null : $this->getDbDate($apkCloud->maintenance_begin, true);
                    $data->device->apk_cloud->maintenance_end =
                        (empty($apkCloud)) ? null : $this->getDbDate($apkCloud->maintenance_end, true);
                }

                // フロントに表示できる規約があるか確認
                if ($this->applicationPolicy->getOne($data->id)) {
                    // デバイスにより規約のURLを判定
                    if (in_array($device->device, ['pc', 'sp', 'android_app', 'apk_cloud'])) {
                        $url = env('HTTP_GAMES_ADULT_URL');
                        if ($data->general == 1) {
                            $url = env('HTTP_GAMES_GENERAL_URL');
                        }
                        $data->policy = sprintf('%s/detail/%s/policy/', $url, $data->title_id);
                    } elseif ($device->device == 'mobile') {
                        $domain = 'co.jp';
                        if ($data->general == 1) {
                            $domain = 'com';
                        }
                        $data->policy = sprintf('http://mobile-freegames.dmm.%s/application/-/policy/=/app_id=%d/', $domain, $data->id);
                    }
                }
            }
        }
        return $paginator;
    }

    /**
     * APKファイルの最終更新日時(Y/m/d H:i)を返す
     * ファイルが存在しない場合、メッセージを返す
     * @param $fileName
     * @return string
     */
    public function getAPKFileTimestamp($fileName)
    {
        if(empty($fileName)){
            return "なし";
        }

        $basePath = $this->getApkFileBasePath();
        $toFilePath = $basePath . '/' . $fileName;
        try {
            $fileSystem = Storage::disk('dl_app_netgame_s3');
            if (!$fileSystem->has($toFilePath)) {
                throw new Exception('File Not Found');
            }
            $timestamp = $fileSystem->getTimestamp($toFilePath);
            return date('Y/m/d H:i', $timestamp);
        } catch (Exception $e) {
            return "なし";
        }
    }

    /**
     * Emulator APKファイルの最終更新日時(Y/m/d H:i)を返す
     * ファイルが存在しない場合、メッセージを返す
     * @param $fileName
     * @return array|bool|string
     */
    private function getEmulatorAPKFileTimestamp($fileName) {
        if(empty($fileName)){
            return "なし";
        }

        $basePath = $this->getEmulatorApkFileBasePath();
        $toFilePath = $basePath . '/' . $fileName;
        try {
            $fileSystem = Storage::disk('dl_app_netgame_s3');
            if (!$fileSystem->has($toFilePath)) {
                throw new Exception('File Not Found');
            }
            $timestamp = $fileSystem->getTimestamp($toFilePath);
            return date('Y/m/d H:i', $timestamp);
        } catch (Exception $e) {
            return "なし";
        }
    }

    /**
     * add 公式サイトURL
     * @param array $data
     * @return bool
     */
    private function addEmulatorSiteUrl($data)
    {
        return $this->applicationEmulatorOfficialSiteUrl->add($data);
    }

    /**
     * get 公式サイトURL
     * @param integer $id
     * @return array
     */
    private function getEmulatorSiteUrl($id)
    {
        return $this->applicationEmulatorOfficialSiteUrl->getOne($id);
    }

    /**
     * edit 公式サイトURL
     * @param integer $id
     * @param array $data
     * @return bool
     */
    private function editEmulatorSiteUrl($id, $data)
    {
        return $this->applicationEmulatorOfficialSiteUrl->edit($id, $data);
    }

    public function getListAll()
    {
        return $this->application->getListAll();
    }

    public function getBasic($id)
    {
        if (empty($id)) {
            return false;
        }
        $data = $this->application->getOne($id);
        if (empty($data->exists)) {
            return $data;
        }
        $appTitleType = $this->getAppTitleType();
        if (! isset($appTitleType[$data->id])) {
            $data->exists = false;
            return $data;
        }
        $data->genre = $this->applicationGenreRef->getSocialById($data->id);
        $data->twitter = $this->applicationTwitter->getActiveById($data->id);
        $data->detail = $this->applicationDetail->getLastById($data->id);
        $applicationDeviceList = $this->applicationDevice->getListById($data->id);

        $deviceList = new stdClass();
        foreach ($applicationDeviceList as $applicationDevice) {
            $deviceList->{$applicationDevice->device} = $applicationDevice;
        }

        // 提供デバイスにPCまたはSPが含まれている場合 ガジェットURLのhttps制限を有効とする
        if ((array_key_exists('pc', $deviceList) && $deviceList->pc->exists)
            || (array_key_exists('sp', $deviceList) && $deviceList->sp->exists)) {
            $data->isSslDevice = true;
        } else {
            $data->isSslDevice = false;
        }

        $data->metaData = $this->applicationMetaData->getOne($data->id);

        return $data;
    }

    public function basicUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        $id = $data['id'];
        $one = $this->getBasic($id);
        if (empty($one->exists)) {
            return false;
        }
        // アプリ
        $app = array_only($data, [
            'title_short',
            'title_ruby',
            'url'
        ]);
        // ジャンル
        if (is_numeric($data['genre_id'])) {
            $main_genre_id = $data['genre_id'];
            $sub_genre_id = null;
        } else {
            list ($main_genre_id, $sub_genre_id) = explode('-', $data['genre_id']);
        }
        $genre = compact('main_genre_id', 'sub_genre_id');
        // Twitter
        $twitter = array_only($data, [
            'twitter_follow'
        ]);
        // 問い合わせ
        $detail = array_only($data, [
            'email',
            'inquiry_note',
            'inquiry_signature',
        ]);
        // 使用出来るタグのみ
        if (isset($detail['inquiry_note'])) {
            $detail['inquiry_note'] = except_html($detail['inquiry_note']);
        }

        Freegame::beginTransaction();
        try {
            // アプリ更新
            $this->application->edit($id, $app);
            // ジャンル更新
            $this->applicationGenreRef->delSocial($id);
            $this->applicationGenreRef->addSocial($id, $genre);
            // Twitter更新
            $this->applicationTwitter->editActive($id, $twitter);
            // 実行画面SSL対応 更新 (PC/SPが提供デバイスにある場合強制有効化)
            if ($data['isSslDevice']) {
                $this->applicationProtocol->edit($id, 1);
            }
            // メタデータ(description)の追加 or 更新
            if (array_key_exists('meta_description', $data)) {
                $this->applicationMetaData->upsert($id, $data['meta_description']);
            }

            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            throw $e;
        }
        // 問い合わせ更新
        $this->applicationDetail->editApp($id, $detail);
        return true;
    }

    public function getDevice($id, $device)
    {
        if (empty($id) || empty($device)) {
            return false;
        }
        $data = $this->applicationDevice->getOne($id, $device);
        if (empty($data->exists)) {
            return $data;
        }
        $appTitleType = $this->getAppTitleType();
        if (! isset($appTitleType[$data->app_id])) {
            $data->exists = false;
            return $data;
        }

        $spMenu = $this->applicationSpMenu->getById($data->app_id);
        // spメニューボタン初期位置が0(従来レイアウト)の場合
        // レコードがない場合も従来レイアウトを選択しているとして扱う
        if (is_null($spMenu) || $spMenu->portrait_position == 0 || $spMenu->landscape_position == 0) {
            $data->spLayout = 1;
            $data->menuPositionPortrait = 4;    // PFデフォルト初期位置
            $data->menuPositionLandscape = 4;   // PFデフォルト初期位置
        } else {
            $data->spLayout = 2;
            $data->menuPositionPortrait = $spMenu->portrait_position;
            $data->menuPositionLandscape = $spMenu->landscape_position;
        }

        $applicationPwaSettings = $this->applicationPwaSettings->getOneByAppId($data->app_id);
        $data->pwaSettingsExist = isset($applicationPwaSettings['app_id']);

        // APKクラウド
        if ($device == 'apk_cloud') {
            $apkCloud = $this->applicationApkCloud->getOne($id);
            $data->maintenance_begin =
                (empty($apkCloud)) ? null : $this->getDbDate($apkCloud->maintenance_begin, true);
            $data->maintenance_end =
                (empty($apkCloud)) ? null : $this->getDbDate($apkCloud->maintenance_end, true);
        }

        return $data;
    }

    public function getDevicePc($id)
    {
        return $this->getDevice($id, 'pc');
    }

    public function getDeviceSp($id)
    {
        return $this->getDevice($id, 'sp');
    }

    public function getDeviceMobile($id)
    {
        return $this->getDevice($id, 'mobile');
    }

    public function getDeviceAndroidApp($id)
    {
        return $this->getDevice($id, 'android_app');
    }

    public function getDeviceApkCloud($id)
    {
        return $this->getDevice($id, 'apk_cloud');
    }

    public function getDeviceEmulator($id)
    {
        return $this->getDevice($id, 'emulator');
    }

    public function deviceUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        $id = $data['id'];
        $device = $data['device'];
        $one = $this->getDevice($id, $device);
        if (empty($one->exists)) {
            return false;
        }
        // デバイス
        $attr = array_only($data, [
            'description',
            'description_middle',
            'description_short',
            'how_to_play',
            'restrictions',
        ]);
        if ($this->isEnableDomainType($id, $device)) {
            $attr['domain_type'] = $data['domain_type'];
        }
        // 使用出来るタグのみ
        if (isset($attr['description'])) {
            $attr['description'] = except_html($attr['description']);
        }
        if (isset($attr['description_middle'])) {
            $attr['description_middle'] = except_html($attr['description_middle']);
        }
        if (isset($attr['description_short'])) {
            $attr['description_short'] = except_html($attr['description_short']);
        }
        if (isset($attr['how_to_play'])) {
            $attr['how_to_play'] = except_html($attr['how_to_play']);
        }

        // トランザクション開始
        Freegame::beginTransaction();
        try {
            // デバイス更新
            $this->applicationDevice->edit($id, $device, $attr);

            // レシート型課金アプリケーション情報登録 ※nullで登録した際はAPI側で更新を行わない
            $applicationData = [
                'id' => $id,
                'title' => null,
                'description' => $attr['description_middle']
            ];

            // 今回デバイスがemulatorの場合レシート型課金にdescription情報を登録する 今後デバイス追加予定
            $apiAddDevices = config('forms.Games.apiAddDevice');

            if (in_array($device, $apiAddDevices)) {

                $receiptApplicationData = $this->receiptApiService->storeReceiptApplicationData($applicationData, $device);

                if (!$receiptApplicationData['resultStatus']) {
                    throw new Exception();
                }
            }

            // sp設定時のみSPメニューボタン初期位置情報登録
            if (isset($data['menu_position_portrait']) && isset($data['menu_position_landscape'])) {
                if ($data['spLayout'] == 2) {
                    $this->applicationSpMenu->edit($id, $data['menu_position_portrait'], $data['menu_position_landscape']);
                } else {
                    // type2を選択しなかった場合はレコードに0(従来画面)を登録
                    $this->applicationSpMenu->edit($id, 0, 0);
                }
            }

            // APKクラウド設定時
            if (isset($data['maintenance_begin']) && isset($data['maintenance_end'])) {
                $this->applicationApkCloud->edit($id, [
                    'maintenance_begin' => $this->getDbDate($data['maintenance_begin'], false),
                    'maintenance_end'   => $this->getDbDate($data['maintenance_end'], false),
                ]);
            }

            Freegame::commit();

        } catch (Exception $e) {
            Freegame::rollback();
            throw $e;
        }

        return true;
    }

    public function devicePcUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        $attr = array_only($data, [
            'id',
            'description',
            'description_middle',
            'how_to_play',
            'restrictions',
            'domain_type'
        ]);
        $attr['device'] = 'pc';
        return $this->deviceUpdate($attr);
    }

    public function deviceSpUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        $attr = array_only($data, [
            'id',
            'description',
            'description_middle',
            'how_to_play',
            'restrictions',
            'spLayout',
            'menu_position_portrait',
            'menu_position_landscape',
            'domain_type'
        ]);
        $attr['device'] = 'sp';
        return $this->deviceUpdate($attr);
    }

    public function deviceMobileUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        $attr = array_only($data, [
            'id',
            'description',
            'description_middle',
            'description_short',
            'how_to_play',
            'restrictions'
        ]);
        $attr['device'] = 'mobile';
        return $this->deviceUpdate($attr);
    }

    public function deviceAndroidAppUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        $attr = array_only($data, [
            'id',
            'description',
            'description_middle',
            'how_to_play',
            'restrictions'
        ]);
        $attr['device'] = 'android_app';
        return $this->deviceUpdate($attr);
    }

    public function deviceApkCloudUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        $attr = array_only($data, [
            'id',
            'description',
            'description_middle',
            'how_to_play',
            'restrictions',
            'maintenance_begin',
            'maintenance_end',
            'vendor',
            'boot_id',
        ]);
        $attr['device'] = 'apk_cloud';
        return $this->deviceUpdate($attr);
    }

    public function deviceEmulatorUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        $attr = array_only($data, [
            'id',
            'description_middle'
        ]);
        $attr['device'] = 'emulator';
        return $this->deviceUpdate($attr);
    }

    public function getApk($id)
    {
        if (empty($id)) {
            return false;
        }
        $data = $this->applicationApk->getOne($id);
        if (empty($data->exists)) {
            return $data;
        }
        $appTitleType = $this->getAppTitleType();
        if (! isset($appTitleType[$data->app_id])) {
            $data->exists = false;
            return $data;
        }
        return $data;
    }

    /**
     * APKファイルの署名を取得
     * @param string $path APKファイル
     * @return string|null 署名のハッシュ|失敗した場合はnull
     */
    public function getApkFingerprint($path)
    {
        $command = sprintf('%s verify --print-certs '.$path, config('toolpath.apksigner'));
        exec($command, $output, $result);
        if ($result) {
            return null;
        }
        preg_match('/SHA-256 digest: ([a-zA-Z0-9]{64})/', implode($output), $hash);

        return $hash[1];
    }
        
    /**
     * apkanalyzerを実行します。
     *
     * @param  mixed $path
     * @param  mixed $option
     * @return string
     * @throws FileUploadException apkanalyzerがAPKファイル解析できなかったとき
     */
    private function execApkanalyzer($option, $path)
    {
        $command = sprintf('%s %s %s 2>&1', config('toolpath.apkanalyzer'), $option, $path);
        $output = null;
        $result = exec($command, $output, $result_code);
        if ($result_code) {
            // 特定のAPKファイルで謎のエラーが出るので原因特定用のログ出力
            $ouput_message = sprintf(
                'APKの解析に失敗しました。アップロードしたAPKファイルを確認してください。command:%s output:%s', 
                $command, implode("", $output));
            log::error($ouput_message);
            throw new FileUploadException($ouput_message);
        }
        return $result;
    }

    /**
     * アップロードされたAPKファイルの情報を取得
     * @param UploadedFile $apk APKファイル
     * @return stdClass apkの情報
     */
    public function getUploadApkInfo($apk)
    {
        $info = new stdClass;
        try {
            // JAVA_HOMEの環境変数を設定する
            // php-fpmが環境をクリアしているのが原因でapkanalyzerがJAVA_HOMEを参照できない事象が発生した為
            putenv(sprintf('JAVA_HOME=%s', config('toolpath.java_home')));
            $info->package_name     = $this->execApkanalyzer('manifest application-id', $apk->getPath().'/'.$apk->getFilename());
            $info->app_version_name = $this->execApkanalyzer('manifest version-name',   $apk->getPath().'/'.$apk->getFilename());
            $info->app_version_code = $this->execApkanalyzer('manifest version-code',   $apk->getPath().'/'.$apk->getFilename());
        } catch (FileUploadException $fue) {
            // apkanalyzerがエラーが発生したときは、ApkParserを使ってパースを実施する
            // apkanalyzerは「ERROR: org.xml.sax.SAXParseException」が発生させる場合がある、
            // 発生した場合でもApkParserでは正常に処理できる場合があるので、ApkParserでもパースを実施する
            try{
                $parser = new ApkParser($apk->getPath().'/'.$apk->getFilename());
                $info->package_name     = $parser->getManifest()->getPackageName();
                $info->app_version_name = $parser->getManifest()->getVersionName();
                $info->app_version_code = $parser->getManifest()->getVersionCode();
            }catch(Exception $e){
                log::error($e->getMessage());
                throw new FileUploadException(sprintf('ApkParser error:%s (apkanalyzer error:%s)',$e->getMessage(), $fue->getMessage()));
            }
        }
        $info->file_size        = round(filesize($apk) / 1048576, 2); // application_apkテーブルの型の問題でサイズをByte→MBに変換

        $info->fingerprint      = $this->getApkFingerprint($apk->getPath().'/'.$apk->getFilename());

        return $info;
    }

    /**
     * APKファイル検証
     * 
     * 検証項目
     * パッケージ名
     * バージョンコード
     * 署名
     * @param stdClass $new 新しいapkの情報
     * @param stdClass $current 現行のapkの情報
     * @return array エラー項目
     */
    public function apkUpdateValidation($new, $current)
    {
        $err = [];

        // 異なるアプリのAPKアップロードを防ぐためにパッケージ名が一致することを確認
        if ($new->package_name != $current->package_name) {
            $err['package_name'] = trans('validationmessage.MSG326');
        }

        // バージョンコードが増えていないとアップデートが出来ないので既存のものより増えていることを確認
        if (!$new->app_version_code || $new->app_version_code <= $current->app_version_code) {
            $err['version_code'] = trans('validationmessage.MSG321');
        }

        // デバッグ用APKの誤配布を防ぐために署名を確認
        if (!$new->fingerprint) {
            $err['fingerprint'] = trans('validationmessage.MSG322');
        } elseif ($current->fingerprint && $new->fingerprint != $current->fingerprint) {
            $err['fingerprint'] = trans('validationmessage.MSG323');
        }

        //アプリバージョンは半角数字とドットのみ許可
        if (!preg_match('/^[0-9.]+$/', $new->app_version_name)) { 
            $err['app_version_name'] = trans('validationmessage.MSG226', ['attribute' => 'アプリバージョン']);
        }

        return $err;
    }

    /**
     * APK情報更新
     * @param array $data
     * @return bool
     */
    public function apkDataUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        $id = $data['id'];
        $one = $this->getApk($id);
        if (empty($one->exists)) {
            return false;
        }
        // APK
        $attr = array_only($data, [
            'file_size',
            'app_version_name',
            'app_version_code',
            'fingerprint',
        ]);
        // APK更新
        return $this->applicationApk->edit($id, $attr);
    }

    /**
     * APK情報更新、APKファイルアップロードを行う
     * APKファイルアップロードで失敗した場合、DBをロールバックする
     * @param array $data
     * @return array|bool
     */
    public function apkUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        $id = $data['id'];
        $one = $this->getApk($id);
        if (empty($one->exists)) {
            return false;
        }
        // APKファイル名
        $data['apk_name'] = $one->apk_name;
        // APKアップロード
        $data['tempPath'] = $this->getApkFileTmpPath();
        $data['basePath'] = $this->getApkFileBasePath();

        $result = '';
        Freegame::beginTransaction();
        try {
            // APK情報更新
            $result = $this->apkDataUpdate($data);
            if (is_array($result) || is_bool($result)) {
                throw new Exception('DB Update Error');
            }

            // テンポラリAPKファイルの移動
            $result = $this->apkFileTmpMove($data);
            if (is_array($result) || is_bool($result)) {
                throw new Exception('APKTmpFile Move Error');
            }

            // 一時ファイル削除
            $this->apkFileTmpClear();

            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            if (is_array($result)) {
                return $result;
            }
            return false;
        }

        return true;
    }

    /**
     * APKファイルベースパス取得
     *
     * @return string
     */
    public function getApkFileBasePath()
    {
        return rtrim(env('DL_APP_BASEPATH', '/public/android'), '/');
    }

    /**
     * APKファイル一時パス取得
     *
     * @return string
     */
    public function getApkFileTmpPath()
    {
        return rtrim(env('DL_APP_TEMPORARYPATH', '/tmp/tmpuploadapk'), '/') . '/' . request()->session()->getId();
    }

    /**
     * APKファイルの一時保存
     * @param UploadedFile $apk
     * @return array エラーメッセージ
     */
    public function apkFileTmpSave($apk)
    {
        $path = $this->getApkFileTmpPath();
        $fileName = request()->session()->getId(). '.apk';
        try {
            // 一時ファイルを保存するディレクトリがない場合、ディレクトリを作成
            if (!\File::exists($path)) {
                \File::makeDirectory($path, 0777, true);
            }

            $apk->move($path, $fileName);

            // テンポラリAPKファイルアップロード
            $data['tempPath'] = $this->getApkFileTmpPath();
            $data['basePath'] = $this->getApkFileBasePath();
            $result = $this->apkFileTmpUpload($data);
            if (is_array($result) || is_bool($result)) {
                throw new Exception('TempAPKFile Upload Error');
            }

            // 一時ファイル削除
            $this->apkFileTmpClear();

        } catch (Exception $e) {
            Log::error('apkFileMoveError: ' . $e->getMessage());
            return [
                'apk_file' => '一時的なエラーによりリクエストが完了しませんでした。しばらく時間が経ってからやり直してください。このエラーが解決しない場合はDMM窓口、もしくはタイトル担当窓口にお問い合わせください。'
            ];
        }
        return [];
    }

    /**
     * APKファイル移動
     *
     * @param array $data
     * @return array|bool
     */
    public function apkFileMove($data)
    {
        if (empty($data['apk_file']) || ! is_object($data['apk_file'])) {
            return true;
        }
        if (empty($data['apk_name'])) {
            return true;
        }
        // APKファイル移動
        $tempPath = $data['tempPath'];
        $fileName = $data['apk_name'];
        try {
            if (! $data['apk_file']->isValid()) {
                throw new Exception('File Not Found');
            }

            // 一時ファイルを保存するディレクトリがない場合、ディレクトリを作成
            if (!\File::exists($tempPath)) {
                \File::makeDirectory($tempPath, 0777, true);
            }

            $data['apk_file']->move($tempPath, $fileName);
        } catch (Exception $e) {
            Log::error('apkFileMoveError: ' . $e->getMessage());
            return [
                'apk_file' => '一時的なエラーによりリクエストが完了しませんでした。しばらく時間が経ってからやり直してください。このエラーが解決しない場合はDMM窓口、もしくはタイトル担当窓口にお問い合わせください。'
            ];
        }
        request()->merge([
            'apk_name' => $fileName
        ]);
        return $fileName;
    }

    /**
     * テンポラリAPKファイルアップロード
     *
     * @param $data
     * @return array|bool
     */
    public function apkFileTmpUpload($data)
    {
        $tempPath = $data['tempPath'];
        $basePath = $data['basePath'];
        $fromFilePath = $tempPath . '/' . request()->session()->getId() . '.apk';;
        $toFilePath = $basePath . '/tmp_' . request()->get('id') . '.apk';

        try {
            $fileSystem = \Storage::disk('dl_app_netgame_s3');

            $stream = fopen($fromFilePath, 'r');
            $fileSystem->put($toFilePath, $stream);
            @fclose($stream);

            if (! $fileSystem->has($toFilePath)) {
                throw new Exception('File Not Found');
            }
        } catch (Exception $e) {
            Log::error('apkTempFileUploadError: ' . $e->getMessage());
            return [
                'apk_file' => '一時的なエラーによりリクエストが完了しませんでした。しばらく時間が経ってからやり直してください。このエラーが解決しない場合はDMM窓口、もしくはタイトル担当窓口にお問い合わせください。'
            ];
        }
    }

    /**
     * APKファイル移動
     *
     * @param $data
     * @return array|bool
     */
    public function apkFileTmpMove($data)
    {
        if (empty($data['apk_name'])) {
            return true;
        }

        $fileName = $data['apk_name'];
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);
        $basePath = $data['basePath'];
        $fromFilePath = $basePath . '/tmp_' . request()->get('id') . '.apk';
        $toFilePath = $basePath . '/' . $fileName;

        try {
            $fileSystem = \Storage::disk('dl_app_netgame_s3');

            // 移動先のファイルを削除
            if ($fileSystem->has($toFilePath)) {
                $fileSystem->delete($toFilePath);
            }
            // ファイルの移動
            $fileSystem->move($fromFilePath, $toFilePath);

            if (! $fileSystem->has($toFilePath)) {
                throw new Exception('File Not Found');
            }

            // 母艦アプリ任意化対応
            // 拡張子がapkではない場合、特設ページから直DL出来るように、拡張子apk付きのファイルをコピーして作成する
            if($extension !== 'apk') {
                // コピー先のファイルの削除
                if ($fileSystem->has($toFilePath.'.apk')) {
                    $fileSystem->delete($toFilePath.'.apk');
                }
                // ファイルのコピー
                $fileSystem->copy($toFilePath, $toFilePath.'.apk');

                if (! $fileSystem->has($toFilePath.'.apk')) {
                    throw new Exception('File Not Found');
                }
            }
        } catch (Exception $e) {
            Log::error('apkFileTmpMoveError: ' . $e->getMessage());
            return [
                'apk_file' => '一時的なエラーによりリクエストが完了しませんでした。しばらく時間が経ってからやり直してください。このエラーが解決しない場合はDMM窓口、もしくはタイトル担当窓口にお問い合わせください。'
            ];
        }

        return $fileName;
    }

    /**
     * APKファイルアップロード
     *
     * @param $data
     * @return array|bool
     */
    public function apkFileUpload($data)
    {
        if (empty($data['apk_name'])) {
            return true;
        }

        $fileName = $data['apk_name'];
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);
        $tempPath = $data['tempPath'];
        $basePath = $data['basePath'];
        $fromFilePath = $tempPath . '/' . request()->session()->getId() . '.apk';
        $toFilePath = $basePath . '/' . $fileName;

        try {
            $fileSystem = \Storage::disk('dl_app_netgame_s3');

            $stream = fopen($fromFilePath, 'r');
            $fileSystem->put($toFilePath, $stream);
            @fclose($stream);

            if (! $fileSystem->has($toFilePath)) {
                throw new Exception('File Not Found');
            }

            // 母艦アプリ任意化対応
            // 拡張子がapkではない場合、特設ページから直DL出来るように、拡張子apk付きのファイルもアップする
            if($extension !== 'apk') {
                $stream = fopen($fromFilePath, 'r');
                $fileSystem->put($toFilePath.'.apk', $stream);
                @fclose($stream);

                if (! $fileSystem->has($toFilePath.'.apk')) {
                    throw new Exception('File Not Found');
                }
            }
        } catch (Exception $e) {
            Log::error('apkFileUploadError: ' . $e->getMessage());
            return [
                'apk_file' => '一時的なエラーによりリクエストが完了しませんでした。しばらく時間が経ってからやり直してください。このエラーが解決しない場合はDMM窓口、もしくはタイトル担当窓口にお問い合わせください。'
            ];
        }

        return $fileName;
    }

    /**
     * APKファイルの一時ファイル削除
     *
     * @param string|null $dir
     * @return bool
     */
    public function apkFileTmpClear($dir = null)
    {
        // 一時ファイル削除
        if (is_null($dir)) {
            $dir = $this->getApkFileTmpPath();
        }
        foreach (glob(rtrim($dir, '/') . '/*') as $path) {
            if (is_dir($path)) {
                $this->apkFileTmpClear($path);
            } else {
                @unlink($path);
            }
        }
        @rmdir($dir);
        return true;
    }

    /**
     * EmulatorApk
     */
    public function getEmulatorApk($id)
    {
        if (empty($id)) {
            return false;
        }
        $data = $this->applicationEmulatorApk->getOne($id);
        $data->url = $this->getEmulatorSiteUrl($id);
        if (empty($data->exists)) {
            return $data;
        }
        $appTitleType = $this->getAppTitleType();
        if (! isset($appTitleType[$data->app_id])) {
            $data->exists = false;
            return $data;
        }
        return $data;
    }

    /**
     * アップロードされたEmulatorAPKファイルの情報を取得
     * @param UploadedFile $apk APKファイル
     * @return stdClass apkの情報
     */
    public function getUploadEmulatorApkInfo($apk)
    {
        $info = new stdClass;
        try {
            // JAVA_HOMEの環境変数を設定する
            // php-fpmが環境をクリアしているのが原因でapkanalyzerがJAVA_HOMEを参照できない事象が発生した為
            putenv(sprintf('JAVA_HOME=%s', config('toolpath.java_home')));
            $info->package_name     = $this->execApkanalyzer('manifest application-id', $apk->getPath().'/'.$apk->getFilename());
            $info->app_version_name = $this->execApkanalyzer('manifest version-name',   $apk->getPath().'/'.$apk->getFilename());
            $info->app_version_code = $this->execApkanalyzer('manifest version-code',   $apk->getPath().'/'.$apk->getFilename());
        } catch (FileUploadException $fue) {
            // apkanalyzerがエラーが発生したときは、ApkParserを使ってパースを実施する
            // apkanalyzerは「ERROR: org.xml.sax.SAXParseException」が発生させる場合がある、
            // 発生した場合でもApkParserでは正常に処理できる場合があるので、ApkParserでもパースを実施する
            try{
                $parser = new ApkParser($apk->getPath().'/'.$apk->getFilename());
                $info->package_name     = $parser->getManifest()->getPackageName();
                $info->app_version_name = $parser->getManifest()->getVersionName();
                $info->app_version_code = $parser->getManifest()->getVersionCode();
            }catch(Exception $e){
                log::error($e->getMessage());
                throw new FileUploadException(sprintf('ApkParser error:%s (apkanalyzer error:%s)',$e->getMessage(), $fue->getMessage()));
            }
        }
        $info->file_size        = filesize($apk);

        $info->fingerprint      = $this->getApkFingerprint($apk->getPath().'/'.$apk->getFilename());

        return $info;
    }

    /**
     * EmulatorAPK情報更新
     * @param $data
     * @return bool
     */
    public function emulatorApkDataUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        $id = $data['id'];
        $oneEmulatorApk = $this->getEmulatorApk($id);
        if (empty($oneEmulatorApk->exists)) {
            return false;
        }

        $oneEmulatorUrl = $this->getEmulatorSiteUrl($id);

        // EmulatorOfficialSiteUrl
        $attrUrlAdd = [
            'app_id' => $id,
            'url' => $data['url'],
            'created_at' => Carbon::now()
        ];
        $attrUrlEdit = [
            'url' => $data['url'],
            'updated_at' => Carbon::now()
        ];

        if (empty($oneEmulatorUrl)) {
            $this->addEmulatorSiteUrl($attrUrlAdd);
        } else {
            $this->editEmulatorSiteUrl($id, $attrUrlEdit);
        }

        // EmulatorAPK
        $attr = array_only($data, [
            'file_size',
            'app_version_name',
            'app_version_code',
            'fingerprint',
        ]);
        // EmulatorAPK更新
        return $this->applicationEmulatorApk->edit($id, $attr);
    }

    public function emulatorApkUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        $id = $data['id'];
        $one = $this->getEmulatorApk($id);
        if (empty($one->exists)) {
            return false;
        }
        // EmulatorAPKファイル名
        $data['apk_name'] = $one->apk_name;

        // EmulatorAPKアップロード
        $data['tempPath'] = $this->getEmulatorApkFileTmpPath();
        $data['basePath'] = $this->getEmulatorApkFileBasePath();

        Freegame::beginTransaction();
        try {
            // EmulatorAPK情報更新
            $result = $this->emulatorApkDataUpdate($data);
            if (is_array($result) || is_bool($result)) {
                throw new Exception('DB Update Error');
            }
            
            // テンポラリAPKファイルの移動
            $result = $this->apkFileTmpMove($data);
            if (is_array($result) || is_bool($result)) {
                throw new Exception('EmulatorAPKTmpFile Move Error');
            }

            // 一時ファイル削除
            $this->apkFileTmpClear($data['tempPath']);

            // GamePlayer側のApkキャッシュをクリア
            // キャッシュクリアの成否にかかわらず進行させる。
            $this->gameplayerApiService->clearApkCache($id);

            // 一時ファイル削除
            $this->emulatorApkFileTmpClear();

            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            if (is_array($result)) {
                return $result;
            }
            return false;
        }

        return true;
    }

    public function getEmulatorApkFileBasePath()
    {
        return rtrim(env('DL_EMULATOR_BASEPATH', '/public/emulator'), '/');
    }

    public function getEmulatorApkFileTmpPath()
    {
        return rtrim(env('DL_EMULATOR_TEMPORARYPATH', '/tmp/tmpuploadapk'), '/') . '/' . request()->session()->getId();
    }

    /**
     * EmulatorAPKファイルの一時保存
     * @param UploadedFile $apk
     * @return array エラーメッセージ
     */
    public function emulatorApkFileTmpSave($apk)
    {
        $path = $this->getEmulatorApkFileTmpPath();
        $fileName = request()->session()->getId(). '.apk';
        try {
            // 一時ファイルを保存するディレクトリがない場合、ディレクトリを作成
            if (!\File::exists($path)) {
                \File::makeDirectory($path, 0777, true);
            }

            $apk->move($path, $fileName);

            // テンポラリAPKファイルアップロード
            $data['tempPath'] = $this->getEmulatorApkFileTmpPath();
            $data['basePath'] = $this->getEmulatorApkFileBasePath();
            $result = $this->apkFileTmpUpload($data);
            if (is_array($result) || is_bool($result)) {
                throw new Exception('TempAPKFile Upload Error');
            }

            // 一時ファイル削除
            $this->apkFileTmpClear($data['tempPath']);
        } catch (Exception $e) {
            Log::error('apkFileMoveError: ' . $e->getMessage());
            return [
                'apk_file' => '一時的なエラーによりリクエストが完了しませんでした。しばらく時間が経ってからやり直してください。このエラーが解決しない場合はDMM窓口、もしくはタイトル担当窓口にお問い合わせください。'
            ];
        }
        return [];
    }

    public function emulatorApkFileTmpClear($dir = null)
    {
        // 一時ファイル削除
        if (is_null($dir)) {
            $dir = $this->getEmulatorApkFileTmpPath();
        }
        foreach (glob(rtrim($dir, '/') . '/*') as $path) {
            if (is_dir($path)) {
                $this->emulatorApkFileTmpClear($path);
            } else {
                @unlink($path);
            }
        }
        @rmdir($dir);
        return true;
    }

    public function getLatestList($condition = [])
    {
        if (empty($condition['perPage'])) {
            $condition['perPage'] = config('forms.Games.latest.perPage');
        }
        if (empty($condition['page'])) {
            $condition['page'] = 1;
        }
        $paginator = $this->applicationLatestInfo->getList($this->formatSearchQuery($condition));
        $paginator->appends([
            'perPage' => $paginator->perPage(),
            'page'    => $condition['page']
        ]);
        return $paginator;
    }

    public function getLatest($id)
    {
        if (empty($id)) {
            return false;
        }
        $data = $this->applicationLatestInfo->getActiveById($id);
        if (empty($data->exists)) {
            return $data;
        }
        $appTitleType = $this->getAppTitleType();
        if (! isset($appTitleType[$data->app_id])) {
            $data->exists = false;
            return $data;
        }
        return $data;
    }

    public function latestStore($data)
    {
        if (empty($data)) {
            return false;
        }
        $id = $data['app_id'];
        $appTitleType = $this->getAppTitleType();
        if (! isset($appTitleType[$id])) {
            return false;
        }
        $attr = array_only($data, [
            'app_id',
            'title',
            'detail',
            'begin_date',
            'end_date'
        ]);
        $attr['initial_begin_date'] = $attr['begin_date'];
        return $this->applicationLatestInfo->add($attr);
    }

    public function latestUpdate($data)
    {
        if (empty($data)) {
            return false;
        }
        $id = $data['id'];
        $one = $this->getLatest($id);
        if (empty($one->exists)) {
            return false;
        }
        $attr = array_only($data, [
            'title',
            'detail',
            'begin_date',
            'end_date'
        ]);
        return $this->applicationLatestInfo->edit($id, $attr);
    }

    public function latestDestroy($data)
    {
        if (empty($data)) {
            return false;
        }
        $id = $data['id'];
        $one = $this->getLatest($id);
        if (empty($one->exists)) {
            return false;
        }
        return $this->applicationLatestInfo->del($id);
    }

    /**
     * Get search params from request
     * @param request $request
     * @return array
     */
    public function formatSearchConditionForLatest($request)
    {
        if (request()->has('search')) {
            $request = session('Games', []);
            request()->merge($request);
        }

        $search = array_only($request, [
            'perPage',
            'page'
        ]);

        request()->session()->set('Games', $search);
        return $search;
    }

    /**
     * Emulatorキーマッピング情報取得
     *
     * @param int $appId
     * @return stdClass|null
     */
    public function getEmulatorKeyMapping($appId)
    {
        if (empty($appId)) {
            return null;
        }
        $emulatorKeyMapping = $this->applicationEmulatorKeyMapping->getOne($appId);
        $emulatorKeyMapping->key_mapping_file_name = '';
        if ($emulatorKeyMapping->cfg_version_code > 0 && $this->existsEmulatorKeyMappingFile($appId)) {
            $emulatorKeyMapping->key_mapping_file_name = $this->getEmulatorKeyMappingFileName($appId);
        }

        return $emulatorKeyMapping;
    }

    /**
     * Emulatorキーマッピング設定ファイル名取得
     *
     * @param int $appId
     * @return string
     */
    public function getEmulatorKeyMappingFileName($appId)
    {
        $emulatorApk = $this->applicationEmulatorApk->getOne($appId);
        return "{$emulatorApk->package_name}.cfg";
    }

    /**
     * Emulatorキーマッピング更新
     *
     * @param array $data
     * @return bool|array
     */
    public function emulatorKeyMappingUpdate($data)
    {
        if (empty($data)) {
            return false;
        }

        Freegame::beginTransaction();
        $result = '';
        try {
            // EmulatorAPK情報更新
            $result = $this->emulatorKeyMappingDataUpdate($data);
            if (!$result) {
                throw new Exception('DB Update Error');
            }
            if (!empty($data['key_mapping_file'])) {
                // CFGファイルバージョンコードカウントアップ
                $result = $this->applicationEmulatorKeyMapping->editCountUpCfgVersionCode($data['id']);
                if ($result !== 1) {
                    throw new Exception('DB Update Error');
                }

                // EmulatorAPKファイル名
                $data['key_mapping_file_name'] = $this->getEmulatorKeyMappingFileName($data['id']);

                // Emulatorキーマッピング設定ファイルアップロード
                $data['tempPath'] = $this->getEmulatorKeyMappingFileTmpPath();
                $data['basePath'] = $this->getEmulatorKeyMappingFileBasePath();

                // 一時ファイル削除
                $this->emulatorKeyMappingFileTmpClear();

                // EmulatorKeyMapping設定ファイル移動
                $result = $this->emulatorKeyMappingFileMove($data);
                if (is_array($result) || is_bool($result)) {
                    throw new Exception('EmulatorKeyMappingFile Move Error');
                }

                // EmulatorKeyMapping設定ファイルアップロード
                $keyMappingFileUploadResult = $this->emulatorKeyMappingFileUpload($data);
                if (is_array($keyMappingFileUploadResult) || is_bool($keyMappingFileUploadResult)) {
                    throw new Exception('EmulatorKeyMappingFile Upload Error');
                }
            }

            Freegame::commit();
        } catch (Exception $e) {
            Log::error('emulatorKeyMappingUpdateError: ' . $e->getMessage());
            Freegame::rollback();
            if (is_array($result)) {
                return $result;
            }
            return false;
        }
        return true;
    }

    /**
     * Emulatorキーマッピング情報更新
     *
     * @param array $data
     * @return int|bool
     */
    public function emulatorKeyMappingDataUpdate($data)
    {
        if (empty($data)) {
            return false;
        }

        // EmulatorKeyMapping更新
        $setAttribute = array_only($data, ['is_enable']);
        $setAttribute['updated_at'] = date('Y-m-d H:i:s');
        return $this->applicationEmulatorKeyMapping->edit($data['id'], $setAttribute);
    }

    /**
     * Emulatorキーマッピング設定ファイルの一時ファイル削除
     *
     * @param string|null $dir
     * @return void
     */
    public function emulatorKeyMappingFileTmpClear($dir = null)
    {
        // 一時ファイル削除
        if (is_null($dir)) {
            $dir = $this->getEmulatorKeyMappingFileTmpPath();
        }
        foreach (glob(rtrim($dir, '/') . '/*') as $path) {
            if (is_dir($path)) {
                $this->emulatorKeyMappingFileTmpClear($path);
            } else {
                @unlink($path);
            }
        }
        @rmdir($dir);
        return;
    }

    /**
     * Emulatorキーマッピング設定ファイル一時パス取得
     *
     * @return string
     */
    public function getEmulatorKeymappingFileTmpPath()
    {
        return rtrim(env('DL_EMULATOR_KEY_MAPPING_TEMPORARYPATH', '/tmp/tmpuploademulatorkeymapping'), '/')
            . '/' . request()->session()->getId();
    }

    /**
     * Emulatorキーマッピング設定ファイルベースパス取得
     *
     * @return string
     */
    public function getEmulatorKeyMappingFileBasePath()
    {
        return rtrim(env('DL_EMULATOR_KEY_MAPPING_BASEPATH', '/public/emulatorkeymapping'), '/');
    }

    /**
     * Emulatorキーマッピング設定ファイル移動
     *
     * @param array $data
     * @return array|bool
     */
    public function emulatorKeyMappingFileMove($data)
    {
        if (empty($data['key_mapping_file']) || ! is_object($data['key_mapping_file'])) {
            return true;
        }
        if (empty($data['key_mapping_file_name'])) {
            return true;
        }
        // APKファイル移動
        $tempPath = $data['tempPath'];
        $fileName = $data['key_mapping_file_name'];
        try {
            if (! $data['key_mapping_file']->isValid()) {
                throw new Exception('File Not Found');
            }

            // 一時ファイルを保存するディレクトリがない場合、ディレクトリを作成
            if (!\File::exists($tempPath)) {
                \File::makeDirectory($tempPath, 0777, true);
            }

            $data['key_mapping_file']->move($tempPath, $fileName);
        } catch (Exception $e) {
            Log::error('keyMappingFileMoveError: ' . $e->getMessage());
            return ['key_mapping_file' => 'キーマッピング設定ファイルの一時保存に失敗しました。'];
        }
        request()->merge(['key_mapping_file_name' => $fileName]);
        return $fileName;
    }

    /**
     * Emulatorキーマッピング設定ファイルアップロード
     *
     * @param array $data
     * @return array|bool
     */
    public function emulatorKeyMappingFileUpload($data)
    {
        if (empty($data['key_mapping_file_name'])) {
            return true;
        }

        $fileName = $data['key_mapping_file_name'];
        $tempPath = $data['tempPath'];
        $basePath = $data['basePath'];
        $fromFilePath = $tempPath . '/' . $fileName;
        $toFilePath = $basePath . '/' . $fileName;

        try {
            $fileSystem = Storage::disk('dl_app_netgame_s3');

            $stream = fopen($fromFilePath, 'r');
            $fileSystem->put($toFilePath, $stream);
            @fclose($stream);

            if (! $fileSystem->has($toFilePath)) {
                throw new Exception('File Not Found');
            }
        } catch (Exception $e) {
            Log::error('emulatorKeyMappingFileUploadError: ' . $e->getMessage());
            return ['key_mapping_file' => 'キーマッピング設定ファイルのアップロードに失敗しました。'];
        }

        // 一時ファイル削除
        $this->emulatorKeyMappingFileTmpClear();

        return $fileName;
    }

    /**
     * Emulatorキーマッピング設定ファイル存在チェック
     *
     * @param int $appId
     * @return bool
     */
    public function existsEmulatorKeyMappingFile($appId)
    {
        $filePath = $this->getEmulatorKeyMappingFileBasePath() . '/' . $this->getEmulatorKeyMappingFileName($appId);
        try {
            $fileSystem = Storage::disk('dl_app_netgame_s3');
            if ($fileSystem->has($filePath)) {
                return true;
            }
        } catch (Exception $e) {
            Log::error('existsEmulatorKeyMappingFile Error: ' . $e->getMessage());
        }
        return false;
    }

    /**
     * トークン一覧と公開鍵取得
     *
     * @param $app_id
     * @return array
     */
    public function getReciboTokenList($app_id)
    {
        // 初期化
        $receiboTokenList = [];

        $supportedDeviceList = $this->receiptApiService->getSupportedDevice();
        foreach ($supportedDeviceList as $device) {
            // レシート用アプリケーションID取得
            $receiptApplication = $this->receiptApiService->getReceiptApplication($app_id, $device);

            // 取得結果のエラーチェック
            if ($receiptApplication['resultMessage'] !== '') {
                $receiboTokenList[$device][] = $this->setEmptyApplicationToken($receiptApplication['resultMessage']);
                continue;
            }
            // 取得結果の空チェック
            if (empty($receiptApplication['resultData'])) {
                $receiboTokenList[$device][] = $this->setEmptyApplicationToken('');
                continue;
            }

            // トークン一覧と公開鍵取得
            $applicationTokenList = $this->receiptApiService->getApplicationTokenList($receiptApplication['resultData']['id']);

            // トークン取得API実行結果判定
            if (!$applicationTokenList['resultStatus']) {
                $errorMessage = array_key_exists('resultMessage', $applicationTokenList['resultMessage']) ?
                    $applicationTokenList['resultMessage'] : '';
                $receiboTokenList[$device][] = $this->setEmptyApplicationToken($errorMessage);
                continue;
            }

            // 現状の仕様だと、APIレスポンスが正常でcredentialsの情報がないということはありえないが念のためチェック
            if (empty($applicationTokenList['response']) || empty($applicationTokenList['response']['credentials'])) {
                $receiboTokenList[$device][] = $this->setEmptyApplicationToken('');
                continue;
            }

            $receiboToken = [];
            foreach ($applicationTokenList['response']['credentials'] as $list) {
                // timestamp変換
                $createdAt = date("Y/m/d H:i:s", $list['createdAt']);

                // viewに表示させるデータ格納
                $receiboToken[] = [
                    'resultMessage' => null,
                    'openKey' => $receiptApplication['resultData']['key'],
                    'receiptApplicationId' => $receiptApplication['resultData']['id'],
                    'id' => $list['id'],
                    'token' => $list['token'],
                    'createdAt' => $createdAt
                ];
            }

            // 表示する順番を整理して、デバイス別に格納
            $receiboTokenList[$device] = array_reverse($receiboToken);
        }

        return $receiboTokenList;
    }

    private function setEmptyApplicationToken($errorMessage)
    {
        return [
            'resultMessage'        => $errorMessage,
            'openKey'              => null,
            'receiptApplicationId' => null,
            'id'                   => null,
            'token'                => null,
            'createdAt'            => null,
        ];
    }

    /**
     * レシート用アプリケーションIDからトークンを新規作成
     *
     * @param $receiptApplicationId
     * @return array
     */
    public function storeApplicationToken($receiptApplicationId)
    {
        // 初期化
        $result = [];
        // アプリケーションAPIトークンの追加
        if ($receiptApplicationId) {
            $result = $this->receiptApiService->storeApplicationToken($receiptApplicationId);
        }
        return $result;
    }

    /**
     * トークンIDからトークンを削除
     *
     * @param $applicationTokenId
     * @return array
     */
    public function deleteApplicationToken($applicationTokenId)
    {
        // 初期化
        $result = [];
        // アプリケーションAPIトークンの削除
        if ($applicationTokenId) {
            $result = $this->receiptApiService->deleteApplicationToken($applicationTokenId);
        }
        return $result;
    }

    /**
     * search application title group
     * @param $appID
     * @return array
     */
    public function getTitleGroup($appID)
    {
        $titleGroup = [];
        $devices = config('forms.ApplicationTitleGroup.mainTitleDevice');
        foreach ($devices as $device) {
            $titleGroup = $this->getTitleGroupByAppId($device, $appID);
            if ($titleGroup) break;
        }
        return $this->setTitleGroupData($titleGroup);
    }

    /**
     * get application title group by app id
     * @param $device
     * @param $appID
     * @param $id
     * @return array
     */
    public function getTitleGroupByAppId($device, $appID, $id = null)
    {
        $tableColumns = config('forms.ApplicationTitleGroup.tableColumn');
        return $this->applicationTitleGroupBySite->getGroupByAppId($tableColumns[$device], $appID, $id);
    }

    /**
     * set title group data
     * @param $titleGroup
     * return array
     */
    public function setTitleGroupData($titleGroup)
    {
        if ($titleGroup) {
            return [
                'id'                    => $titleGroup->id,
                'app_store_url'         => $titleGroup->app_store_url ?: '',
                'google_play_url'       => $titleGroup->google_play_url ?: '',
                'begin'                 => $this->formatDisplayDate($titleGroup->public_start_date),
                'end'                   => $this->formatDisplayDate($titleGroup->public_end_date),
            ];
        } else {
            return [
                'id'                    => '',
                'app_store_url'         => '',
                'google_play_url'       => '',
                'begin'                 => '',
                'end'                   => '',
            ];
        }
    }

    /**
     * Format store date
     * @param  string $date
     * @return string
     */
    public function formatStoreDate($date)
    {
        $formatStoreDate = config('forms.ApplicationTitleGroup.formatStoreDate');
        if ($date !== '') {
            $date = date($formatStoreDate, strtotime($date));
        }

        return $date;
    }

    /**
     * Format display date
     * @param string $date [description]
     * @return string
     */
    public function formatDisplayDate($date)
    {
        if (empty($date)) return $date;
        $formatDisplayDate = config('forms.ApplicationTitleGroup.formatDate');
        return date($formatDisplayDate, strtotime($date));
    }

    /**
     * update application title group
     * @param $data
     * @return boolean
     */
    public function titleGroupUpdate($data)
    {
        // 画面入力項目を設定
        $param = [
            'app_store_url'         => $data['app_store_url'] ?: null,
            'google_play_url'       => $data['google_play_url'] ?: null,
            'public_start_date'     => $data['begin'] ? $this->formatStoreDate($data['begin']) : null,
            'public_end_date'       => $data['end'] ? $this->formatStoreDate($data['end']) : null
        ];

        $deviceAppIds = $this->makeTitleGroupAppIds($data['app_id']);
        if ($data['id']) {
            // 整合性チェック
            $error = true;
            foreach ($deviceAppIds as $tableColumn => $appID) {
                if ($this->isTitleGroupExists($tableColumn, $appID, $data['id'], false) === true) {
                    $error = false;
                    break;
                }
            }
            if ($error) {
                return false;
            }
            // 更新の場合は画面入力項目のみを更新
            $this->applicationTitleGroupBySite->edit(['id' => $data['id']], $param);
        } else {
            // 登録の場合はアプリIDとサイトを追加で設定
            $param['main_app_id'] = $data['app_id'];
            $param += $deviceAppIds;

            $app = $this->application->getOne($data['app_id']);
            $param['site'] = empty($app->general) ? 'adult' : 'general';

            $this->applicationTitleGroupBySite->add($param);
        }
        return true;
    }

    /**
     * make update app ids for title group
     * @param $appID
     * @return array
     */
    public function makeTitleGroupAppIds($appID)
    {
        $ids = [];
        $devices = $this->applicationDevice->getListById($appID);
        $tableColumns = config('forms.ApplicationTitleGroup.tableColumn');
        foreach ($devices as $device) {
            if (array_key_exists($device->device, $tableColumns)) {
                $ids[$tableColumns[$device->device]] = $appID;
            }
        }
        return $ids;
    }

    /**
     * is title group exists
     * @param $device
     * @param $appID
     * @param $id
     * @param $idExclusion
     * @return boolean
     */
    public function isTitleGroupExists($device, $appID, $id = null, $idExclusion = true) {
        return !empty($this->applicationTitleGroupBySite->getGroupByAppId($device, $appID, $id, $idExclusion));
    }

    /**
     * ドメインタイプの指定が有効かを返す。
     * @param $appId アプリID
     * @param $device デバイス
     * @return boolean ドメインタイプの指定が有効か
     */
    public function isEnableDomainType($appId, $device)
    {
        if (!in_array($device, ['pc', 'sp'])) {
            return false;
        }
        
        $apps = config('forms.Games.enableDomainTypeApps');

        if (empty($apps)) {
            // 設定自体がない場合は全開放
            return true;
        }

        foreach (explode(',', $apps) as $app) {
            $appIdDevice = explode(':', $app);

            if (in_array($appId, $appIdDevice) && in_array($device, $appIdDevice)) {
                return true;
            }
        }

        return false;
    }

    /**
     * DBの日付データ取得。
     * @param string $date 日付を示す値。
     * @param boolean $fromDb trueの場合DB値を表示値に、falseの場合表示値をDB値に変換する。
     * @param boolean $format 表示値の日付フォーマット。
     * @return boolean ドメインタイプの指定が有効か
     */
    private function getDbDate($date, $fromDb, $format='Y/m/d H:i')
    {
        if ($fromDb) {
            if (!empty($date)) {
                $date = date($format, strtotime($date));
            }
        } else {
            if (empty($date)) {
                $date = null;
            } else {
                $date = str_replace('/', '-', $date);
            }
        }

        return $date;
    }

}
