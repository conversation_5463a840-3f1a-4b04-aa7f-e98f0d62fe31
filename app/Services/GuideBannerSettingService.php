<?php

namespace App\Services;

use App\Models\FreegameGuide\GuideApplication;
use App\Models\FreegameDeveloper\DeveloperGuideApplication;
use App\Models\FreegameGuide\GuideBanner;
use App\Models\FreegameGuide\GuideRotatingBanner;
use App\Models\FreegameGuide\GuideFixedBanner;
use App\Models\FreegameGuide\GuideRotatingBannerTmp;
use App\Models\FreegameGuide\GuideFixedBannerTmp;
use \Exception;
use Log;
use App\Models\Freegame\Freegame;

class GuideBannerSettingService extends CustomService
{
    protected $guideApp;
    protected $devGuideApp;
    protected $guideBanner;
    protected $guideRotatingBanner;
    protected $guideFixedBanner;
    protected $guideRotatingBannerTmp;
    protected $guideFixedBannerTmp;

    public function __construct(
        GuideApplication $guideApp,
        DeveloperGuideApplication $devGuideApp,
        GuideBanner $guideBanner,
        GuideRotatingBanner $guideRotatingBanner,
        GuideFixedBanner $guideFixedBanner,
        GuideRotatingBannerTmp $guideRotatingBannerTmp,
        GuideFixedBannerTmp $guideFixedBannerTmp
    ) {
        $this->guideApp = $guideApp;
        $this->devGuideApp = $devGuideApp;
        $this->guideBanner = $guideBanner;
        $this->guideRotatingBanner = $guideRotatingBanner;
        $this->guideFixedBanner = $guideFixedBanner;
        $this->guideRotatingBannerTmp = $guideRotatingBannerTmp;
        $this->guideFixedBannerTmp = $guideFixedBannerTmp;
    }

    /**
     * Get guide_rotating_banner List
     * @param  integer $guideAppId
     * @param boolean $isOnlyRotating
     * @return array
     */
    public function getListRotating($guideAppId, $isOnlyRotating = false)
    {
        $result = array();
        $getList = array();
        if ($isOnlyRotating) {
            $getList = $this->guideRotatingBanner->getListByGuideAppId($guideAppId);
        } else {
            $getList = $this->guideRotatingBanner->getListByGuideAppIdJoinGuideBanner($guideAppId);
        }
        foreach ($getList as $rotating) {
            $index = $rotating['priority'];
            $result[$index] = $rotating->toArray();
        }
        return $result;
    }

    /**
     * Get guide_fixed_banner List
     * @param  integer $guideAppId
     * @param boolean $isOnlyRotating
     * @return array
     */
    public function getListFixed($guideAppId, $isOnlyRotating = false)
    {
        $result = array();
        $getList = array();
        if ($isOnlyRotating) {
            $getList = $this->guideFixedBanner->getListByGuideAppId($guideAppId);
        } else {
            $getList = $this->guideFixedBanner->getListByGuideAppIdJoinGuideBanner($guideAppId);
        }
        foreach ($getList as $fixed) {
            $index = $fixed['placement'];
            $result[$index] = $fixed->toArray();
        }
        return $result;
    }

    /**
     * Get guide_fixed_banner
     * @param  integer $guideAppId
     * @param integer $placement
     * @return array
     */
    public function getOneFixed($guideAppId, $placement)
    {
        return $this->guideFixedBanner
            ->getOneByGuideAppIdAndPlacementJoinGuideBanner($guideAppId, $placement);
    }

    /**
     * Get guide_banner List
     * @param  integer $guideAppId
     * @return array
     */
    public function getListBannerName($guideAppId)
    {
        $result = ['' => '未設定'];
        $getList = $this->guideBanner->getListByGuideAppId($guideAppId);
        foreach ($getList as $banner) {
            $index = $banner['id'];
            $result[$index] = $banner['name'];
        }

        return $result;
    }

    /**
     * Get guide_application by id
     * @param  integer $id
     * @return array
     */
    public function getOneGuideApplication($id)
    {
        // ---------
        // DB
        $guideApplication = $this->guideApp->getOne($id);
        // ---------
        return $guideApplication->toArray();
    }

    /**
     * Update guide_rotating_banner to database
     * @param  array $request
     * @return boolean
     */
    public function editRotating($request)
    {
        $guideAppId = $request['guideAppId'];
        $editList = array_get($request, 'rotating', []);
        $update = array();

        // ---------
        // DB
        Freegame::beginTransaction();
        try {
            // ---------
            // 既存分を確認して、登録済みだが更新のリストにないものを削除
            $rotatingList = $this->getListRotating($guideAppId, true);
            foreach ($rotatingList as $rotating) {
                $priority = $rotating['priority'];
                if (empty($editList[$priority])) {
                    $this->guideRotatingBanner->del($rotating['id']);
                } else {
                    $update[$priority] = $rotating['id'];
                }
            }
            // ---------
            // 削除後の更新
            $result = false;
            foreach ($editList as $priority => $rotating) {
                // ---------
                // DB
                $guideBanner = $this->guideBanner->getOne($rotating['guide_banner_id'], $guideAppId);
                // ---------
                if (empty($guideBanner)) {
                    continue;
                }
                if (isset($update[$priority])) {
                    $updateRotating = [
                        'guide_application_id' => $guideAppId,
                        'guide_banner_id'      => $rotating['guide_banner_id'],
                        'priority'             => $priority,
                        'stamp'                => timestamp_to_sqldate(now_stamp()),
                    ];
                    $result = $this->guideRotatingBanner->edit($updateRotating, $update[$priority]);
                } else {
                    // 追加分はinsert
                    $insertRotating = [
                        'guide_application_id' => $guideAppId,
                        'guide_banner_id'      => $rotating['guide_banner_id'],
                        'priority'             => $priority,
                        'stamp'                => timestamp_to_sqldate(now_stamp()),
                    ];
                    // ---------
                    // DB
                    $result = $this->guideRotatingBanner->insert($insertRotating);
                    // ---------
                }
            }
            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            throw $e;
        }
        // ---------

        return $result;
    }

    /**
     * Update guide_fixed_banner to database
     * @param  array $request
     * @return boolean
     */
    public function editFixed($request)
    {
        $guideAppId = $request['guideAppId'];
        $editList = array_get($request, 'fixed', []);
        $update = array();

        // ---------
        // DB
        Freegame::beginTransaction();
        try {
            // ---------
            // 既存分を確認して、登録済みだが更新のリストにないものを削除
            $fixedList = $this->getListFixed($guideAppId, true);
            foreach ($fixedList as $fixed) {
                $priority = $fixed['placement'];
                if (empty($editList[$priority])) {
                    $this->guideFixedBanner->del($fixed['id']);
                } else {
                    $update[$priority] = $fixed['id'];
                }
            }
            // ---------
            // 削除後の更新
            $result = false;
            foreach ($editList as $priority => $fixed) {
                // ---------
                // DB
                $guideBanner = $this->guideBanner->getOne($fixed['guide_banner_id'], $guideAppId);
                // ---------
                if (empty($guideBanner)) {
                    continue;
                }
                if (isset($update[$priority])) {
                    $updateFixed = [
                        'guide_application_id' => $guideAppId,
                        'guide_banner_id'      => $fixed['guide_banner_id'],
                        'view_status'          => $fixed['view_status'],
                        'placement'            => $priority,
                        'stamp'                => timestamp_to_sqldate(now_stamp()),
                    ];
                    $result = $this->guideFixedBanner->edit($updateFixed, $update[$priority]);
                } else {
                    // 追加分はinsert
                    $insertFixed = [
                        'guide_application_id' => $guideAppId,
                        'guide_banner_id'      => $fixed['guide_banner_id'],
                        'view_status'          => $fixed['view_status'],
                        'placement'            => $priority,
                        'stamp'                => timestamp_to_sqldate(now_stamp()),
                    ];
                    // ---------
                    // DB
                    $result = $this->guideFixedBanner->insert($insertFixed);
                    // ---------
                }
            }
            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            throw $e;
        }
        // ---------

        return $result;
    }

    /**
     * Edit  rotating preview
     * @param  array $request
     * @return array $data
     */
    public function editRotatingPreview($request)
    {
        $guideAppId = $request['guideAppId'];
        $data = ['success' => false, 'preview_hash' => '', 'preview_url' => ''];
        $guideApplication = $this->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            $data['errors'][] = '運用サイト情報が取得できません。';
        } else {
            // フォームの値にすでにハッシュがある場合、古いプレビューなので削除をする
            $previewHash = $request['preview_hash'];
            if (empty($previewHash) === false) {
                // ---------
                // DB
                $this->delRotatingPreview($guideAppId, $previewHash);
                // ---------
            }
            list($previewHash, $previewUrl) = $this->getPreviewHashAndUrl(
                'rotating_banner',
                $guideApplication['domain'],
                '/banner/rotating'
            );

            if ($this->editRotatingBannerTmp($request, $previewHash)) {
                $data['success'] = true;
                $data['preview_hash'] = $previewHash;
                $data['preview_url'] = $previewUrl;
            } else {
                $data['success'] = false;
                $data['errors'][] = 'テンポラリデータの保存に失敗しました。';
            }
        }
        return $data;
    }
    /**
     * Edit  guide_rotating_banner_tmp table
     * @param  array $request
     * @param  string $previewHash
     * @return boolean
     */
    private function editRotatingBannerTmp($request, $previewHash)
    {
        $guideAppId = $request['guideAppId'];
        $guideBannerIdList = $request['rotating'];

        // 設定数 （Viewと共通）
        $settingNum = config('forms.GuideBannerSetting.rotatingSettingNum');
        Freegame::beginTransaction();
        try {
            // $makeInsertData
            for ($priority = 1; $priority <= $settingNum; $priority++) {
                if (empty($guideBannerIdList[$priority]['guide_banner_id'])) {
                    continue;
                }
                $setBannerId = $guideBannerIdList[$priority]['guide_banner_id'];
                $newRotatingTmp = [
                    'guide_application_id' => $guideAppId,
                    'guide_banner_id'      => $setBannerId,
                    'priority'             => $priority,
                    'preview_hash'         => $previewHash,
                    'stamp'                => timestamp_to_sqldate(now_stamp()),
                ];
                // ---------
                // DB
                $this->guideRotatingBannerTmp->insert($newRotatingTmp);
                // ---------
            }
            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            Log::error(var_export($e->getMessage(), true));
            return false;
        }
        return true;
    }

    /**
     * Edit  fixed preview
     * @param  array $request
     * @return array $data
     */
    public function editFixedPreview($request)
    {
        $guideAppId = $request['guideAppId'];
        $data = ['success' => false, 'preview_hash' => '', 'preview_url' => ''];
        $guideApplication = $this->getOneGuideApplication($guideAppId);
        if (empty($guideApplication)) {
            Log::error('Not Found guide_application : guide_application_id=' . $guideAppId);
            $data['errors'][] = '運用サイト情報が取得できません。';
        } else {
            // フォームの値にすでにハッシュがある場合、古いプレビューなので削除をする
            $previewHash = $request['preview_hash'];
            if (empty($previewHash) === false) {
                // ---------
                // DB
                $this->delFixedPreview($guideAppId, $previewHash);
                // ---------
            }
            list($previewHash, $previewUrl) = $this->getPreviewHashAndUrl(
                'fixed_banner',
                $guideApplication['domain'],
                '/banner/fixed'
            );

            if ($this->editFixedBannerTmp($request, $previewHash)) {
                $data['success'] = true;
                $data['preview_hash'] = $previewHash;
                $data['preview_url'] = $previewUrl;
            } else {
                $data['success'] = false;
                $data['errors'][] = 'テンポラリデータの保存に失敗しました。';
            }
        }

        return $data;
    }
    /**
     * Edit  guide_fixed_banner_tmp table
     * @param  array $request
     * @param  array $guideBannerIdList
     * @param  string $previewHash
     * @return boolean
     */
    private function editFixedBannerTmp($request, $previewHash)
    {
        $guideAppId = $request['guideAppId'];
        $guideBannerIdList = $request['fixed'];

        // 設定数 （Viewと共通）
        $settingNum = config('forms.GuideBannerSetting.fixedSettingNum');
        Freegame::beginTransaction();
        try {
            // $makeInsertData
            for ($priority = 1; $priority <= $settingNum; $priority++) {
                if (empty($guideBannerIdList[$priority]['guide_banner_id'])) {
                    continue;
                }
                $setBannerId = $guideBannerIdList[$priority]['guide_banner_id'];
                $setViewStatus = $guideBannerIdList[$priority]['view_status'];
                $newFixedTmp = [
                    'guide_application_id' => $guideAppId,
                    'guide_banner_id'      => $setBannerId,
                    'view_status'          => $setViewStatus,
                    'placement'            => $priority,
                    'preview_hash'         => $previewHash,
                    'stamp'                => timestamp_to_sqldate(now_stamp()),
                ];
                // ---------
                // DB
                $this->guideFixedBannerTmp->insert($newFixedTmp);
                // ---------
            }
            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            Log::error(var_export($e->getMessage(), true));
            return false;
        }
        return true;
    }

    /**
     * Get  Preview Hash And Preview Url
     * @param  string $preSeed
     * @param  string $domain
     * @param  string $previewPath
     * @return array(
     *  string $previewHash
     *  string $previewUrl
     * )
     */
    private function getPreviewHashAndUrl($preSeed, $domain, $previewPath)
    {
        // SHA1ハッシュ化（ソルト値＋特定の値)
        $preHashSeed = $preSeed . date('YmdGis', now_stamp());
        $previewHash = hash(
            'sha1',
            env('AUTH_PASSWORD_SALT', 'SomeRandomString')
            . $preHashSeed
            . $preSeed
            . date('YmdGis', now_stamp())
        );
        // プレビューのurl生成
        $previewUrl = adjustment_path($domain);
        $previewUrl .= '/preview' . $previewPath . '/' . $previewHash . '/';
        return [$previewHash, $previewUrl];
    }

    /**
     * delete  rotating preview to database
     * @param  integer $guideAppId
     * @param  string $previewHash
     * @return
     */
    public function delRotatingPreview($guideAppId, $previewHash)
    {
        return $this->guideRotatingBannerTmp->delByPreviewHash($guideAppId, $previewHash);
    }

    /**
     * delete  Fixed preview to database
     * @param  integer $guideAppId
     * @param  string $previewHash
     * @return
     */
    public function delFixedPreview($guideAppId, $previewHash)
    {
        return $this->guideFixedBannerTmp->delByPreviewHash($guideAppId, $previewHash);
    }

    /**
     * Check Edit Permission
     * @return boolean
     */
    public function isEnableEdit($guideAppId)
    {
        $userId = auth_user_id();
        if (empty($userId)) {
            return false;
        }
        if (auth_is_pf()) {
            return true;
        } else {
            $idList = $this->devGuideApp->getListGuideAppIdByDevId($userId);
            if (in_array($guideAppId, $idList->toArray())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get values
     *
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.GuideBannerSetting');

        return [
            'screenName'  => $privateConfigs['screenName'],
            'menuName'  => $privateConfigs['menuName'],
            'rotatingSettingNum'  => $privateConfigs['rotatingSettingNum'],
            'fixedSettingNum'  => $privateConfigs['fixedSettingNum'],
            'fixedViewStatus'  => $privateConfigs['fixedViewStatus'],
        ];
    }
}
