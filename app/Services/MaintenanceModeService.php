<?php
namespace App\Services;

use Exception;

/**
 * メンテナンスモード
 */
class MaintenanceModeService extends CustomService
{

    public function getMessage($data)
    {
        return "\n\n"
            . "現在メンテナンス中のため、しばらくの間ご利用いただけません。"
            . "\n\n"
            . sprintf("メンテナンス時間帯【 %s ～ %s 】", $data['begin'], $data['end'])
            . "\n\n"
            . "ご迷惑をお掛けし申し訳ございませんが、何卒よろしくお願いいたします。"
            . "\n\n";
    }

    public function formatData($data)
    {
        if (empty($data)) {
            $data['exists'] = false;
        } else {
            if ($data['begin'] <= time() && time() <= $data['end']) {
                $data['status'] = 'active';
            } elseif (time() < $data['begin']) {
                $data['status'] = 'before';
            } elseif ($data['end'] < time()) {
                $data['status'] = 'finish';
            }
            $data['begin'] = date('Y/m/d H:i', $data['begin']);
            $data['end'] = date('Y/m/d H:i', $data['end']);
            $data['exists'] = true;
        }
        return $data;
    }

    public function getData()
    {
        try {
            $data = $this->sendXmlRpcData([
                'message' => 'Developer_MainteSetting.GetSettingDeveloper'
            ]);
        } catch (Exception $e) {
            $data = [];
        }
        if (isset($data[0])) {
            $data = unserialize($data[0]);
        }
        return $this->formatData($data);
    }
}
