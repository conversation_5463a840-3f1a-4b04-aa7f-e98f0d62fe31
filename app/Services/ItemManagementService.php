<?php

namespace App\Services;

use App\Models\Freegame\Application;
use App\Models\FreegameDeveloper\Developer;
use App\Models\FreegameDeveloper\DeveloperApplication;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

class ItemManagementService extends CustomService
{
    /** @var Application */
    protected $application;
    /** @var Developer */
    protected $developer;
    /** @var DeveloperApplication */
    protected $developerApplication;
    /** @var ReceiptApiService */
    protected $receiptApiService;

    // アプリケーションタイトル一覧
    protected $applicationTitleList;

    // ロケール
    const LOCALE_JP = 'ja-JP';
    // 取得したマイクロ単位の価格をローカライズするための値
    const AMOUNT_LOCALIZE_JP = 1000000;

    /**
     * ItemManagementService constructor.
     * @param Application $application
     * @param Developer $developer
     * @param DeveloperApplication $developerApplication
     * @param ReceiptApiService $receiptApiService
     */
    public function __construct(
        Application $application,
        Developer $developer,
        DeveloperApplication $developerApplication,
        ReceiptApiService $receiptApiService
    )
    {
        $this->application = $application;
        $this->developer = $developer;
        $this->developerApplication = $developerApplication;
        $this->receiptApiService = $receiptApiService;
    }

    /**
     * Get values
     *
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.ItemManagement');

        return [
            'appTitleType' => $this->getApplicationTitleList(),
            'menuName' => $privateConfigs['menuName'],
            'screenName' => $privateConfigs['screenName'],
            'subScreenName' => $privateConfigs['subScreenName'],
        ];
    }

    /**
     * アプリケーションタイトル一覧取得
     *
     * @return array
     */
    public function getApplicationTitleList()
    {
        $deviceList = $this->receiptApiService->getSupportedDevice();

        if (isset($this->applicationTitleList)) {
            return $this->applicationTitleList;
        }

        // アプリケーションタイトル一覧取得
        if (auth_is_sap()) {
            $list = $this->getApplicationTitleListForSap($deviceList);
        } else {
            // PFアカウントの場合は全件取得
            $list = $this->application->getApplicationTitleListByDevice($deviceList);
        }

        $opts = [];
        foreach ($list as $data) {
            $opts[$data->id][$data->device] = $this->receiptApiService->convertDisplayTitle($data->title, $data->device);
        }
        $this->applicationTitleList = ['' =>  ['' => '選択してください']] + $opts;

        return $this->applicationTitleList;
    }

    /**
     * アプリケーションタイトル一覧取得(Sapアカウント)
     *
     * @param array $deviceList
     * @return array
     */
    private function getApplicationTitleListForSap($deviceList)
    {
        // ログイン情報を元に権限があるアプリケーションリストを取得する
        $developerApplicationList = $this->developerApplication->getApplicationAppIdList([
            'developer_id' => auth_user_id()
        ]);

        // タイトル情報取得
        if (empty($developerApplicationList->count())) {
            return [];
        } else {
            $applicationIdList = [];
            foreach ($developerApplicationList as $data) {
                array_push($applicationIdList, $data->app_id);
            }
            $list = $this->application->getApplicationTitleListByDeviceAndApplicationIdList($deviceList, $applicationIdList);
        }
        return $list;
    }

    /**
     * アイテム一覧の取得
     *
     * @param array $searchCondition
     * @return array['productList'=>Collection, 'informationLabel'=>string]
     * @return array
     */
    public function getItemList($searchCondition)
    {
        $itemList['productList'] = collect([]);
        $itemList['totalCount'] = 0;

        // レシート型課金側のアプリケーション情報取得
        $receiptApplication = $this->receiptApiService->getReceiptApplication($searchCondition['app_id'], $searchCondition['device']);

        // アプリケーション情報取得チェック
        if (!array_key_exists('id', $receiptApplication['resultData'])) {
            // アプリケーション情報がない場合はメッセージを設定する
            $itemList['informationLabel'] = $receiptApplication['resultMessage'];
            return $itemList;
        }

        // 製品情報一覧取得
        $receiptProductData = $this->receiptApiService->getReceiptProductList(
            $receiptApplication['resultData']['id'],
            $searchCondition['item_id'],
            $searchCondition['item_name'],
            $searchCondition['status'],
            $searchCondition['limit'],
            $searchCondition['offset']
        );
        // 製品情報取得チェック
        if (!$receiptProductData['resultStatus']) {
            $itemList['informationLabel'] = $receiptProductData['resultMessage'];
            return $itemList;
        }

        foreach ($receiptProductData['response']['products'] as $productData) {
            // 製品価格情報取得
            $receiptPriceData = $this->receiptApiService->getReceiptPriceData($productData['id']);
            // 製品価格情報取得チェック
            if (!($receiptPriceData['resultStatus'])) {
                $itemList['informationLabel'] = $receiptPriceData['resultMessage'];
                return $itemList;
            }

            // 表示用アイテム情報の生成
            $itemData = $this->createItemData($productData, $receiptPriceData['response']['prices']);
            $itemList['productList']->push($itemData);
        }

        // アイテム未登録
        if ($itemList['productList']->isEmpty()) {
            $itemList['informationLabel'] = config('forms.ItemManagement.informationLabel.items_unregistered');
            return $itemList;
        }

        // 更新日順にソートする
        $itemList['productList'] = $itemList['productList']->sortByDesc('updatedAt');
        $itemList['totalCount'] = $receiptProductData['response']['totalNumber'];

        return $itemList;
    }

    /**
     * 製品情報と製品価格情報から表示するアイテム情報を生成する
     *
     * @param array $productData
     * @param array $receiptPriceDataList
     * @return array
     */
    private function createItemData($productData, $receiptPriceDataList)
    {
        if (empty($receiptPriceDataList)) {
            // 製品価格情報が存在しない場合
            $productDataUpdatedAt = Carbon::createFromTimestamp($productData['updatedAt']);
            $itemData = [
                'id' => $productData['id'],
                'sku' => $productData['sku'],
                'title' => $productData['title'],
                'amount' => '-',
                'updatedAt' => $productDataUpdatedAt->format('Y/m/d H:i:s'),
                'status' => config('forms.ItemManagement.status.' . $productData['status']),
            ];
        } else {
            // 製品価格情報が存在する場合
            $receiptPriceData = current($receiptPriceDataList);
            $itemData = [
                'id' => $productData['id'],
                'sku' => $productData['sku'],
                'title' => $productData['title'],
                'amount' => $this->createDisplayAmount($receiptPriceData['amount'], $receiptPriceData['locale']),
                'updatedAt' => $this->getLastUpdatedAt($productData, $receiptPriceData),
                'status' => config('forms.ItemManagement.status.' . $productData['status']),
            ];
        }
        return $itemData;
    }

    /**
     * アイテムcsv サンプルダウンロード
     *
     * @return view
     */
    public function downloadSampleCsv()
    {
        $headers = array(
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="sample.csv"',
        );
        
        $csv =<<<END
Product ID,Published State,Purchase Type,Auto Translate,Locale; Title; Description,Auto Fill Prices,Price,Pricing Template ID
com.dmm.games.testgame.stone_1,unpublished,managed_by_android,false,ja_JP; 魔法石30個＋ボーナス魔法石25個; テストゲーム　魔法石30個＋ボーナス魔法石25個,false,JP; 3600000000,
com.dmm.games.testgame.stone_2,unpublished,managed_by_android,false,ja_JP; 魔法石75個＋ボーナス魔法石50個; テストゲーム　魔法石75個＋ボーナス魔法石50個,false,JP; 10000000000,
END;

        return \Response::make($csv, 200, $headers);
    }

    /**
     * アイテムcsvインポート処理
     * こちらはGooglePlayでエクスポートされるcsvファイルを取り込む機能になっています。
     * TODO GooglePlayの出力形式が変更された際に処理を見直す必要があります。
     * 【SYS3-06_CSVインポート機能について】
     * https://confl.arms.dmm.com/pages/viewpage.action?pageId=265077145
     *
     * @param $applicationId
     * @param $csvData
     * @param $device
     * @return array
     */
    public function itemCsvImport($applicationId, $csvData, $device)
    {
        // 戻り値初期定義
        $importResult = [
            'successCount' => 0, // 登録処理成功件数
            'failureCount' => 0, // 登録処理失敗件数
            'failureProductIdList' => [], // 登録に失敗した製品IDリスト
        ];

        // 読み込んだCSVデータを登録しやすい形に整形
        $csvDataList = $this->csvDataFormat($csvData);

        // 整形したデータを1レコードごとに登録する
        foreach ($csvDataList as $itemDataList) {
            try {
                // 同一カラムに複数定義されているデータを配列に変換する
                $localeTitleDescriptionList = $this->localeTitleDescriptionToArray($itemDataList['localeTitleDescription']);
                $priceList = $this->priceToArray($itemDataList['price']);

                // 製品情報登録処理
                $itemData = $this->createImportProductData($applicationId, $itemDataList, $localeTitleDescriptionList);
                // バリデーション処理
                $productValidationCheckResult = $this->productValidationCheck($itemData);
                if (!$productValidationCheckResult) {
                    $importResult['failureCount']++;
                    array_push($importResult['failureProductIdList'], $itemDataList['productID']);
                    continue;
                }
                // 製品情報登録API実行
                $storeProductResult = $this->receiptApiService->storeReceiptProductData($itemData, $device);
                // 製品情報登録チェック
                if (!$storeProductResult['resultStatus']) {
                    $importResult['failureCount']++;
                    array_push($importResult['failureProductIdList'], $itemDataList['productID']);
                    continue;
                }

                // 登録結果から製品価格情報登録に必要な情報を取り出す
                $productId = $storeProductResult['response']['id'];

                // 製品価格情報登録処理
                $itemPriceData = $this->createImportPriceData($productId, $localeTitleDescriptionList, $priceList);
                // バリデーションチェック処理
                $priceValidationCheckResult = $this->priceValidationCheck($itemPriceData);
                if (!$priceValidationCheckResult) {
                    $importResult['failureCount']++;
                    array_push($importResult['failureProductIdList'], $itemDataList['productID']);
                    continue;
                }
                // 製品価格情報登録API実行
                $storePriceResult = $this->receiptApiService->storeReceiptPriceData($itemPriceData);
                // 製品価格情報登録チェック
                if (!$storePriceResult['resultStatus']) {
                    $importResult['failureCount']++;
                    array_push($importResult['failureProductIdList'], $itemDataList['productID']);
                    continue;
                }

                // 登録処理成功、成功件数加算
                $importResult['successCount']++;
            } catch (\Exception $e) {
                // csvデータに予期しない値が定義されたときのため
                $importResult['failureCount']++;
                array_push($importResult['failureProductIdList'], $itemDataList['productID']);
            }
        }
        return $importResult;
    }

    /**
     * csvデータを登録しやすい形に整形する
     *
     * @param $csvData
     * @return array
     */
    private function csvDataFormat($csvData)
    {
        // 各行を処理
        $csvDataList = [];
        foreach ($csvData as $rowCount => $row) {
            // 1行目はキーヘッダ行として取り込み
            if ($rowCount === 0) {
                foreach($row as $colCount => $col) {
                    $search = [' ', ';'];
                    $keyList[$colCount] = str_replace($search, '', lcfirst($col));
                }
                continue;
            }

            // 2行目以降はデータ行として取り込み
            $line = [];
            foreach ($keyList as $colCount => $col) {
                $line[$keyList[$colCount]] = $row[$colCount];
            }
            array_push($csvDataList, $line);
        }
        return $csvDataList;
    }

    /**
     * Locale,Title,Descriptionがまとめて定義されているので配列に変換する。
     *
     * @param string $localeTitleDescription
     * @return array
     */
    private function localeTitleDescriptionToArray($localeTitleDescription)
    {
        $titles = explode('; ', $localeTitleDescription);
        $dataList = [];
        $chunks = collect($titles)->chunk(3)->toArray();
        foreach ($chunks as $key => $data) {
            $dataList[$data[0]] = [
                'title' => $data[1],
                'description' => $data[2],
            ];
        }
        return $dataList;
    }

    /**
     * priceに国情報がまとめて定義されているので分解する。
     *
     * @param string $price
     * @return array
     */
    private function priceToArray($price)
    {
        $titles = explode('; ', $price);
        $dataList = [];
        $chunks = collect($titles)->chunk(2)->toArray();
        foreach ($chunks as $key => $data) {
            $dataList[$data[0]] = [
                'price' => $data[1],
            ];
        }
        return $dataList;
    }

    /**
     * 製品情報バリデーションチェック
     *
     * @param array $productData
     * @return bool
     */
    private function productValidationCheck($productData)
    {
        $validator = Validator::make($productData, [
            'sku' => 'required|regex:/^[0-9a-z]{1}[0-9a-z_.]+$/|max:64',
            'title' => 'required|max:55',
            'description' => 'required|max:80',
            'status' => 'required',
        ]);
        // バリデーションチェックエラー
        if ($validator->fails()) {
            return false;
        }
        return true;
    }

    /**
     * 製品価格情報バリデーションチェック
     *
     * @param array $priceData
     * @return bool
     */
    private function priceValidationCheck($priceData)
    {
        $validator = Validator::make($priceData, [
            'amount'        => 'required|integer|between:1,999999990000',
            'title'         => 'required|max:55',
            'description'   => 'required|max:80',
        ]);
        // バリデーションチェックエラー
        if ($validator->fails()) {
            return false;
        }
        return true;
    }

    /**
     * インポートしたデータから登録用の製品情報を作成する
     *
     * @param string $applicationId
     * @param array $itemDataList
     * @param array $localeTitleDescriptionList
     * @return array
     */
    private function createImportProductData($applicationId, $itemDataList, $localeTitleDescriptionList)
    {
        $itemDataList = [
            'app_id' => $applicationId,
            'description' => $localeTitleDescriptionList['ja_JP']['description'],
            'platform' => 'ANDROID_EMULATOR', // 現状はエミュレーターのみ対象のため固定で設定
            'sku' => $itemDataList['productID'],
            'status' => strtoupper($itemDataList['publishedState']), // csvの定義は小文字のため大文字に変換する
            'title' => $localeTitleDescriptionList['ja_JP']['title'],
            'type' => 'MANAGED', // レシート課金側に確認したところ現状は「MANAGED」固定で問題ないとのことなので固定で設定
        ];
        return $itemDataList;
    }

    /**
     * インポートしたデータから登録用の製品価格情報を作成する
     *
     * @param string $productId
     * @param array $localeData
     * @param array $priceData
     * @return array
     */
    private function createImportPriceData($productId, $localeData, $priceData)
    {
        $priceData = [
            'amount' => $priceData['JP']['price'],
            'description' => $localeData['ja_JP']['description'],
            'locale' => 'ja-JP', // 現状はJP固定で設定する
            'product_id' => $productId,
            'title' => $localeData['ja_JP']['title'],
        ];
        return $priceData;
    }

    /**
     * アイテム管理情報の登録
     *
     * @param array $productData
     * @param string $device
     * @return array
     */
    public function storeProduct($productData, $device)
    {
        $storeResult = $this->receiptApiService->storeReceiptProductData($productData, $device);
        return $storeResult;
    }

    /**
     * アイテム詳細情報の取得
     *
     * @param string $productId
     * @return array
     */
    public function getItemDetail($productId)
    {
        // 製品情報取得
        $receiptProductData = $this->receiptApiService->getReceiptProductData($productId);
        // 製品情報取得チェック
        if (!$receiptProductData['resultStatus']) {
            $itemDetail['informationLabel'] = $receiptProductData['resultMessage'];
            return $itemDetail;
        }
        // 取得した製品情報の空チェック
        if (empty($receiptProductData['response'])) {
            $itemDetail['informationLabel'] = config('forms.ItemManagement.informationLabel.items_unregistered');
            return $itemDetail;
        }

        // 製品価格情報取得
        $receiptPriceData = $this->receiptApiService->getReceiptPriceData($productId);
        // 製品価格情報取得チェック
        if (!$receiptPriceData['resultStatus']) {
            $itemDetail['informationLabel'] = $receiptPriceData['resultMessage'];
            return $itemDetail;
        }

        // 表示用のアイテム詳細情報を生成
        $itemDetail = $this->createItemDetailData($receiptProductData['response'], $receiptPriceData['response']['prices']);

        return $itemDetail;
    }

    /**
     * 製品情報と製品価格情報からアイテム詳細画面に表示するアイテム情報を生成する
     *
     * @param array $productData
     * @param array $priceDataList
     * @return array
     */
    private function createItemDetailData($productData, $priceDataList)
    {
        $itemDetailData['product'] = [
            'productId' => $productData['id'],
            'sku' => $productData['sku'],
            'title' => $productData['title'],
            'description' => $productData['description'],
            'status' => config('forms.ItemManagement.status.' . $productData['status']),
        ];

        if (!empty($priceDataList)) {
            $priceData = current($priceDataList);
            $itemDetailData['price'] = [
                'priceId' => $priceData['id'],
                'amount' => $this->createDisplayAmount($priceData['amount'], $priceData['locale']),
                'title' => $priceData['title'],
                'description' => $priceData['description'],
            ];
        }
        return $itemDetailData;
    }

    /**
     * アイテム管理情報の編集
     *
     * @param string $productId
     * @return array
     */
    public function editProduct($productId)
    {
        // 製品情報取得
        $receiptProductData = $this->receiptApiService->getReceiptProductData($productId);
        // 製品情報取得チェック
        if (!$receiptProductData['resultStatus']) {
            $editProductData['informationLabel'] = $receiptProductData['resultMessage'];
            return $editProductData;
        }
        // 取得した製品情報の空チェック
        if (empty($receiptProductData['response'])) {
            $editProductData['informationLabel'] = config('forms.ItemManagement.informationLabel.items_unregistered');
            return $editProductData;
        }
        $editProductData['productData'] = $receiptProductData['response'];
        return $editProductData;
    }

    /**
     * アイテム管理情報の更新
     *
     * @param array $productData
     * @return boolean
     */
    public function updateProduct($productData)
    {
        $updateResult = $this->receiptApiService->updateReceiptProductData($productData);
        return $updateResult['resultStatus'];
    }

    /**
     * 販売情報の登録
     *
     * @param array $priceData
     * @return boolean
     */
    public function storePrice($priceData)
    {
        // TODO 多言語化対応時に指定の言語で変換するように修正する。
        $priceData['amount'] = $this->createMicroUnitAmount($priceData['amount'], self::LOCALE_JP);
        $storeResult = $this->receiptApiService->storeReceiptPriceData($priceData);
        return $storeResult['resultStatus'];
    }

    /**
     * 販売情報の編集
     *
     * @param string $productId
     * @return array
     */
    public function editPrice($productId)
    {
        // 製品価格情報取得
        $receiptPriceData = $this->receiptApiService->getReceiptPriceData($productId);
        // 製品価格情報取得チェック
        if (!$receiptPriceData['resultStatus']) {
            $editPriceData['informationLabel'] = $receiptPriceData['resultMessage'];
            return $editPriceData;
        }
        // 取得した製品価格情報の空チェック
        if (empty($receiptPriceData['response']['prices'])) {
            $editPriceData['informationLabel'] = config('forms.ItemManagement.informationLabel.items_unregistered');
            return $editPriceData;
        }
        $editPriceData['priceData'] = current($receiptPriceData['response']['prices']);
        // 価格情報を表示用に変換
        $displayAmount = $this->createDisplayAmount($editPriceData['priceData']['amount'], $editPriceData['priceData']['locale']);
        $editPriceData['priceData']['amount'] = str_replace(',', '', $displayAmount);
        return $editPriceData;
    }

    /**
     * 販売情報の更新
     *
     * @param array $priceData
     * @return boolean
     */
    public function updatePrice($priceData)
    {
        // 価格を表示用の単位から登録用の単位に変換
        $priceData['amount'] = $this->createMicroUnitAmount($priceData['amount'], $priceData['locale']);
        $updateResult = $this->receiptApiService->updateReceiptPriceData($priceData);
        return $updateResult['resultStatus'];
    }

    /**
     * アイテム情報の削除
     *
     * @param string $productId
     * @return boolean
     */
    public function deleteProduct($productId)
    {
        $deleteReceiptProduct = $this->receiptApiService->deleteReceiptProductData($productId);
        return $deleteReceiptProduct['resultStatus'];
    }

    /**
     * 表示用の価格情報を生成する
     * レシート課金側で登録されている価格情報は、マイクロ単位の価格になっているので、
     * 表示する際は特定の通貨にローカライズする必要があります。
     *
     * @param integer $amount
     * @param string $locale
     * @return string
     */
    private function createDisplayAmount($amount, $locale)
    {
        $displayAmount = '';
        if ($locale === self::LOCALE_JP) {
            $displayAmount = number_format($amount / self::AMOUNT_LOCALIZE_JP);
        }
        return $displayAmount;
    }

    /**
     * 登録用の価格情報を生成する
     * レシート課金側で登録されている価格情報は、マイクロ単位の価格になっているので、
     * 登録する際はマイクロ単位に変換が必要になります。
     *
     * @param string $amount
     * @param string $locale
     * @return string
     */
    private function createMicroUnitAmount($amount, $locale)
    {
        $microUnitAmount = '';
        if ($locale === self::LOCALE_JP) {
            $microUnitAmount = $amount * self::AMOUNT_LOCALIZE_JP;
        }
        return $microUnitAmount;
    }

    /**
     * 最新の更新日時を返却する
     *
     * @param array $productData
     * @param array $receiptPriceData
     * @return string
     */
    private function getLastUpdatedAt($productData, $receiptPriceData)
    {
        $productDataUpdatedAt = Carbon::createFromTimestamp($productData['updatedAt']);
        $receiptPriceDataUpdatedAt = Carbon::createFromTimestamp($receiptPriceData['updatedAt']);
        if ($productDataUpdatedAt->gte($receiptPriceDataUpdatedAt)) {
            $lastUpdateAt = $productDataUpdatedAt;
        } else {
            $lastUpdateAt = $receiptPriceDataUpdatedAt;
        }
        return $lastUpdateAt->format('Y/m/d H:i:s');
    }
}
