<?php

namespace App\Services;

use App\Models\FreegameGuide\GuideApplication;
use App\Models\FreegameDeveloper\DeveloperGuideApplication;
use \Exception;

class GuideApplicationService extends CustomService
{
    protected $guideApp;
    protected $devGuideApp;

    public function __construct(
        GuideApplication $guideApp,
        DeveloperGuideApplication $devGuideApp
    ) {
        $this->guideApp = $guideApp;
        $this->devGuideApp = $devGuideApp;
    }

    /**
     * Get guide_application List
     * @param  array $condition
     * @return array
     */
    public function getList()
    {
        $userId = auth_user_id();
        if (empty($userId)) {
            return [];
        }

        $guideAppList = array();
        if (auth_is_pf()) {
            $guideAppList = $this->guideApp->getListAll();
        } else {
            $idList = $this->devGuideApp->getListGuideAppIdByDevId($userId);
            $guideAppList = $this->guideApp->getListByIdLists($idList);
        }

        return $guideAppList;
    }

    /**
     * Get guide_application by id
     * @param  integer $id
     * @return array
     */
    public function getOne($id)
    {
        // ---------
        // DB
        $guideApplication = $this->guideApp->getOne($id);
        // ---------
        return $guideApplication;
    }

    /**
     * Insert guide_application to database
     * @param  array $request
     * @return boolean
     */
    public function create($request)
    {
        // Request で値はチェック済み
        $newGuideApp = [
            'name'         => $request['name'],
            'domain'   => $request['domain'],
            'integrated_inquiry'   => $request['integrated_inquiry']
        ];
        // ---------
        // DB
        $result = $this->guideApp->insert($newGuideApp);
        // ---------

        return $result;
    }

    /**
     * Update guide_application to database
     * @param  array $request
     * @return boolean
     */
    public function edit($request)
    {
        // Request で値はチェック済み
        $editGuideApp = [
            'name'         => $request['name'],
            'domain'   => $request['domain'],
            'integrated_inquiry'   => $request['integrated_inquiry']
        ];
        // ---------
        // DB
        $result = $this->guideApp->edit($editGuideApp, $request['id']);
        // ---------

        return $result;
    }

    /**
     * Check Edit Permission
     * @return boolean
     */
    public function isEnableEdit($guideAppId)
    {
        $userId = auth_user_id();
        if (empty($userId)) {
            return false;
        }
        if (auth_is_pf()) {
            return true;
        } else {
            $idList = $this->devGuideApp->getListGuideAppIdByDevId($userId);
            if (in_array($guideAppId, $idList->toArray())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get values
     *
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.GuideApplication');

        return [
            'screenName'  => $privateConfigs['screenName'],
            'menuName'  => $privateConfigs['menuName'],
        ];
    }
}
