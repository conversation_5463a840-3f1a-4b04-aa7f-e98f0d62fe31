<?php
namespace App\Services;

use App\Models\Freegame\Application;
use App\Models\Freegame\ItemPurchaseOrderLog;
use App\Models\FreegameDeveloper\DeveloperApplication;
use App\Models\FreegameDeveloper\PointOrderHistory;
use Exception;

/**
 * 課金履歴ダウンロード(月別)
 */
class PointReportsMonthlyService extends CustomService
{
    protected $application;
    protected $itemPurchaseOrderLog;
    protected $couponApiService;
    protected $developerApplication;
    protected $pointOrderHistory;

    public function __construct(
        Application                 $application,
        ItemPurchaseOrderLog        $itemPurchaseOrderLog,
        CouponApiService            $couponApiService,
        DeveloperApplication        $developerApplication,
        PointOrderHistory           $pointOrderHistory
    ) {
        $this->application                 = $application;
        $this->itemPurchaseOrderLog        = $itemPurchaseOrderLog;
        $this->couponApiService            = $couponApiService;
        $this->developerApplication        = $developerApplication;
        $this->pointOrderHistory           = $pointOrderHistory;
    }

    /**
     * CSVファイル名取得
     *
     * @param  object $condition
     *
     * @return string
     *
     */
    public function getCsvFileName($condition = [])
    {
        if (empty($condition['date'])) {
            $condition['begin'] = '1970/01/01';
        }
        return sprintf(
            config('forms.PointReportsMonthly.CsvFileName'),
            date('Y-m', strtotime($condition['date'])),
            $condition['app_id']
        );
    }

    /**
     * CSVヘッダー取得
     *
     * @param  object $condition
     *
     * @return array  $header
     *
     */
    public function getCsvHeader($condition = [])
    {
        $header = array();
        $header['app_id']               = 'app_id';
        $header['payment_id']           = 'payment_id';
        $header['item_id']              = 'item_id';
        $header['unit_price']           = 'unit_price';
        $header['quantity']             = 'quantity';
        $header['total_point']          = 'total_point';
        $header['date']                 = 'date';
        $header['device']               = 'device';
        $header['is_staff']             = 'is_staff';
        $header['status']               = 'status';
        $header['pay_amount_coupon']    = 'pay_amount_coupon';
        $header['discount_use_point']   = 'discount_use_point';
        $header['publisher_floor_id']   = 'publisher_floor_id';

        return $header;
    }

    /**
     * CSV出力データ取得
     *
     * @param  object $condition
     *
     * @return array  $list
     *
     */
    public function getCsvList($condition = [])
    {
        $list = array();
        $bindings = [];
        $period  = date('Y-m', strtotime($condition['date']));
        $lastday = date('t', strtotime($period.'-01'));

        $params = [
            'app_id' => $condition['app_id'],
            'begin'  => $period.'-01 00:00:00',
            'end'    => $period.'-'.$lastday.' 23:59:59',
            'order'  => ['entry_date', 'asc'],
        ];

        if (!\App::environment('develop') && !\App::environment('local')) {
            $params['partition'] = $this->getPartition(strtotime($params['begin']), strtotime($params['end']));
        }

        $sql = $this->pointOrderHistory->getPointOrderHistory($params, true, $bindings);
        
        $result = $this->pointOrderHistory->getExportCsvData($sql, $bindings);

        $list = array();
        while ($data = $result->fetch()) {
            $listTmp = array();
            $listTmp['app_id']      = $data->app_id;
            $listTmp['payment_id']  = $data->payment_id;
            $listTmp['item_id']     = $data->item_id;
            $listTmp['unit_price']  = $data->unit_price;
            $listTmp['quantity']    = $data->quantity;
            $listTmp['total_point'] = $data->total_point;
            $listTmp['date']        = $data->entry_date;
            $listTmp['device']      = $data->device;
            $listTmp['is_staff']    = $data->is_staff;
            $listTmp['status']      = $data->status;
            // クーポンを使用していない決済もあるので一旦クーポン未使用の値を設定しておく
            $listTmp['pay_amount_coupon'] = 0;
            $listTmp['discount_use_point'] = $data->total_point;
            $listTmp['publisher_floor_id'] = "";

            $list[] = $listTmp;
        }

        // paymentIDに対応する決済情報の取得
        $paymentIdList = array_pluck($list, 'payment_id');
        $orderLogs = collect([]);
        foreach (array_chunk($paymentIdList, 1000) as $paymentIdListChunk) {
            $paymentIds = $this->itemPurchaseOrderLog->getListByPaymentIds($paymentIdListChunk);
            $orderLogs = $orderLogs->merge($paymentIds);
        }
        if (count($orderLogs->count()) <= 0) {
            // 決済情報が存在しなければクーポン決済が行われていないものとしてそのまま返す
            $this->formatExportValues($list);
            return;
        }

        // クーポンIDに対応する費用負担元の配列を作成
        $couponIds = $orderLogs->pluck('coupon_id')->filter(function ($item) {
            return !empty($item);
        })->values()->all();
        if (empty($couponIds)) {
            // クーポン決済が行われていない場合はこれ以上処理を行わない
            $this->formatExportValues($list);
            return;
        }
        // クーポンAPIからクーポン詳細情報を取得する
        $apiResponse = $this->couponApiService->getCouponDetail($couponIds);
        if ($apiResponse['resultCode'] != 200) {
            throw new Exception($apiResponse['resultMessage']);
        } else if (empty($apiResponse['response'])) {
            // ステータスは正常だがレスポンスが空の場合クーポンIDが正しく渡されなかったためこれ以上処理を行わない
            $this->formatExportValues($list);
            return;
        }
        $publisherFloorIdList = array();
        foreach ($apiResponse['response']['coupons'] as $couponDetail) {
            $publisherFloorIdList[$couponDetail['id']] = $couponDetail['publisher_floor_id'];
        }

        // paymentIDに対応する使用クーポンの値引額とIDの配列を作成
        $useCouponList = array();
        foreach ($orderLogs as $orderLog) {
            $useCouponList[$orderLog['payment_id']] = [
                'pay_amount_coupon' => $orderLog['pay_amount_coupon'],
                'coupon_id'         => $orderLog['coupon_id'],
            ];
        }

        // クーポンに関する情報をCSV出力リストに反映
        foreach ($list as $key => $data) {
            if (!array_key_exists($data['payment_id'], $useCouponList)) {
                continue;
            }

            $useCoupon = $useCouponList[$data['payment_id']];
            $list[$key]['pay_amount_coupon'] = $useCoupon['pay_amount_coupon'];
            $list[$key]['discount_use_point'] -= $list[$key]['pay_amount_coupon'];

            if (array_key_exists($useCoupon['coupon_id'], $publisherFloorIdList)) {
                $list[$key]['publisher_floor_id'] = $publisherFloorIdList[$useCoupon['coupon_id']];
            }
        }

        $this->formatExportValues($list);
    }

    /**
     * Format result values
     * @param  array $list
     */
    protected function formatExportValues($list)
    {
        foreach ($list as $data) {
            if ($data) {
                echo mb_convert_encoding(implode(',', $data), 'sjis-win', 'UTF-8')."\n";
            }
        }
    }

    /**
     * アプリケーション一覧取得
     *
     * @param  array $params
     *
     * @return array $list
     *
     */
    public function getApplicationList()
    {
        $list = array();

        if (auth_is_user_sap()) {
            $object = $this->developerApplication->getListByDeveloperId(['developer_id' => auth_user_id()]);
            if (count($object->count()) > 0) {
                $ids = array();
                foreach ($object as $data) {
                    $ids[] = $data->app_id;
                }
                if (count($ids) > 0) {
                    $object = $this->application->getApplicationTitleList(['id' => $ids]);
                    if (count($object->count()) > 0) {
                        foreach ($object as $data) {
                            $list[$data->id] = $data->title;
                        }
                    }
                }
            }
        } else {
            $object = $this->application->getApplicationTitleList();
            if (count($object->count()) > 0) {
                foreach ($object as $data) {
                    $list[$data->id] = $data->title;
                }
            }
        }

        return $list;
    }

    /**
     * パーティション取得
     *
     * @param  timestamp $begin
     * @param  timestamp $end
     *
     * @return string    $patition
     *
     */
    public function getPartition($begin, $end)
    {
        $tergetDatetime = strtotime(date('Ym01000000', $begin));
        $endDatetime    = strtotime(date('Ym01000000', $end));

        $patition      = '';
        $patitionArray = array();

        while ($endDatetime >= $tergetDatetime) {
            $month = date('n', $tergetDatetime);
            // 12月は0に置き換え
            if ($month == 12) {
                $month = (string)0;
            }
            if (! in_array('p' . $month, $patitionArray)) {
                $patitionArray[] = 'p' . $month;
            }
            $tmpDatetime    = date('YmdHis', $tergetDatetime);
            $tergetDatetime = strtotime("$tmpDatetime + 1 month");

            if (count($patitionArray) > 0) {
                $patition = implode(',', $patitionArray);
            }
        }

        return $patition;
    }
}