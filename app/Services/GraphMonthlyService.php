<?php
namespace App\Services;

use App\Models\FreegameDeveloper\ReportUtilization;
use App\Models\FreegameDeveloper\ReportDmmMonthlyUtilization;
use App\Models\FreegameDeveloper\ReportGender;
use App\Models\FreegameDeveloper\ReportMonthlyGender;
use App\Models\FreegameDeveloper\ReportDmmMonthlyGender;
use App\Models\Freegame\Application;
use App\Models\FreegameDeveloper\DeveloperApplication;

/**
 * レポート：月別表示・グラフ
 */
class GraphMonthlyService extends CustomService
{
    protected $reportUtilization;

    protected $reportDmmMonthlyUtilization;

    protected $reportGender;

    protected $reportMonthlyGender;

    protected $reportDmmMonthlyGender;

    protected $application;

    protected $developerApplication;

    protected $appTitleType;

    public function __construct(
        ReportUtilization $reportUtilization,
        ReportDmmMonthlyUtilization $reportDmmMonthlyUtilization,
        ReportGender $reportGender,
        ReportMonthlyGender $reportMonthlyGender,
        ReportDmmMonthlyGender $reportDmmMonthlyGender,
        Application $application,
        DeveloperApplication $developerApplication
    ) {
        $this->reportUtilization = $reportUtilization;
        $this->reportDmmMonthlyUtilization = $reportDmmMonthlyUtilization;
        $this->reportGender = $reportGender;
        $this->reportMonthlyGender = $reportMonthlyGender;
        $this->reportDmmMonthlyGender = $reportDmmMonthlyGender;
        $this->application = $application;
        $this->developerApplication = $developerApplication;
    }

    public function getFormData()
    {
        return [
            'menuName' => config('forms.GraphMonthly.menuName'),
            'screenName' => config('forms.GraphMonthly.screenName'),
            'deviceType' => config('forms.GraphMonthly.deviceType'),
            'reportType' => config('forms.GraphMonthly.reportType'),
            'genderType' => config('forms.GraphMonthly.genderType'),
            'appTitleType' => $this->getAppTitleType()
        ];
    }

    public function getAppTitleType()
    {
        if (isset($this->appTitleType)) {
            return $this->appTitleType;
        }
        if (auth_is_sap()) {
            $devAppList = $this->developerApplication->getApplicationAppIdList([
                'developer_id' => auth_user_id()
            ]);
            if (empty($devAppList->count())) {
                return [];
            } else {
                foreach ($devAppList as $data) {
                    $condition['id'][] = $data->app_id;
                }
                $list = $this->application->getApplicationTitleList($condition);
            }
        } else {
            $list = $this->application->getApplicationTitleList();
        }
        $opts = [];
        if (auth_is_pf()) {
            $opts = config('forms.GraphMonthly.reportDmmType');
        }
        foreach ($list as $data) {
            $opts[$data->id] = $data->title;
        }
        $this->appTitleType = $opts;
        return $opts;
    }

    public function formatSearchQuery($search = [])
    {
        if (empty($search['begin'])) {
            $search['begin'] = '';
        } else {
            $search['begin'] = date('Y-m-01', strtotime($search['begin']));
        }
        if (empty($search['end'])) {
            $search['end'] = '';
        } else {
            $search['end'] = date('Y-m-t', strtotime($search['end']));
        }
        $appTitleType = $this->getAppTitleType();
        if (empty($search['app_id']) || ! isset($appTitleType[$search['app_id']])) {
            $search['app_id'] = '-1';
        }
        if (empty($search['device'])) {
            $search['device'] = 'pc';
        }
        $search = array_only($search, [
            'begin',
            'end',
            'app_id',
            'attr',
            'device',
            'type',
            'select'
        ]);
        return $search;
    }

    public function getList($condition = [])
    {
        if (empty($condition['report'])) {
            return [];
        }
        $method = camel_case('get_' . $condition['report']);
        if (method_exists($this, $method)) {
            return $this->{$method}($condition);
        }
        return [];
    }

    public function getActiveUser($condition = [])
    {
        $condition['select'] = [
            'date',
            'active_user'
        ];
        $addActiveUsers = [];
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilization->getMonthlyList($this->formatSearchQuery($condition));
            $subList = $this->reportUtilization->getList($this->formatSearchQuery($condition));
            foreach ($subList as $subData) {
                $addActiveUsers[$subData->date->format('Ym')] = $subData->active_user;
            }
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            $addKey = $data->date->format('Ym');
            $data->active_user = array_get($addActiveUsers, $addKey, $data->active_user);
            $data->total = $data->active_user;
        }
        return $list;
    }

    public function getRegistUser($condition = [])
    {
        $condition['select'] = [
            'date',
            'regist_user'
        ];
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilization->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            $data->total = $data->regist_user;
        }
        return $list;
    }

    public function getSuspendUser($condition = [])
    {
        $condition['select'] = [
            'date',
            'suspend_user'
        ];
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilization->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            $data->total = $data->suspend_user;
        }
        return $list;
    }

    public function getRPVReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'pv'
        ];
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilization->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            $data->total = $data->pv;
        }
        return $list;
    }

    public function getDAUReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'mau'
        ];
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilization->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        $subList = $this->getAUReport($condition);
        $addMaus = [];
        foreach ($subList as $subData) {
            $sum = 0;
            foreach (array_except($subData->toArray(), [
                'date'
            ]) as $val) {
                $sum += $val;
            }
            $addMaus[$subData->date->format('Ym')] = $sum;
        }
        $month = date('Y-m-01 00:00:00');
        foreach ($list as &$data) {
            $addKey = $data->date->format('Ym');
            if ($data->date >= $month) {
                $data->mau = 0;
            } else {
                $data->mau = array_get($addMaus, $addKey, 0);
            }
            $data->total = $data->mau;
        }
        return $list;
    }

    public function getAveragePVReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'pv',
            'mau'
        ];
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilization->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        $subList = $this->getAUReport($condition);
        $addMaus = [];
        foreach ($subList as $subData) {
            $sum = 0;
            foreach (array_except($subData->toArray(), [
                'date'
            ]) as $val) {
                $sum += $val;
            }
            $addMaus[$subData->date->format('Ym')] = $sum;
        }
        $month = date('Y-m-01 00:00:00');
        foreach ($list as &$data) {
            $addKey = $data->date->format('Ym');
            if ($data->date >= $month) {
                $data->mau = 0;
            } else {
                $data->mau = array_get($addMaus, $addKey, $data->mau);
            }
            if (empty($data->pv) || empty($data->mau)) {
                $data->total = 0;
            } else {
                $data->total = round($data->pv / $data->mau, 3);
            }
        }
        return $list;
    }

    public function getAccountingUUReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'use_point_user'
        ];
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilization->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        $addUsePointUsers = [];
        if ($condition['app_id'] != 'spt') {
            $subList = $this->getPUReport($condition);
            foreach ($subList as $subData) {
                $sum = 0;
                foreach (array_except($subData->toArray(), [
                    'date'
                ]) as $val) {
                    $sum += $val;
                }
                $addUsePointUsers[$subData->date->format('Ym')] = $sum;
            }
        }
        $month = date('Y-m-01 00:00:00');
        foreach ($list as &$data) {
            $addKey = $data->date->format('Ym');
            if ($data->date >= $month) {
                $data->use_point_user = 0;
            } else {
                $data->use_point_user = array_get($addUsePointUsers, $addKey, $data->use_point_user);
            }
            $data->total = $data->use_point_user;
        }
        return $list;
    }

    public function getChargingRateReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'mau',
            'use_point_user'
        ];
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilization->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        $addUsePointUsers = [];
        if ($condition['app_id'] != 'spt') {
            $subList = $this->getPUReport($condition);
            foreach ($subList as $subData) {
                $sum = 0;
                foreach (array_except($subData->toArray(), [
                    'date'
                ]) as $val) {
                    $sum += $val;
                }
                $addUsePointUsers[$subData->date->format('Ym')] = $sum;
            }
        }
        $subList = $this->getAUReport($condition);
        $addMaus = [];
        foreach ($subList as $subData) {
            $sum = 0;
            foreach (array_except($subData->toArray(), [
                'date'
            ]) as $val) {
                $sum += $val;
            }
            $addMaus[$subData->date->format('Ym')] = $sum;
        }
        $month = date('Y-m-01 00:00:00');
        foreach ($list as &$data) {
            $addKey = $data->date->format('Ym');
            if ($data->date >= $month) {
                $data->use_point_user = 0;
            } else {
                $data->use_point_user = array_get($addUsePointUsers, $addKey, $data->use_point_user);
            }
            $data->mau = array_get($addMaus, $addKey, $data->mau);
            if (empty($data->use_point_user) || empty($data->mau)) {
                $data->total = 0;
            } else {
                $data->total = round($data->use_point_user / $data->mau, 3);
            }
        }
        return $list;
    }

    public function getUsePointReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'use_point'
        ];
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilization->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            $data->total = $data->use_point;
        }
        return $list;
    }

    public function getARPUReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'active_user',
            'use_point'
        ];
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilization->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            if (empty($data->use_point) || empty($data->active_user)) {
                $data->total = 0;
            } else {
                $data->total = round($data->use_point / $data->active_user, 3);
            }
        }
        return $list;
    }

    public function getARPPUReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'use_point_user',
            'use_point'
        ];
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilization->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        $addUsePointUsers = [];
        if ($condition['app_id'] != 'spt') {
            $subList = $this->getPUReport($condition);
            foreach ($subList as $subData) {
                $sum = 0;
                foreach (array_except($subData->toArray(), [
                    'date'
                ]) as $val) {
                    $sum += $val;
                }
                $addUsePointUsers[$subData->date->format('Ym')] = $sum;
            }
        }
        $month = date('Y-m-01 00:00:00');
        foreach ($list as &$data) {
            $addKey = $data->date->format('Ym');
            if ($data->date >= $month) {
                $data->use_point_user = 0;
            } else {
                $data->use_point_user = array_get($addUsePointUsers, $addKey, $data->use_point_user);
            }
            if (empty($data->use_point) || empty($data->use_point_user)) {
                $data->total = 0;
            } else {
                $data->total = round($data->use_point / $data->use_point_user, 3);
            }
        }
        return $list;
    }

    public function getUserReport($condition = [])
    {
        $condition['select'] = [
            'date'
        ];
        foreach (config('forms.GraphMonthly.genderType') as $genderGroup) {
            $condition['select'] = array_merge($condition['select'], array_keys($genderGroup));
        }
        $condition['type'] = 'user';
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportGender->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyGender->getMonthlyList($this->formatSearchQuery($condition));
        }
        return $list;
    }

    public function getPVReport($condition = [])
    {
        $condition['select'] = [
            'date'
        ];
        foreach (config('forms.GraphMonthly.genderType') as $genderGroup) {
            $condition['select'] = array_merge($condition['select'], array_keys($genderGroup));
        }
        $condition['type'] = 'pv';
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportGender->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyGender->getMonthlyList($this->formatSearchQuery($condition));
        }
        return $list;
    }

    public function getAUReport($condition = [])
    {
        $condition['select'] = [
            'date'
        ];
        foreach (config('forms.GraphMonthly.genderType') as $genderGroup) {
            $condition['select'] = array_merge($condition['select'], array_keys($genderGroup));
        }
        $condition['type'] = 'mau';
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportMonthlyGender->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyGender->getMonthlyList($this->formatSearchQuery($condition));
        }
        return $list;
    }

    public function getPUReport($condition = [])
    {
        $condition['select'] = [
            'date'
        ];
        foreach (config('forms.GraphMonthly.genderType') as $genderGroup) {
            $condition['select'] = array_merge($condition['select'], array_keys($genderGroup));
        }
        $condition['type'] = 'use_point_user';
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportMonthlyGender->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyGender->getMonthlyList($this->formatSearchQuery($condition));
        }
        return $list;
    }

    public function getPointReport($condition = [])
    {
        $condition['select'] = [
            'date'
        ];
        foreach (config('forms.GraphMonthly.genderType') as $genderGroup) {
            $condition['select'] = array_merge($condition['select'], array_keys($genderGroup));
        }
        $condition['type'] = 'use_point';
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportGender->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyGender->getMonthlyList($this->formatSearchQuery($condition));
        }
        return $list;
    }

    public function getARPMAUReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'mau',
            'use_point'
        ];
        if (is_numeric($condition['app_id'])) {
            $list = $this->reportUtilization->getMonthlyList($this->formatSearchQuery($condition));
        } else {
            $condition['attr'] = $condition['app_id'];
            $list = $this->reportDmmMonthlyUtilization->getMonthlyList($this->formatSearchQuery($condition));
        }
        foreach ($list as &$data) {
            if (empty($data->use_point) || empty($data->mau)) {
                $data->total = 0;
            } else {
                $data->total = round($data->use_point / $data->mau, 3);
            }
        }
        return $list;
    }
}
