<?php

namespace App\Services;

use App\Models\FreegameGuide\GuideApplication;
use App\Models\FreegameDeveloper\DeveloperGuideApplication;
use App\Models\FreegameGuide\GuideNotification;
use App\Models\FreegameGuide\GuideNotificationCategory;
use \Exception;

class GuideNotificationCategoryService extends CustomService
{
    protected $guideApp;
    protected $devGuideApp;
    protected $guideNotic;
    protected $guideNoticCategory;

    public function __construct(
        GuideApplication $guideApp,
        DeveloperGuideApplication $devGuideApp,
        GuideNotification $guideNotic,
        GuideNotificationCategory $guideNoticCategory
    ) {
        $this->guideApp = $guideApp;
        $this->devGuideApp = $devGuideApp;
        $this->guideNotic = $guideNotic;
        $this->guideNoticCategory = $guideNoticCategory;
    }

    /**
     * Get guide_faq_category List
     * @param  integer $guideAppId
     * @return array
     */
    public function getListByGuideAppId($guideAppId)
    {
        $getList = $this->guideNoticCategory->getListByGuideAppId($guideAppId);

        return $getList->toArray();
    }

    /**
     * Get guide_faq_category by id
     * @param  integer $id
     * @param  integer $guideAppId
     * @return array
     */
    public function getOneById($id, $guideAppId)
    {
        $getList = $this->guideNoticCategory->getOneById($id, $guideAppId);
        return empty($getList) ? $getList : $getList->toArray();
    }

    /**
     * Get guide_application by id
     * @param  integer $id
     * @return array
     */
    public function getOneGuideApplication($id)
    {
        // ---------
        // DB
        $guideApplication = $this->guideApp->getOne($id);
        // ---------
        return empty($guideApplication) ? $guideApplication : $guideApplication->toArray();
    }

    /**
     * Insert guide_faq_category to database
     * @param  array $request
     * @return boolean
     */
    public function create($request)
    {
        // Request で値はチェック済み
        $newGuideNoticCategory = [
            'guide_application_id' => $request['guideAppId'],
            'name'                 => $request['name'],
            'background_color'     => $request['background_color'],
            'color'                => $request['color'],
            'stamp'                => timestamp_to_sqldate(now_stamp()),
        ];
        // ---------
        // DB
        $result = $this->guideNoticCategory->insert($newGuideNoticCategory);
        // ---------

        return $result;
    }

    /**
     * Update guide_faq_category to database
     * @param  array $request
     * @return boolean
     */
    public function edit($request)
    {
        // ---------
        // DB
        $oldGuideNoticCategory = $this->getOneById($request['id'], $request['guideAppId']);
        // ---------
        if (empty($oldGuideNoticCategory)) {
            return false;
        }

        // Request で値はチェック済み
        $editGuideFaqCategory = [
            'guide_application_id' => $request['guideAppId'],
            'name'                 => $request['name'],
            'background_color'     => $request['background_color'],
            'color'                => $request['color'],
            'stamp'                => timestamp_to_sqldate(now_stamp()),
        ];
        // ---------
        // DB
        $result = $this->guideNoticCategory->edit($editGuideFaqCategory, $request['id']);
        // ---------

        return $result;
    }

    /**
     * Delete content
     * @param  int $id
     * @param  integer $guideAppId
     * @return boolean
     */
    public function deleteContent($id, $guideAppId)
    {
        // ---------
        // DB
        $oldGuideNoticCategory = $this->getOneById($id, $guideAppId);
        // ---------
        if (empty($oldGuideNoticCategory)) {
            return false;
        }

        // ---------
        // DB
        $countNotic = $this->guideNotic->getCountByGuideAppIdAndCategoryId($guideAppId, $id);

        // カテゴリにお知らせが紐付いていないものだけを削除する
        if ($countNotic === 0) {
            $this->guideNoticCategory->del($id);
        }
        // ---------
        return true;
    }

    /**
     * Check Edit Permission
     * @return boolean
     */
    public function isEnableEdit($guideAppId)
    {
        $userId = auth_user_id();
        if (empty($userId)) {
            return false;
        }
        if (auth_is_pf()) {
            return true;
        } else {
            $idList = $this->devGuideApp->getListGuideAppIdByDevId($userId);
            if (in_array($guideAppId, $idList->toArray())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get values
     *
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.GuideNotificationCategory');

        return [
            'screenName'  => $privateConfigs['screenName'],
            'menuName'  => $privateConfigs['menuName'],
        ];
    }
}
