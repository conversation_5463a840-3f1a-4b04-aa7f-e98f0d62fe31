<?php
namespace App\Services;

use App\Models\FreegameGuide\GuideApplication;
use App\Models\FreegameDeveloper\DeveloperGuideApplication;
use App\Models\FreegameGuide\GuideFaq;
use App\Models\FreegameGuide\GuideFaqCategory;
use App\Models\FreegameGuide\GuideFaqCategoryGroup;
use \Exception;

class GuideFaqCategoryService extends CustomService
{
    protected $guideApp;
    protected $devGuideApp;
    protected $guideFaq;
    protected $guideFaqCategory;
    protected $guideFaqCategoryGroup;

    public function __construct(
        GuideApplication $guideApp,
        DeveloperGuideApplication $devGuideApp,
        GuideFaq $guideFaq,
        GuideFaqCategory $guideFaqCategory,
        GuideFaqCategoryGroup $guideFaqCategoryGroup
    ) {
        $this->guideApp = $guideApp;
        $this->devGuideApp = $devGuideApp;
        $this->guideFaq = $guideFaq;
        $this->guideFaqCategory = $guideFaqCategory;
        $this->guideFaqCategoryGroup = $guideFaqCategoryGroup;
    }

    /**
     * Get guide_faq_category List
     * @param  integer $guideAppId
     * @return array
     */
    public function getListByGuideAppId($guideAppId)
    {
        $getList = $this->guideFaqCategory->getListByGuideAppId($guideAppId);

        return $getList->toArray();
    }

    /**
     * Get guide_faq_category List
     * @param  array $condition
     * @return array
     */
    public function getFaqCategoryList($condition)
    {
        return $this->guideFaqCategory->getList($condition)->toArray();
    }

    /**
     * Get guide_faq_category by id
     * @param  integer $id
     * @param  integer $guideAppId
     * @return array
     */
    public function getOneById($id, $guideAppId)
    {
        $getList = $this->guideFaqCategory->getOneById($id, $guideAppId);
        return empty($getList) ? $getList : $getList->toArray();
    }

    /**
     * Get guide_application by id
     * @param  integer $id
     * @return array
     */
    public function getOneGuideApplication($id)
    {
        // ---------
        // DB
        $guideApplication = $this->guideApp->getOne($id);
        // ---------
        return empty($guideApplication) ? $guideApplication : $guideApplication->toArray();
    }

    /**
     * Insert guide_faq_category to database
     * @param  array $request
     * @return boolean
     */
    public function create($request)
    {
        // Request で値はチェック済み
        $newGuideFaqCategory = [
            'guide_application_id' => $request['guideAppId'],
            'name'                 => $request['name'],
            'stamp'                => timestamp_to_sqldate(now_stamp()),
        ];
        if (isset($request['guide_faq_category_group_id'])) {
            $newGuideFaqCategory['guide_faq_category_group_id'] = $request['guide_faq_category_group_id'];
        }

        // ---------
        // DB
        $result = $this->guideFaqCategory->insert($newGuideFaqCategory);
        // ---------

        return $result;
    }

    /**
     * Update guide_faq_category to database
     * @param  array $request
     * @return boolean
     */
    public function edit($request)
    {
        // ---------
        // DB
        $oldGuideFaqCategory = $this->getOneById($request['id'], $request['guideAppId']);
        // ---------
        if (empty($oldGuideFaqCategory)) {
            return false;
        }

        // Request で値はチェック済み
        $editGuideFaqCategory = [
            'guide_application_id' => $request['guideAppId'],
            'name'                 => $request['name'],
            'stamp'                => timestamp_to_sqldate(now_stamp()),
        ];
        if (isset($request['guide_faq_category_group_id'])) {
            $editGuideFaqCategory['guide_faq_category_group_id'] = $request['guide_faq_category_group_id'];
        }

        // ---------
        // DB
        $result = $this->guideFaqCategory->edit($editGuideFaqCategory, $request['id']);
        // ---------

        return $result;
    }

    /**
     * Delete content
     * @param  int $id
     * @param  integer $guideAppId
     * @return boolean
     */
    public function deleteContent($id, $guideAppId)
    {
        // ---------
        // DB
        $oldGuideFaqCategory = $this->getOneById($id, $guideAppId);
        // ---------
        if (empty($oldGuideFaqCategory)) {
            return false;
        }

        // ---------
        // DB
        $countFaq = $this->guideFaq->getCountByGuideAppIdAndCategoryId($guideAppId, $id);

        // カテゴリにFAQが紐付いていないものだけを削除する
        if ($countFaq === 0) {
            $this->guideFaqCategory->del($id);
        }
        // ---------
        return true;
    }

    /**
     * Update priority of guide_faq_category to database
     * @param  array $request
     * @return boolean
     */
    public function priorityEdit($request)
    {
        $result = true;
        if (empty($request['faqcategory'])) {
            return false;
        }

        foreach ($request['faqcategory'] as $intKey => $faqCategory) {
            // ---------
            // DB
            $oldGuideFaqCategory = $this->getOneById($faqCategory['id'], $request['guideAppId']);
            // ---------
            if (empty($oldGuideFaqCategory)) {
                continue;
            }

            $priority = $intKey + 1;
            $editGuideFaqCategory = [
                'priority' => $priority,
                'stamp'    => timestamp_to_sqldate(now_stamp()),
            ];
            // ---------
            // DB
            $result = $this->guideFaqCategory->edit($editGuideFaqCategory, $faqCategory['id']);
            // ---------
        }

        return $result;
    }

    /**
     * Check Edit Permission
     * @return boolean
     */
    public function isEnableEdit($guideAppId)
    {
        $userId = auth_user_id();
        if (empty($userId)) {
            return false;
        }
        if (auth_is_pf()) {
            return true;
        } else {
            $idList = $this->devGuideApp->getListGuideAppIdByDevId($userId);
            if (in_array($guideAppId, $idList->toArray())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get values
     *
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.GuideFaqCategory');

        return [
            'screenName'  => $privateConfigs['screenName'],
            'menuName'  => $privateConfigs['menuName'],
        ];
    }

    /**
     * Get guide_faq_category_group List
     * @param  integer $guideAppId
     * @return array   $list
     */
    public function getGuideFaqCategoryGroupList($guideAppId)
    {
        $list = [];

        $getList = $this->guideFaqCategoryGroup->getList([
            'guide_application_id' => $guideAppId
        ]);

        foreach ($getList as $key => $val) {
            $list[$val->id] = $val->name;
        }

        return $list;
    }
}
