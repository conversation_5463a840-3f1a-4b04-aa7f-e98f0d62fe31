<?php

namespace App\Services;

use App\Models\FreegameGuide\GuideApplication;
use App\Models\FreegameDeveloper\DeveloperGuideApplication;
use App\Models\FreegameGuide\GuideForm;
use App\Models\FreegameGuide\GuideFormOptions;
use App\Models\FreegameGuide\GuidePrivacyPolicy;
use \Exception;
use App\Models\Freegame\Freegame;

class GuideFormService extends CustomService
{
    protected $guideApp;
    protected $devGuideApp;
    protected $guideForm;
    protected $guideFormOptions;
    protected $guidePrivacyPolicy;

    public function __construct(
        GuideApplication $guideApp,
        DeveloperGuideApplication $devGuideApp,
        GuideForm $guideForm,
        GuideFormOptions $guideFormOptions,
        GuidePrivacyPolicy $guidePrivacyPolicy
    ) {
        $this->guideApp = $guideApp;
        $this->devGuideApp = $devGuideApp;
        $this->guideForm = $guideForm;
        $this->guideFormOptions = $guideFormOptions;
        $this->guidePrivacyPolicy = $guidePrivacyPolicy;
    }

    /**
     * Get GuideFormList
     * @param  integer $guideAppId
     * @return array
     */
    public function getDetails($guideAppId)
    {
         // ---------
        // DB
        $guideFormList = $this->guideForm->getListByGuideAppId($guideAppId);

        if (empty($guideFormList) === false) {
            foreach ($guideFormList as &$detail) {
                if (in_array($detail['type'], ['text', 'textarea'])) {
                    $detail['options'] = [
                        0 => [
                            'id'            => 0,
                            'guide_form_id' => $detail['id'],
                            'name'          => $detail['value'],
                            'value'         => $detail['value'],
                        ]
                    ];
                } else {
                    $optionList =
                        $this->guideFormOptions->getListByGuideFormId($detail['id']);
                    $detail['options'] = $optionList->toArray();
                }
            }
            unset($detail);
        }

        // ---------

        // ---------
        // result
        return $guideFormList;
    }

    /**
     * Get PrivacyPolicy
     * @param  integer $guideAppId
     * @return GuidePrivacyPolicy
     */
    public function getPrivacyPolicy($guideAppId)
    {
        return $this->guidePrivacyPolicy->getOneByGuideAppId($guideAppId);
    }

    /**
     * Get guide_application by id
     * @param  integer $id
     * @return array
     */
    public function getOneGuideApplication($id)
    {
        // ---------
        // DB
        $guideApplication = $this->guideApp->getOne($id);
        // ---------
        return $guideApplication;
    }

    /**
     * Edit GuideForm to database
     * @param array $request
     * @return boolean
     */
    public function edit($request)
    {
        $guideAppId = $request['guideAppid'];
        $updateList = $request['data'];
        foreach ($updateList as &$update) {
            if (in_array($update['type'], ['text', 'textarea'])) {
                $update['value'] = $update['options'][0]['value'];
                unset($update['options'][0]['value']);
            }
        }
        unset($update);

        // Validate に使う値を優先する
        $isSubjectFormId = $request['is_subject'];
        $updateList[$isSubjectFormId]['is_subject'] = 1;

        // ---------
        // DB
        Freegame::beginTransaction();
        try {
            // ---------
            // 既存分を確認して、登録済みだが更新のリストにないものを削除
            $guideFormList = $this->guideForm->getListByGuideAppId($guideAppId);
            foreach ($guideFormList as $form) {
                $formId = $form['id'];
                $answerFormat = null;
                if (empty($updateList[$formId])) {
                    $this->guideForm->del($formId);
                }
                $optionList = $this->guideFormOptions
                    ->getListByGuideFormId($formId);
                foreach ($optionList as $option) {
                    $optionId = $option['id'];
                    if (empty($updateList[$formId]['options'][$optionId])) {
                        $this->guideFormOptions->del($optionId);
                    }
                }
            }

            $where = [
                'guide_application_id' => $guideAppId,
            ];
            $params = [
                'policy' => $request['policy'],
            ];
            // プライバシーポリシー更新
            $this->guidePrivacyPolicy->updateOrCreate($where, $params);

            // ---------
            // 削除後の更新
            $this->editFormDetail($updateList, $guideAppId);

            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            throw $e;
        }
        // ---------
    }

    /**
     * Create GuideForm to database
     * @param array $updateList
     * @param integer $guideAppId
     * @return boolean
     */
    private function editFormDetail($updateList, $guideAppId)
    {
        foreach ($updateList as $intKey => $detail) {
            $formId = $intKey;
            $isEnptyDetail = $this->guideForm->isEmpty($formId, $guideAppId);
            $detail['value'] = (isset($detail['value'])) ? $detail['value'] : '';
            $params = array(
                'guide_application_id' => $guideAppId,
                'priority'     => $detail['priority'],
                'name'         => $detail['name'],
                'explanation'  => $detail['explanation'],
                'value'        => $detail['value'],
                'type'         => $detail['type'],
                'max_length'   => $detail['max_length'],
                'is_required'  => isset($detail['is_required']) ? 1 : 0,
                'is_from'      => $detail['is_from'],
                'is_subject'   => isset($detail['is_subject']) ? 1 : 0,
            );
            if ($isEnptyDetail) {
                // 追加分はinsert
                $formId = $this->guideForm->insertGetId($params);
            } else {
                $this->guideForm->edit($params, $formId);
            }

            // ---------
            // アンケート選択肢情報登録
            if (!in_array($detail['type'], ['text', 'textarea'])) {
                $this->editFormOptions($guideAppId, $formId, $detail);
            }
        }
    }

    /**
     * Create GuideFormOptions to database
     * @param integer $guideAppId
     * @param integer $formId
     * @param array $detail
     * @return boolean
     */
    private function editFormOptions($guideAppId, $formId, $detail)
    {
        if (empty($detail['options'])) {
            $errMsg = 'Not Found options.'
                . ' ::method=' . __METHOD__
                . ' ::guide_application_id=' . var_export($guideAppId, true)
                . ' ::guide_form_id=' . var_export($formId, true)
                . ' ::type=' . var_export($detail['type'], true);
            throw new Exception($errMsg);
        }
        foreach ($detail['options'] as $optionId => $option) {
            $isEnptyOption = $this->guideFormOptions->isEmpty($optionId, $formId);
            if ($isEnptyOption) {
                // 追加分はinsert
                $insertGuideFormOptions = array(
                    'guide_form_id' => $formId,
                    'priority'      => $option['priority'],
                    'value'         => $option['value'],
                    'name'          => $option['value'],
                );
                $this->guideFormOptions->insert($insertGuideFormOptions);
            } else {
                $updateGuideFormOptions = array(
                    'guide_form_id' => $formId,
                    'priority'      => $option['priority'],
                    'value'         => $option['value'],
                    'name'          => $option['value'],
                );
                $this->guideFormOptions->edit($updateGuideFormOptions, $optionId);
            }
        }
    }

    /**
     * Check Edit Permission
     * @return boolean
     */
    public function isEnableEdit($guideAppId)
    {
        $userId = auth_user_id();
        if (empty($userId)) {
            return false;
        }
        if (auth_is_pf()) {
            return true;
        } else {
            $idList = $this->devGuideApp->getListGuideAppIdByDevId($userId);
            if (in_array($guideAppId, $idList->toArray())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get values
     *
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.GuideForm');

        return [
            'screenName'  => $privateConfigs['screenName'],
            'menuName'  => $privateConfigs['menuName'],
            'typeList'  => $privateConfigs['typeList'],
        ];
    }
}
