<?php

namespace App\Services;

use App\Models\FreegameGuide\GuideApplication;
use App\Models\FreegameDeveloper\DeveloperGuideApplication;
use App\Models\FreegameGuide\GuideMaintenancePage;
use App\Models\FreegameGuide\GuidePage;
use \Exception;

class GuideMaintenanceService extends CustomService
{
    protected $guideApp;
    protected $devGuideApp;
    protected $guideMaintePage;
    protected $guidePage;

    public function __construct(
        GuideApplication $guideApp,
        DeveloperGuideApplication $devGuideApp,
        GuideMaintenancePage $guideMaintePage,
        GuidePage $guidePage
    ) {
        $this->guideApp = $guideApp;
        $this->devGuideApp = $devGuideApp;
        $this->guideMaintePage = $guideMaintePage;
        $this->guidePage = $guidePage;
    }

    /**
     * Get Mentenance List
     * @param  integer $guideAppId
     * @return array
     */
    public function getListMaintenance($guideAppId)
    {
        $getList = $this->guideMaintePage->getListByGuideAppId($guideAppId);

        return $getList->toArray();
    }

    /**
     * Get Mentenance by id
     * @param  integer $id
     * @param  integer $guideAppId
     * @return array
     */
    public function getOneMaintenance($id, $guideAppId)
    {
        $getList = $this->guideMaintePage->getOneByid($id, $guideAppId);
        return empty($getList) ? $getList : $getList->toArray();
    }

    /**
     * Get Page List
     * @return array
     */
    public function getListGuidePageName()
    {
        $result = array();
        $getList = $this->guidePage->getList();
        foreach ($getList as $guidePage) {
            $index = $guidePage['id'];
            $result[$index] = $guidePage['page_name'];
        }
        return $result;
    }

    /**
     * Get guide_application by id
     * @param  integer $id
     * @return array
     */
    public function getOneGuideApplication($id)
    {
        // ---------
        // DB
        $guideApplication = $this->guideApp->getOne($id);
        // ---------
        return empty($guideApplication) ? $guideApplication : $guideApplication->toArray();
    }

    /**
     * Insert guide_mentenance_page to database
     * @param  array $request
     * @return boolean
     */
    public function create($request)
    {
        // Request で値はチェック済み
        $newGuideMainte = [
            'guide_application_id' => $request['guideAppId'],
            'guide_page_id'        => $request['guide_page_id'],
            'is_internal_release'  => $request['is_internal_release'],
            'start_datetime'       => $request['start_datetime'],
            'end_datetime'         => $request['end_datetime'],
            'stamp'                => timestamp_to_sqldate(now_stamp()),
        ];
        // ---------
        // DB
        $result = $this->guideMaintePage->insert($newGuideMainte);
        // ---------

        return $result;
    }

    /**
     * Update guide_mentenance_page to database
     * @param  array $request
     * @return boolean
     */
    public function edit($request)
    {
        // ---------
        // DB
        $oldGuideMainte = $this->getOneMaintenance($request['id'], $request['guideAppId']);
        // ---------
        if (empty($oldGuideMainte)) {
            return false;
        }

        // Request で値はチェック済み
        $editGuideMainte = [
            'guide_page_id'       => $request['guide_page_id'],
            'is_internal_release' => $request['is_internal_release'],
            'start_datetime'      => $request['start_datetime'],
            'end_datetime'        => $request['end_datetime'],
            'stamp'               => timestamp_to_sqldate(now_stamp()),
        ];
        // ---------
        // DB
        $result = $this->guideMaintePage->edit($editGuideMainte, $request['id']);
        // ---------

        return $result;
    }

    /**
     * Delete content
     * @param  integer $id
     * @param  integer $guideAppId
     * @return boolean
     */
    public function deleteContent($id, $guideAppId)
    {
        // ---------
        // DB
        $oldGuideMainte = $this->getOneMaintenance($id, $guideAppId);
        // ---------
        if (empty($oldGuideMainte)) {
            return false;
        }

        // ---------
        // DB
        $this->guideMaintePage->del($id);
        // ---------
        return true;
    }

    /**
     * Check Edit Permission
     * @return boolean
     */
    public function isEnableEdit($guideAppId)
    {
        $userId = auth_user_id();
        if (empty($userId)) {
            return false;
        }
        if (auth_is_pf()) {
            return true;
        } else {
            $idList = $this->devGuideApp->getListGuideAppIdByDevId($userId);
            if (in_array($guideAppId, $idList->toArray())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get values
     *
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.GuideMaintenance');

        return [
            'screenName'  => $privateConfigs['screenName'],
            'menuName'  => $privateConfigs['menuName'],
            'siteOpenType' => $privateConfigs['siteOpenType'],
        ];
    }
}
