<?php
namespace App\Services;

use App\Models\FreegameDeveloper\ReportUtilization;
use App\Models\FreegameDeveloper\ReportGender;
use App\Models\Freegame\Application;
use App\Models\FreegameDeveloper\DeveloperApplication;

/**
 * レポート：日別表示・グラフ
 */
class GraphDailyService extends CustomService
{

    protected $reportUtilization;

    protected $reportGender;

    protected $application;

    protected $developerApplication;

    protected $appTitleType;

    public function __construct(
        ReportUtilization $reportUtilization,
        ReportGender $reportGender,
        Application $application,
        DeveloperApplication $developerApplication
    ) {
        $this->reportUtilization = $reportUtilization;
        $this->reportGender = $reportGender;
        $this->application = $application;
        $this->developerApplication = $developerApplication;
    }

    public function getFormData()
    {
        return [
            'menuName' => config('forms.GraphDaily.menuName'),
            'screenName' => config('forms.GraphDaily.screenName'),
            'deviceType' => config('forms.GraphDaily.deviceType'),
            'reportType' => config('forms.GraphDaily.reportType'),
            'genderType' => config('forms.GraphDaily.genderType'),
            'appTitleType' => $this->getAppTitleType()
        ];
    }

    public function getAppTitleType()
    {
        if (isset($this->appTitleType)) {
            return $this->appTitleType;
        }
        if (auth_is_sap()) {
            $devAppList = $this->developerApplication->getApplicationAppIdList([
                'developer_id' => auth_user_id()
            ]);
            if (empty($devAppList->count())) {
                return [];
            } else {
                foreach ($devAppList as $data) {
                    $condition['id'][] = $data->app_id;
                }
                $list = $this->application->getApplicationTitleList($condition);
            }
        } else {
            $list = $this->application->getApplicationTitleList();
        }
        $opts = [];
        foreach ($list as $data) {
            $opts[$data->id] = $data->title;
        }
        $this->appTitleType = $opts;
        return $opts;
    }

    public function formatSearchQuery($search = [])
    {
        if (empty($search['begin'])) {
            $search['begin'] = '';
        } else {
            $search['begin'] = date('Y-m-d', strtotime($search['begin']));
        }
        if (empty($search['end'])) {
            $search['end'] = '';
        } else {
            $search['end'] = date('Y-m-d', strtotime($search['end']));
        }
        $appTitleType = $this->getAppTitleType();
        if (empty($search['app_id']) || ! isset($appTitleType[$search['app_id']])) {
            $search['app_id'] = '-1';
        }
        if (empty($search['device'])) {
            $search['device'] = 'pc';
        }
        $search = array_only($search, [
            'begin',
            'end',
            'app_id',
            'device',
            'type',
            'select'
        ]);
        return $search;
    }

    public function getList($condition = [])
    {
        if (empty($condition['report'])) {
            return [];
        }
        $method = camel_case('get_' . $condition['report']);
        if (method_exists($this, $method)) {
            return $this->{$method}($condition);
        }
        return [];
    }

    public function getActiveUser($condition = [])
    {
        $condition['select'] = [
            'date',
            'active_user'
        ];
        $list = $this->reportUtilization->getList($this->formatSearchQuery($condition));
        foreach ($list as &$data) {
            $data->total = $data->active_user;
        }
        return $list;
    }

    public function getRegistUser($condition = [])
    {
        $condition['select'] = [
            'date',
            'regist_user'
        ];
        $list = $this->reportUtilization->getList($this->formatSearchQuery($condition));
        foreach ($list as &$data) {
            $data->total = $data->regist_user;
        }
        return $list;
    }

    public function getSuspendUser($condition = [])
    {
        $condition['select'] = [
            'date',
            'suspend_user'
        ];
        $list = $this->reportUtilization->getList($this->formatSearchQuery($condition));
        foreach ($list as &$data) {
            $data->total = $data->suspend_user;
        }
        return $list;
    }

    public function getRPVReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'pv'
        ];
        $list = $this->reportUtilization->getList($this->formatSearchQuery($condition));
        foreach ($list as &$data) {
            $data->total = $data->pv;
        }
        return $list;
    }

    public function getDAUReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'dau'
        ];
        $list = $this->reportUtilization->getList($this->formatSearchQuery($condition));
        foreach ($list as &$data) {
            $data->total = $data->dau;
        }
        return $list;
    }

    public function getAveragePVReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'pv',
            'dau'
        ];
        $list = $this->reportUtilization->getList($this->formatSearchQuery($condition));
        foreach ($list as &$data) {
            if (empty($data->pv) || empty($data->dau)) {
                $data->total = 0;
            } else {
                $data->total = round($data->pv / $data->dau, 3);
            }
        }
        return $list;
    }

    public function getAccountingUUReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'use_point_user'
        ];
        $list = $this->reportUtilization->getList($this->formatSearchQuery($condition));
        foreach ($list as &$data) {
            $data->total = $data->use_point_user;
        }
        return $list;
    }

    public function getChargingRateReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'dau',
            'use_point_user'
        ];
        $list = $this->reportUtilization->getList($this->formatSearchQuery($condition));
        foreach ($list as &$data) {
            if (empty($data->use_point_user) || empty($data->dau)) {
                $data->total = 0;
            } else {
                $data->total = round($data->use_point_user / $data->dau, 3);
            }
        }
        return $list;
    }

    public function getUsePointReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'use_point'
        ];
        $list = $this->reportUtilization->getList($this->formatSearchQuery($condition));
        foreach ($list as &$data) {
            $data->total = $data->use_point;
        }
        return $list;
    }

    public function getARPUReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'active_user',
            'use_point'
        ];
        $list = $this->reportUtilization->getList($this->formatSearchQuery($condition));
        foreach ($list as &$data) {
            if (empty($data->use_point) || empty($data->active_user)) {
                $data->total = 0;
            } else {
                $data->total = round($data->use_point / $data->active_user, 3);
            }
        }
        return $list;
    }

    public function getARPPUReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'use_point_user',
            'use_point'
        ];
        $list = $this->reportUtilization->getList($this->formatSearchQuery($condition));
        foreach ($list as &$data) {
            if (empty($data->use_point) || empty($data->use_point_user)) {
                $data->total = 0;
            } else {
                $data->total = round($data->use_point / $data->use_point_user, 3);
            }
        }
        return $list;
    }

    public function getUserReport($condition = [])
    {
        $condition['select'] = [
            'date'
        ];
        foreach (config('forms.GraphDaily.genderType') as $genderGroup) {
            $condition['select'] = array_merge($condition['select'], array_keys($genderGroup));
        }
        $condition['type'] = 'user';
        return $this->reportGender->getList($this->formatSearchQuery($condition));
    }

    public function getPVReport($condition = [])
    {
        $condition['select'] = [
            'date'
        ];
        foreach (config('forms.GraphDaily.genderType') as $genderGroup) {
            $condition['select'] = array_merge($condition['select'], array_keys($genderGroup));
        }
        $condition['type'] = 'pv';
        return $this->reportGender->getList($this->formatSearchQuery($condition));
    }

    public function getAUReport($condition = [])
    {
        $condition['select'] = [
            'date'
        ];
        foreach (config('forms.GraphDaily.genderType') as $genderGroup) {
            $condition['select'] = array_merge($condition['select'], array_keys($genderGroup));
        }
        $condition['type'] = 'dau';
        return $this->reportGender->getList($this->formatSearchQuery($condition));
    }

    public function getPUReport($condition = [])
    {
        $condition['select'] = [
            'date'
        ];
        foreach (config('forms.GraphDaily.genderType') as $genderGroup) {
            $condition['select'] = array_merge($condition['select'], array_keys($genderGroup));
        }
        $condition['type'] = 'use_point_user';
        return $this->reportGender->getList($this->formatSearchQuery($condition));
    }

    public function getPointReport($condition = [])
    {
        $condition['select'] = [
            'date'
        ];
        foreach (config('forms.GraphDaily.genderType') as $genderGroup) {
            $condition['select'] = array_merge($condition['select'], array_keys($genderGroup));
        }
        $condition['type'] = 'use_point';
        return $this->reportGender->getList($this->formatSearchQuery($condition));
    }

    public function getARPDAUReport($condition = [])
    {
        $condition['select'] = [
            'date',
            'dau',
            'use_point'
        ];
        $list = $this->reportUtilization->getList($this->formatSearchQuery($condition));
        foreach ($list as &$data) {
            if (empty($data->use_point) || empty($data->dau)) {
                $data->total = 0;
            } else {
                $data->total = round($data->use_point / $data->dau, 3);
            }
        }
        return $list;
    }
}
