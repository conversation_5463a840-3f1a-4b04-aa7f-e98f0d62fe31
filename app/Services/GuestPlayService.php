<?php

namespace App\Services;

use App\Models\Freegame\Guest;
use GuzzleHttp\Client as HttpClient;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Cookie\CookieJar;

/**
 * ゲストプレイユーザーサービス
 */
class GuestPlayService extends CustomService
{
    // アクセストークン取得API
    const ACCESS_TOKEN_API_URL_PRODUCTION = 'https://api-mission.games.dmm.com/client_credentials/v1/';
    const ACCESS_TOKEN_API_URL_STAGING = 'https://stg.api-mission.games.dmm.com/client_credentials/v1/';
    const ACCESS_TOKEN_API_URL_DEVELOP = 'https://77j0dk78i1.execute-api.ap-northeast-1.amazonaws.com/v1/{proxy+}';

    // ゲストプレイユーザー作成API
    const GUEST_API_URL = 'https://personal.games.dmm.com/user/v1/guest/create';

    // ゲストプレイcookieのデフォルト設定
    const defaultCookieSettings = [
        'key' => 'games_guestplay',
        'expireMinutes' => 259200,
        'path' => '/',
        'domain' => '.dmm.com',
        'secure' => false,
        'httpOnly' => true
    ];

    /** @var HttpClient */
    protected $httpClient;

    /** @var Guest */
    protected $guest;

    /** @var CookieJar */
    private $cookie;

    public function __construct(HttpClient $httpClient, Guest $guest, CookieJar $cookie)
    {
        parent::__construct();

        $this->httpClient = $httpClient;
        $this->guest = $guest;
        $this->cookie = $cookie;
    }

    public function getFormData()
    {
        return [
            'formData' => config('forms.GuestPlay')
        ];
    }

    /**
     * アクセストークンを取得し、ゲストユーザーを作成する。
     */
    public function createGuestPlayUser()
    {
        try {
            // アクセストークンを取得する
            $accessToken = $this->getAccessToken();

            // ゲストプレイユーザー作成APIを叩いて、cookieに入れるhashed_idをもらう。
            $hashedId = $this->createGuestPlayUserAndGetHashedId($accessToken);
        } catch (GuzzleException $e) {
            abort(500, sprintf('ゲストユーザーを作成できませんでした：%s', $e->getMessage()));
        }

        // config('session.secure')がtrueになってるから、ゲストプレイクッキーをsecure = falseで発行する為一時的に変える
        $this->cookie->setDefaultPathAndDomain(config('session.path'), config('session.domain'), false);

        // cookieにenqueueする
        $this->cookie->queue(
            self::defaultCookieSettings['key'],
            json_encode(['hashed_id' => $hashedId]),
            self::defaultCookieSettings['expireMinutes'],
            self::defaultCookieSettings['path'],
            self::defaultCookieSettings['domain'],
            self::defaultCookieSettings['secure'],
            self::defaultCookieSettings['httpOnly']
        );
    }

    /**
     * @return string
     */
    private function getEndpoint()
    {
        $endpoint = self::ACCESS_TOKEN_API_URL_DEVELOP;
        if (env('APP_ENV') == 'production') {
            $endpoint = self::ACCESS_TOKEN_API_URL_PRODUCTION;
        } else if (env('APP_ENV') == 'staging') {
            $endpoint = self::ACCESS_TOKEN_API_URL_STAGING;
        }
        return $endpoint;
    }

    /**
     * @return string|null
     * @throws GuzzleException
     */
    private function getAccessToken()
    {
        $method = 'POST';
        $endpoint = $this->getEndpoint();
        $options = [
            'timeout' => 10,
            'connect_timeout' => 10,
            'headers' => [
                'Content-Type' => 'application/json',
            ],
        ];
        $response = $this->httpClient->request($method, $endpoint, $options);

        return data_get(json_decode($response->getBody()->getContents()), 'data.access_token');
    }

    /**
     * @param string $accessToken
     * @return string|null
     * @throws GuzzleException
     */
    private function createGuestPlayUserAndGetHashedId($accessToken)
    {
        $method = 'POST';
        $endpoint = self::GUEST_API_URL;
        $options = [
            'verify' => false,
            'timeout' => 10,
            'connect_timeout' => 10,
            'headers' => [
                'Content-Type' => 'application/json',
                'x-access-token' => $accessToken,
            ],
        ];
        $response = $this->httpClient->request($method, $endpoint, $options);

        return data_get(json_decode($response->getBody()->getContents()), 'body.hashed_id');
    }

    /**
     * ゲストユーザーIDを取得する
     * @param $request Request
     * @return int|null
     */
    public function getGuestUserId($request)
    {
        // Cookieからゲストプレイを取り出す
        $cookie = $request->cookie(self::defaultCookieSettings['key'], null);

        if (empty($cookie)) {
            return null;
        }

        $hashedId = json_decode($cookie, true);

        return $this->guest->getIdByHashedId($hashedId);
    }

    /**
     * 年齢認証をスキップさせる可能DMM exchange 用のリダイレクトURL
     *
     * @param string $url 遷移先
     * @param int $direct 年齢認証をスキップさせる (デフォルト：1 (有効))
     * @return string DMM ExchangeのURLフルパス
     */
    public function getDirectExchangeUrl($url, $direct = 1)
    {
        return sprintf('%s/service/-/exchange/=/?url=%s&direct=%s', env('HTTP_DMM_GENERAL_URL'), urlencode($url), $direct);
    }

    /**
     * guestテーブルからデータ取得
     * @param string|int $userId
     * @return Guest|null
     */
    public function getGuestData($userId)
    {
        return $this->guest->getOneById($userId);
    }

    /**
     * ゲストプレイユーザのキャッシュを削除する
     * @param string $hashedId
     */
    public function deleteGuestPlayUserCache($hashedId)
    {
        // キャッシュキーはgames-key-configリポジトリの`guest_play_user_key`と同じキーを使う
        // https://stash.arms.dmm.com/projects/OLGPF-GAMES/repos/games-key-config/browse/config/cache-key.php
        $key = 'cache_' . 'guest_play_user:hashedId:' . $hashedId;

        if ($this->redisEnable) {
            // キャッシュ削除
            $this->redis->del($key);
        }
    }
}
