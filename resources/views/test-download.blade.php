<!DOCTYPE html>
<html>
<head>
    <title>CSV Download Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; }
        .button:hover { background: #005a87; }
        form { margin: 10px 0; }
        input, select { padding: 5px; margin: 5px; }
    </style>
</head>
<body>
    <h1>CSV Download Test Page</h1>
    <p>This page helps test different CSV download scenarios to reproduce the Chrome issue.</p>

    <div class="test-section">
        <h2>1. Test Actual Point Reports CSV Download</h2>
        <p>Test the real CSV download functionality with different parameters:</p>
        
        <form action="{{ route('PointReportsMonthly.csvdownload') }}" method="POST">
            {{ csrf_field() }}
            <label>Date: <input type="text" name="date" value="{{ date('Y/m/01') }}" placeholder="YYYY/MM/DD"></label><br>
            <label>App ID: <input type="text" name="app_id" value="1" placeholder="Enter app ID"></label><br>
            <button type="submit" class="button">Download CSV (POST)</button>
        </form>
        
        <p>Or test with GET parameters:</p>
        <a href="{{ route('PointReportsMonthly.csvdownload') }}?date={{ date('Y/m/01') }}&app_id=1" class="button">Download CSV (GET)</a>
    </div>

    <div class="test-section">
        <h2>2. Test Different Scenarios</h2>
        <p>Try these different test scenarios:</p>

        <a href="{{ route('PointReportsMonthly.csvdownload') }}?date={{ date('Y/m/01', strtotime('-1 month')) }}&app_id=1" class="button">Last Month Data</a>
        <a href="{{ route('PointReportsMonthly.csvdownload') }}?date={{ date('Y/m/01', strtotime('-2 months')) }}&app_id=1" class="button">2 Months Ago</a>
        <a href="{{ route('PointReportsMonthly.csvdownload') }}?date={{ date('Y/m/01') }}&app_id=999" class="button">Invalid App ID</a>
    </div>

    <div class="test-section">
        <h2>3. Test Downloads by Size (Critical for Chrome Issue)</h2>
        <p><strong>Important:</strong> The production issue occurs at around 6.3MB. Test these in Chrome:</p>

        <a href="/test/download?mode=small" class="button">Small File (~1KB)</a>
        <a href="/test/download?mode=large" class="button">Large File (~1MB)</a>
        <a href="/test/download?mode=6mb" class="button" style="background: #d32f2f;">6.3MB Test (Progressive Size)</a>
        <a href="/test/download?mode=6mb-with-length" class="button" style="background: #b71c1c; color: white;">Content-Length Mismatch</a>
        <a href="/test/download?mode=production-exact" class="button" style="background: #4a148c; color: white;">EXACT Production Issue</a>
        <a href="/test/download?mode=gzip-issue" class="button" style="background: #e65100; color: white;">GZIP Buffer Issue (NEW)</a>
        <a href="/test/download?mode=chunked" class="button">Chunked Streaming</a>
        <a href="/test/download?mode=problematic" class="button">Problematic Buffering</a>
        <a href="/test/download?mode=extreme" class="button">Extreme Test</a>

        <p><strong>Key Differences (Based on Production Analysis):</strong></p>
        <ul>
            <li><strong>6.3MB Test:</strong> Size increases progressively (like your local tests)</li>
            <li><strong>Content-Length Mismatch:</strong> Says 6.0MB but sends 6.3MB (causes confusion)</li>
            <li><strong>EXACT Production Issue:</strong> Says 6.0MB, sends exactly 6.0MB, then stops abruptly</li>
            <li><strong>GZIP Buffer Issue (NEW):</strong> Tests gzip compression failure at 6.3MB (most likely cause)</li>
        </ul>

        <p><strong>Production Analysis Shows:</strong></p>
        <ul>
            <li>✅ <strong>Small files (&lt;6MB):</strong> Get gzipped, have full Laravel headers, download successfully</li>
            <li>❌ <strong>Large files (&gt;6MB):</strong> No gzip, minimal headers, fail at 6.3MB with "Resuming downloading"</li>
        </ul>

        <p><strong>Most Likely Cause:</strong> Nginx gzip buffer limit of 6MB causes compression to fail for large files, leading to the Chrome download issue.</p>
    </div>

    <div class="test-section">
        <h2>4. Instructions</h2>
        <ol>
            <li><strong>Test in Chrome</strong>: Click the download buttons above, especially the 6.3MB test</li>
            <li><strong>Watch for "Resuming downloading"</strong>: Look for this status in Chrome's download bar around 6.3MB</li>
            <li><strong>Monitor download progress</strong>: Check if download gets stuck at exactly 6.3MB</li>
            <li><strong>Compare with Firefox</strong>: Test the same URLs in Firefox to see if they work</li>
            <li><strong>Check logs</strong>: Look at the Laravel logs for PHP settings output</li>
        </ol>

        <h3>Current Configuration (Designed for 6.3MB Issue):</h3>
        <ul>
            <li>Nginx: fastcgi_buffers = 50 * 128k = 6.4MB total buffer</li>
            <li>Nginx: fastcgi_busy_buffers_size = 6400k (6.25MB)</li>
            <li>PHP: Unlimited output buffering + implicit_flush off</li>
            <li>This should cause Chrome to get stuck around 6.3MB</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>4. Debug Information</h2>
        <p>Check the Laravel logs for PHP configuration details:</p>
        <code>docker-compose logs php-fpm | grep "CSV Download PHP Settings"</code>
    </div>
</body>
</html>
