@extends('layout')

@section('content')
<header id="gnavi">
    @include('partials.breadcrumbs', [
        'breadcrumbs' => array_merge(
            $formData['menuName'],
            [
                [$formData['screenName'], 'SbxUsers.index'],
                '編集',
            ]
        )
     ])
    <!-- [#gnavi] -->
</header>
@include('partials.errormessage', ['errors' => $errors])
<section>
    <div class="c-area">
        <h2 class="c-headline"><i class="fa fa-cube fa-pink"></i>{{$formData['screenName']}}：編集</h2>
        {!! Form::open(['route' => 'SbxUsers.editconfirm', 'id' => 'testAccount']) !!}
        {!! Form::hidden('id', $content['id']) !!}
        {!! Form::hidden('gender', $content['gender']) !!}
        {!! Form::hidden('birth_date', $content['birth_date']) !!}
        {!! Form::hidden('blood_type', $content['blood_type']) !!}
        {!! Form::hidden('type', $content['type']) !!}
        {!! Form::hidden('cl_uid', $content['cl_uid']) !!}
        {!! Form::hidden('isEntryClUid', $content['isEntryClUid']) !!}
        {!! Form::hidden('nowPoint', $content['nowPoint']) !!}
        {!! Form::hidden('dmmPremium', $content['dmmPremium']) !!}
        {!! Form::hidden('begin_at', $content['begin_at']) !!}
        {!! Form::hidden('end_at', $content['end_at']) !!}
        <table class="c-table">
            <colgroup>
                <col class="col-m">
                <col class="null">
            </colgroup>
            <thead>
                <tr>
                    <th>項目</th>
                    <th>設定</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <th>id</th>
                    <td>{{ $content['id'] }}</td>
                </tr>
                <tr>
                    <th>パスワード</th>
                    <td>●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●</td>
                </tr>
                <tr {{ error_class('nickname') }}>
                    <th>ニックネーム<span class="tag is-danger">必須</span></th>
                    <td>{!! Form::input('text', 'nickname', $content['nickname'], ['class' => 'placeholder w100']) !!}</td>
                </tr>
                <tr>
                    <th>性別</th>
                    <td>{{ $formData['gender'][$content['gender']] }}</td>
                </tr>
                <tr>
                    <th>生年月日</th>
                    <td>{{ date('Y/m/d', strtotime($content['birth_date'])) }}</td>
                </tr>
                <tr>
                    <th>血液型</th>
                    <td>{{ $formData['blood'][$content['blood_type']] }}</td>
                </tr>
                <tr {{ error_class('dmmPremium') }}>
                    <th>DMMプレミアム会員設定
                        <span class="tag is-danger">必須</span>
                    </th>
                    <td>
                        {!! Form::radio('dmmPremium', '0', ($content['dmmPremium'] == '0') ? true : false, ['id' => 'radio3-1', 'class'  => 'dmmPremium']) !!}
                        <label for="radio3-1">無効</label>
                        {!! Form::radio('dmmPremium', '1', ($content['dmmPremium'] == '1') ? true : false, ['id' => 'radio3-2', 'class'  => 'dmmPremium']) !!}
                        <label for="radio3-2">有効</label>
                    </td>
                </tr>
                <tr premium="premium_period" {{ error_class(['begin_at', 'end_at']) }}>
                    <th>有効期間
                        <span class="tag is-danger">必須</span>
                    </th>
                    <td>
                        {!! Form::input('date', 'begin_at', (empty($content['begin_at'])) ? '' : \Carbon\Carbon::parse($content['begin_at'])->format('Y-m-d'), ['id' => 'begin_at','class' => 'begin_at', 'step' => 1]) !!}
                        ~
                        {!! Form::input('date', 'end_at', (empty($content['end_at'])) ? '' : \Carbon\Carbon::parse($content['end_at'])->format('Y-m-d'), ['id' => 'end_at','class' => 'end_at', 'step' => 1]) !!}
                    </td>
                </tr>
                <tr {{ error_class('type') }}>
                    <th>User Type<span class="tag is-danger">必須</span></th>
                    <td>
                        {!! Form::radio('type', 'null', (empty($content['type']) || $content['type'] == 'null') ? true : false, ['id' => 'radio1-1', 'class'  => 'type']) !!}
                        <label for="radio1-1">{{ $formData['type']['null'] }}</label>
                        @if(auth_is_pf())
                            {!! Form::radio('type', 'staff', true, ['id' => 'radio1-2', 'class'  => 'type']) !!}
                            <label for="radio1-2">{{ $formData['type']['staff'] }}</label>
                        @elseif(auth_is_user_sap())
                            {!! Form::radio('type', 'developer', ($content['type'] == 'developer') ? true : false, ['id' => 'radio1-2', 'class'  => 'type']) !!}
                            <label for="radio1-2">{{ $formData['type']['developer'] }}</label>
                        @endif
                    </td>
                </tr>
                <tr {{ error_class('grade') }}>
                    <th>アカウント種別<span class="tag is-danger">必須</span></th>
                    <td>
                        {!! Form::radio('grade', '1', ($content['grade'] == 1) ? true : false, ['id' => 'radio2-1', 'class'  => 'grade']) !!}
                        <label for="radio2-1">
                            {{ $formData['gradeEdit'][1] }}
                            <span class="c-tooltip">
                                <i class="fa fa-question-circle"></i>
                                <span class="c-tooltip_inner">
                                    <span class="c-tooltip_text gamelist-tooltips_text">
                                        「ゲスト」は、ゲストプレイを使用する場合のみ選択してください。
                                        <!-- /.c-tooltip_text -->
                                    </span>
                                    <!-- /.c-tooltip_inner -->
                                </span>
                                <!-- /.c-tooltip -->
                            </span>
                        </label>
                        {!! Form::radio('grade', '2', (empty($content['grade']) || $content['grade'] >= 2) ? true : false, ['id' => 'radio2-2', 'class'  => 'grade']) !!}
                        <label for="radio2-2">{{ $formData['gradeEdit'][2] }}</label>
                    </td>
                </tr>
                <tr {{ error_class('point') }}>
                    <th>追加ポイント<br>(現在{{ empty($content['nowPoint']) ? 0 : $content['nowPoint'] }}ポイント)</th>
                    <td>
                        @if($content['grade'] == 1)
                            <select name="point"><option value="0" selected="selected">0</option></select>
                        @else
                            {!! Form::select('point', $formData['point'], $content['point'], ['id' => 'point', 'class' => 'point']) !!}
                        @endif
                    </td>
                </tr>
                <tr {{ error_class('cl_uid') }}>
                    <th>
                        クライアントゲームID
                        <span class="c-tooltip">
                            <i class="fa fa-question-circle"></i>
                            <span class="c-tooltip_inner">
                                <span class="c-tooltip_text">
                                    「PC/SPブラウザゲーム」と「PCダウンロードゲーム」のID連携を確認するための項目です。
                                    <br/>
                                    「PCダウンロードゲーム」で作成したテストアカウントのIDを入力してください。
                                    ID連携を確認しない場合は入力不要です。
                                    <!-- /.c-tooltip_text -->
                                </span>
                                <!-- /.c-tooltip_inner -->
                            </span>
                            <!-- /.c-tooltip -->
                        </span>
                    </th>
                    <td>
                    @if(empty($content['isEntryClUid']))
                        {!! Form::input('text', 'cl_uid', $content['cl_uid'], ['class' => 'placeholder w100']) !!}
                    @else
                        {{ $content['cl_uid'] }}
                    @endif
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="center">
            <a href="{{ URL::route('SbxUsers.index', ['search' => 'on']) }}" class="button is-medium"><i class="fa fa-reply"></i> 戻る</a>
            <a href="#" class="button is-warning is-medium" confirm-form="testAccount"><i class="fa fa-check"></i>確認</a>
        </div>
        {!! Form::close() !!}
        <!-- /.c-area -->
    </div>
</section>
@endsection