@extends('layout')

@section('content')

<header id="gnavi">
    @include('partials.breadcrumbs', [
        'breadcrumbs' => array_merge(
            $formData['menuName'],
            [
                [$formData['screenName'], 'SbxUsers.index'],
                '登録',
            ]
        )
     ])
    <!-- [#gnavi] -->
</header>
<section>
    <div class="c-area">
        <h2 class="c-headline"><i class="fa fa-cube fa-pink"></i>{{ $formData['screenName'] }}：登録確認</h2>
        <table class="c-table">
            <colgroup>
                <col class="col-s">
                <col class="null">
            </colgroup>
            <thead>
                <tr>
                    <th>項目</th>
                    <th>設定</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <th>パスワード</th>
                    <td>●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●</td>
                </tr>
                <tr>
                    <th>アカウント種別</th>
                    <td>{{ $formData['grade'][$request->get('grade')] }}</td>
                </tr>
                <tr>
                    <th>ニックネーム</th>
                    <td>{{ $request->get('nickname') }}</td>
                </tr>
                <tr>
                    <th>性別</th>
                    <td>{{ $formData['gender'][$request->get('gender')] }}</td>
                </tr>
                <tr>
                    <th>生年月日</th>
                    <td>{{ $request->get('birth') }}</td>
                </tr>
                <tr>
                    <th>血液型</th>
                    <td>{{ $formData['blood'][$request->get('blood')] }}</td>
                </tr>
                <tr>
                    <th>ポイント</th>
                    <td>{{ $request->get('point') }}</td>
                </tr>
                <tr>
                    <th>クライアントゲームID</th>
                    <td>{{ $request->get('cl_uid') }}</td>
                </tr>
                <tr>
                    <th>作成数</th>
                    <td>{{ $request->get('create_count') }}</td>
                </tr>

            </tbody>
        </table>
        <div class="center">
            <a href="javascript:void(0)" confirm-form="createForm" class="button is-medium submit-button"><i class="fa fa-reply"></i>修正</a>
            <a href="javascript:void(0)" confirm-form="storeForm" class="button is-medium submit-button is-success"><i class="fa fa-circle-o"></i>確定</a>
            {!! Form::open(['route' => ['SbxUsers.create'], 'id' => 'createForm']) !!}
            {!! csrf_field() !!}
            {!! Form::hidden('password', $request->get('password')) !!}
            {!! Form::hidden('password2', $request->get('password2')) !!}
            {!! Form::hidden('grade', $request->get('grade')) !!}
            {!! Form::hidden('nickname', $request->get('nickname')) !!}
            {!! Form::hidden('gender', $request->get('gender')) !!}
            {!! Form::hidden('birth', $request->get('birth')) !!}
            {!! Form::hidden('blood', $request->get('blood')) !!}
            {!! Form::hidden('point', $request->get('point')) !!}
            {!! Form::hidden('cl_uid', $request->get('cl_uid')) !!}
            {!! Form::hidden('create_count', $request->get('create_count')) !!}
            {!! Form::close() !!}

            {!! Form::open(['route' => ['SbxUsers.store'], 'id' => 'storeForm']) !!}
            {!! csrf_field() !!}
            {!! Form::hidden('password', $request->get('password')) !!}
            {!! Form::hidden('password2', $request->get('password2')) !!}
            {!! Form::hidden('grade', $request->get('grade')) !!}
            {!! Form::hidden('nickname', $request->get('nickname')) !!}
            {!! Form::hidden('gender', $request->get('gender')) !!}
            {!! Form::hidden('birth', $request->get('birth')) !!}
            {!! Form::hidden('blood', $request->get('blood')) !!}
            {!! Form::hidden('point', $request->get('point')) !!}
            {!! Form::hidden('cl_uid', $request->get('cl_uid')) !!}
            {!! Form::hidden('create_count', $request->get('create_count')) !!}
            {!! Form::close() !!}
        </div>
    </div>
</section>

@endsection
