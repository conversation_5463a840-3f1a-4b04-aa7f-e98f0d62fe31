@extends('layout')

@section('content')

<header id="gnavi">
    @include('partials.breadcrumbs', [
        'breadcrumbs' => array_merge(
            $formData['menuName'],
            [
                [$formData['screenName'], 'SbxUsers.index'],
                '編集',
            ]
        )
     ])
    <!-- [#gnavi] -->
</header>
<section>
    <div class="c-area">
        <h2 class="c-headline"><i class="fa fa-cube fa-pink"></i>{{ $formData['screenName'] }}：編集確認</h2>
        <table class="c-table">
            <colgroup>
                <col class="col-s">
                <col class="null">
            </colgroup>
            <thead>
                <tr>
                    <th>項目</th>
                    <th>設定</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <th>id</th>
                    <td>{{ $request->get('id') }}</td>
                </tr>
                <tr>
                    <th>パスワード</th>
                    <td>●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●</td>
                </tr>
                <tr>
                    <th>ニックネーム</th>
                    <td>{{ $request->get('nickname') }}</td>
                </tr>
                <tr>
                    <th>性別</th>
                    <td>{{ $formData['gender'][$request->get('gender')] }}</td>
                </tr>
                <tr>
                    <th>生年月日</th>
                    <td>{{ date('Y/m/d', strtotime($request->get('birth_date'))) }}</td>
                </tr>
                <tr>
                    <th>血液型</th>
                    <td>{{ $formData['blood'][$request->get('blood_type')] }}</td>
                </tr>
                <tr>
                    <th>User Type</th>
                    <td>{{ $formData['type'][$request->get('type')] }}</td>
                </tr>
                <tr>
                    <th>アカウント種別</th>
                    <td>{{ $formData['gradeEdit'][$request->get('grade')] }}</td>
                </tr>
                <tr>
                    <th>追加ポイント</th>
                    <td>{{ $request->get('point') }}</td>
                </tr>
                <tr>
                    <th>クライアントゲームID</th>
                    <td>{{ $request->get('cl_uid') }}</td>
                </tr>
            </tbody>
        </table>
        <div class="center">
            <a href="javascript:void(0)" confirm-form="editForm" class="button is-medium submit-button"><i class="fa fa-reply"></i>修正</a>
            <a href="javascript:void(0)" confirm-form="updateForm" class="button is-medium submit-button is-success"><i class="fa fa-circle-o"></i>確定</a>
            {!! Form::open(['route' => ['SbxUsers.edit'], 'id' => 'editForm']) !!}
            {!! csrf_field() !!}
            {!! Form::hidden('id', $request->get('id')) !!}
            {!! Form::hidden('nickname', $request->get('nickname')) !!}
            {!! Form::hidden('gender', $request->get('gender')) !!}
            {!! Form::hidden('birth_date', $request->get('birth_date')) !!}
            {!! Form::hidden('blood_type', $request->get('blood_type')) !!}
            {!! Form::hidden('type', $request->get('type')) !!}
            {!! Form::hidden('grade', $request->get('grade')) !!}
            {!! Form::hidden('point', $request->get('point')) !!}
            {!! Form::hidden('cl_uid', $request->get('cl_uid')) !!}
            {!! Form::hidden('isEntryClUid', $request->get('isEntryClUid')) !!}
            {!! Form::hidden('nowPoint', $request->get('nowPoint')) !!}
            {!! Form::close() !!}

            {!! Form::open(['route' => ['SbxUsers.update'], 'id' => 'updateForm']) !!}
            {!! csrf_field() !!}
            {!! Form::hidden('id', $request->get('id')) !!}
            {!! Form::hidden('nickname', $request->get('nickname')) !!}
            {!! Form::hidden('gender', $request->get('gender')) !!}
            {!! Form::hidden('birth_date', $request->get('birth_date')) !!}
            {!! Form::hidden('blood_type', $request->get('blood_type')) !!}
            {!! Form::hidden('type', $request->get('type')) !!}
            {!! Form::hidden('grade', $request->get('grade')) !!}
            {!! Form::hidden('point', $request->get('point')) !!}
            {!! Form::hidden('cl_uid', $request->get('cl_uid')) !!}
            {!! Form::hidden('isEntryClUid', $request->get('isEntryClUid')) !!}
            {!! Form::hidden('nowPoint', $request->get('nowPoint')) !!}
            {!! Form::close() !!}
        </div>
    </div>
</section>

@endsection
