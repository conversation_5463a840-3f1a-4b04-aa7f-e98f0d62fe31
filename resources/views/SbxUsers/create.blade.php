@extends('layout')

@section('content')
<header id="gnavi">
    @include('partials.breadcrumbs', [
        'breadcrumbs' => array_merge(
            $formData['menuName'],
            [
                [$formData['screenName'], 'SbxUsers.index'],
                '登録',
            ]
        )
     ])
    <!-- [#gnavi] -->
</header>
@include('partials.errormessage', ['errors' => $errors])
<section>
    <div class="c-area">
        <h2 class="c-headline"><i class="fa fa-cube fa-pink"></i>{{$formData['screenName']}}：登録</h2>
        {!! Form::open(['route' => 'SbxUsers.createconfirm', 'id' => 'testAccount']) !!}
        <table class="c-table">
            <colgroup>
                <col class="col-m">
                <col class="null">
            </colgroup>
            <thead>
                <tr>
                    <th>項目</th>
                    <th>設定</th>
                </tr>
            </thead>
            <tbody>
                <tr {{ error_class('password') }}>
                    <th>パスワード<span class="tag is-danger">必須</span></th>
                    <td>{!! Form::input('password', 'password', $request->get('password'), ['class' => 'placeholder w100']) !!}</td>
                </tr>
                <tr {{ error_class('password2') }}>
                    <th>
                        パスワード確認<span class="tag is-danger">必須</span><br />
                    </th>
                    <td>
                        {!! Form::input('password', 'password2', $request->get('password2'), ['class' => 'placeholder w100']) !!}
                    </td>
                </tr>
                <tr {{ error_class('grade') }}>
                    <th>アカウント種別<span class="tag is-danger">必須</span></th>
                    <td>
                        {!! Form::radio('grade', '2', (empty($request->get('grade')) || $request->get('grade') == 2) ? true : false, ['id' => 'radio1-1', 'class'  => 'grade']) !!}
                        <label for="radio1-1">{{$formData['grade'][2]}}</label>
                        {!! Form::radio('grade', '1', ($request->get('grade') == 1) ? true : false, ['id' => 'radio1-2', 'class'  => 'grade']) !!}
                        <label for="radio1-2">
                            {{$formData['grade'][1]}}
                            <span class="c-tooltip">
                                <i class="fa fa-question-circle"></i>
                                <span class="c-tooltip_inner">
                                    <span class="c-tooltip_text gamelist-tooltips_text">
                                        「ゲスト」は、ゲストプレイを使用する場合のみ選択してください。
                                        <!-- /.c-tooltip_text -->
                                    </span>
                                    <!-- /.c-tooltip_inner -->
                                </span>
                                <!-- /.c-tooltip -->
                            </span>
                        </label>
                    </td>
                </tr>
                <tr {{ error_class('nickname') }}>
                    <th>ニックネーム<span class="tag is-danger">必須</span></th>
                    <td>{!! Form::input('text', 'nickname', $request->get('nickname'), ['class' => 'placeholder w100']) !!}</td>
                </tr>
                <tr {{ error_class('gender') }}>
                    <th>性別<span class="tag is-danger">必須</span></th>
                    <td>
                        {!! Form::radio('gender', 'male', (empty($request->get('gender')) || $request->get('gender') == 'male') ? true : false, ['id' => 'radio2-1', 'class'  => 'gender']) !!}
                        <label for="radio2-1">{{$formData['gender']['male']}}</label>
                        {!! Form::radio('gender', 'female', ($request->get('gender') == 'female') ? true : false, ['id' => 'radio2-2', 'class'  => 'gender']) !!}
                        <label for="radio2-2">{{$formData['gender']['female']}}</label>
                    </td>
                </tr>
                <tr {{ error_class('birth') }}>
                    <th>生年月日<span class="tag is-danger">必須</span></th>
                    <td>{!! Form::input('text', 'birth', empty($request->get('birth')) ? $formData['birthDefault'] : $request->get('birth'), ['id' => 'birth', 'class' => 'placeholder w100', 'autocomplete' => 'off']) !!}</td>
                </tr>
                <tr {{ error_class('blood') }}>
                    <th>血液型<span class="tag is-danger">必須</span></th>
                    <td>
                     {!! Form::select('blood', $formData['blood'], $request->get('blood'), ['id' => 'blood','class' => 'blood']) !!}
                    </td>
                </tr>
                <tr {{ error_class('point') }}>
                    <th>ポイント</th>
                    <td>
                     {!! Form::select('point', $formData['point'], $request->get('point'), ['id' => 'point', 'class' => 'point']) !!}
                    </td>
                </tr>
                <tr {{ error_class('cl_uid') }}>
                    <th>
                        クライアントゲームID
                        <span class="c-tooltip">
                            <i class="fa fa-question-circle"></i>
                            <span class="c-tooltip_inner">
                                <span class="c-tooltip_text">
                                    「PC/SPブラウザゲーム」と「PCダウンロードゲーム」のID連携を確認するための項目です。
                                    <br/>
                                    「PCダウンロードゲーム」で作成したテストアカウントのIDを入力してください。
                                    ID連携を確認しない場合は入力不要です。
                                    <!-- /.c-tooltip_text -->
                                </span>
                                <!-- /.c-tooltip_inner -->
                            </span>
                            <!-- /.c-tooltip -->
                        </span>
                    </th>
                    <td>{!! Form::input('text', 'cl_uid', $request->get('cl_uid'), ['class' => 'placeholder w100']) !!}</td>
                </tr>
                <tr {{ error_class('create_count') }}>
                    <th>
                        作成数
                        <span class="c-tooltip">
                            <i class="fa fa-question-circle"></i>
                            <span class="c-tooltip_inner">
                                <span class="c-tooltip_text">
                                    2以上を指定した場合、ニックネームの末尾に連番が追加されます。
                                    <!-- /.c-tooltip_text -->
                                </span>
                                <!-- /.c-tooltip_inner -->
                            </span>
                            <!-- /.c-tooltip -->
                        </span>
                    </th>
                    <td>
                        {{--*/
                            $createCountList = [];
                            $createCountMax = config('forms.SbxUsers.createCountMax');
                            for ($createCount=1; $createCount<=$createCountMax; $createCount++) {
                                $createCountList[$createCount] = $createCount;
                            }
                        /*--}}

                        {!! Form::select('create_count', $createCountList, $request->get('create_count'), ['id' => 'create_count']) !!}
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="center">
            <a href="{{ URL::route('SbxUsers.index', ['search' => 'on']) }}" class="button is-medium"><i class="fa fa-reply"></i> 戻る</a>
            <a href="#" class="button is-warning is-medium" confirm-form="testAccount"><i class="fa fa-check"></i>確認</a>
        </div>
        {!! Form::close() !!}
        <!-- /.c-area -->
    </div>
</section>
@endsection