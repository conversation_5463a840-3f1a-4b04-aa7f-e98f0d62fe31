@extends('layout')

@section('content')

<header id="gnavi">
    @include('partials.breadcrumbs', [
        'breadcrumbs' => array_merge(
            $formData['menuName'],
            [
                [$formData['screenName'], 'SbxUsers.index'],
                '登録',
            ]
        )
     ])
    <!-- [#gnavi] -->
</header>

<section>
    <div class="c-area">
        <h2 class="c-headline"><i class="fa fa-cube fa-pink"></i>{{$formData['screenName']}}:登録完了</h2>
        <div class="center mg-b20">
            <p class="bold tx-20 mg-b6">{{$formData['screenName']}}：登録が完了しました</p>
            ※反映に時間がかかることがありますので、反映されていない場合は少々お待ちください。
        </div>

        <div class="center mg-b10">
            <table id="user-list" class="c-table info-table" style="display:inline-table;width:800px;">
                <colgroup>
                    <col class="col-m">
                    <col class="null">
                </colgroup>
                <tbody>
                <tr>
                    <th>ID</th>
                    <th>ニックネーム</th>
                </tr>
                @foreach ($users as $userId => $nickname)
                    <tr>
                        <td class="user-id">{{ $userId }}</td>
                        <td class="nickname">{{ $nickname }}</td>
                    </tr>
                @endforeach
                </tbody>
                <!-- /.c-table -->
            </table>
        </div>

        <div class="center mg-b20">
            <a id="copy-button" class="button">
                クリップボードにコピー
            </a>
        </div>

        <div class="center">
            <a href="{{ URL::route('SbxUsers.index', ['search' => 'on']) }}" class="button">
                <i class="fa fa-reply"></i>{{$formData['screenName']}}の一覧に戻る
            </a>
        </div>
        <!-- /.class -->
    </div>
</section>
@endsection