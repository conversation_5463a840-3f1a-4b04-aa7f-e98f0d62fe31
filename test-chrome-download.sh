#!/bin/bash

echo "=== Chrome Download Issue Reproduction Script ==="
echo "This script helps you test different configurations to reproduce the Chrome download issue"
echo ""

# Function to restart containers
restart_containers() {
    echo "Restarting containers..."
    docker-compose down
    docker-compose up -d
    echo "Waiting for containers to start..."
    sleep 10
}

# Function to test configuration
test_config() {
    local config_name="$1"
    echo ""
    echo "=== Testing Configuration: $config_name ==="
    echo "1. Open Chrome and go to: http://localhost:8081"
    echo "2. Navigate to the CSV download page"
    echo "3. Try downloading the CSV file"
    echo "4. Check if Chrome shows 'Resuming downloading' and gets stuck"
    echo ""
    echo "Press Enter when you've tested this configuration..."
    read
}

echo "Current configuration has these settings that might cause Chrome issues:"
echo "- nginx: fastcgi_buffering = on, large buffers, gzip = on"
echo "- PHP: output_buffering = On (unlimited), implicit_flush = off"
echo ""

restart_containers
test_config "Aggressive Buffering (Current)"

echo ""
echo "=== Trying Alternative Configuration 1: Minimal Buffering ==="

# Switch to minimal buffering
sed -i.bak 's/fastcgi_buffering on;/fastcgi_buffering off;/' files/nginx/default.conf
sed -i 's/fastcgi_buffer_size 64k;/fastcgi_buffer_size 1k;/' files/nginx/default.conf
sed -i 's/fastcgi_buffers 16 64k;/fastcgi_buffers 2 1k;/' files/nginx/default.conf
sed -i 's/gzip on;/gzip off;/' files/nginx/default.conf

restart_containers
test_config "Minimal Buffering"

echo ""
echo "=== Trying Alternative Configuration 2: Mixed Settings ==="

# Switch to mixed settings
sed -i 's/fastcgi_buffering off;/fastcgi_buffering on;/' files/nginx/default.conf
sed -i 's/output_buffering = On/output_buffering = 4096/' files/php-fpm/php.ini
sed -i 's/php_admin_flag\[implicit_flush\] = off/php_admin_flag[implicit_flush] = on/' files/php-fpm/www.conf

restart_containers
test_config "Mixed Settings"

echo ""
echo "=== Restoring Original Configuration ==="
mv files/nginx/default.conf.bak files/nginx/default.conf 2>/dev/null || true
sed -i 's/output_buffering = 4096/output_buffering = On/' files/php-fpm/php.ini
sed -i 's/php_admin_flag\[implicit_flush\] = on/php_admin_flag[implicit_flush] = off/' files/php-fpm/www.conf

restart_containers

echo ""
echo "=== Testing Complete ==="
echo "If you found a configuration that reproduces the Chrome issue, you can:"
echo "1. Note which settings caused the problem"
echo "2. Compare with your production environment"
echo "3. Implement a fix by adjusting the problematic settings"
