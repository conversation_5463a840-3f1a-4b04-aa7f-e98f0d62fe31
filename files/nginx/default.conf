server {
    listen 80;
    server_name localhost;
    root /var/www;
    index index.php index.html server.php;

    location / {
        try_files $uri $uri/ /server.php?$query_string;
    }

    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass php-fpm:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;

        # Configuration to reproduce Chrome download issues
        # Settings designed to cause problems around 6.3MB

        # Buffer settings that might cause 6.3MB limit
        fastcgi_buffering on;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 50 128k;  # 50 * 128k = 6.4MB total buffer
        fastcgi_busy_buffers_size 6451k;  # Slightly above 6.3MB
        fastcgi_max_temp_file_size 0;  # Disable temp files to force memory buffering
        fastcgi_temp_file_write_size 128k;

        # Additional settings that might contribute to 6.3MB issue
        proxy_buffering on;
        proxy_buffer_size 64k;
        proxy_buffers 100 64k;  # 100 * 64k = 6.4MB
        proxy_busy_buffers_size 6400k;

        # Gzip settings that might interfere at specific sizes
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_comp_level 6;
        gzip_types text/csv application/octet-stream text/plain;
        gzip_proxied any;

        # Timeouts that might cause issues during large transfers
        fastcgi_read_timeout 300s;
        fastcgi_send_timeout 300s;
    }

    error_log /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;

    location = /favicon.ico {
        access_log off;
        log_not_found off;
    }

    location ~* .(jpg|jpeg|png|gif|ico|css|js|svg|ttf|woff|woff2|eot)$ {
        root   /var/www/public/;
        expires max;
        add_header Cache-Control public;
        access_log off;
    }

}
