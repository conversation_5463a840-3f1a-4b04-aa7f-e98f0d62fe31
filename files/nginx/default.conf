server {
    listen 80;
    server_name localhost;

    # Match production settings
    keepalive_timeout 0;
    server_tokens off;

    #--------------------
    # GZIP settings
    #--------------------
    gzip              on;
    gzip_disable      "msie6"
    gzip_vary         on;
    gzip_proxied      any;
    gzip_comp_level   5;
    gzip_buffers      16 8k;
    gzip_http_version 1.1;
    gzip_types        text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript;

    root /var/www;
    index index.php index.html server.php;

    client_max_body_size 3072M;

    # Add 6MB limit to reproduce production issue
    fastcgi_max_temp_file_size 6m;

    location / {
        try_files $uri $uri/ /server.php?$query_string;
    }

    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass php-fpm:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;


        fastcgi_intercept_errors        on;
        fastcgi_ignore_client_abort     off;
        fastcgi_connect_timeout 60;
        fastcgi_send_timeout 180;
        fastcgi_read_timeout 1200;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
        fastcgi_temp_file_write_size 256k;

        include       fastcgi_params;
    }

    error_log /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;

    location = /favicon.ico {
        access_log off;
        log_not_found off;
    }

    location ~* .(jpg|jpeg|png|gif|ico|css|js|svg|ttf|woff|woff2|eot)$ {
        root   /var/www/public/;
        expires max;
        add_header Cache-Control public;
        access_log off;
    }

}
