server {
    listen 80;
    server_name localhost;
    root /var/www;
    index index.php index.html server.php;

    location / {
        try_files $uri $uri/ /server.php?$query_string;
    }

    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass php-fpm:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;

        # Fix for "upstream sent too big header" error
        fastcgi_buffer_size 32k;
        fastcgi_buffers 8 32k;
        fastcgi_busy_buffers_size 64k;

        # Add these directives to reproduce production streaming behavior
        fastcgi_buffering off;
        fastcgi_max_temp_file_size 0;
        proxy_buffering off;
        gzip off;
    }

    error_log /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;

    location = /favicon.ico {
        access_log off;
        log_not_found off;
    }

    location ~* .(jpg|jpeg|png|gif|ico|css|js|svg|ttf|woff|woff2|eot)$ {
        root   /var/www/public/;
        expires max;
        add_header Cache-Control public;
        access_log off;
    }

}
