server {
    listen 80;
    server_name localhost;
    root /var/www;
    index index.php index.html server.php;

    location / {
        try_files $uri $uri/ /server.php?$query_string;
    }

    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass php-fpm:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;

        # Configuration to reproduce Chrome download issues
        # Try different combinations of these settings:

        # Option 1: Aggressive buffering (like production might have)
        fastcgi_buffering on;
        fastcgi_buffer_size 64k;
        fastcgi_buffers 16 64k;
        fastcgi_busy_buffers_size 128k;
        fastcgi_max_temp_file_size 1024m;

        # Option 2: Uncomment these for minimal buffering
        # fastcgi_buffering off;
        # fastcgi_buffer_size 1k;
        # fastcgi_buffers 2 1k;

        proxy_buffering off;
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_types text/csv application/octet-stream;
    }

    error_log /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;

    location = /favicon.ico {
        access_log off;
        log_not_found off;
    }

    location ~* .(jpg|jpeg|png|gif|ico|css|js|svg|ttf|woff|woff2|eot)$ {
        root   /var/www/public/;
        expires max;
        add_header Cache-Control public;
        access_log off;
    }

}
