server {
    listen 80;
    server_name localhost;
    root /var/www;
    index index.php index.html server.php;

    location / {
        try_files $uri $uri/ /server.php?$query_string;
    }

    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass php-fpm:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;

        # Configuration to reproduce Chrome download issues
        # EXTREME settings to force Chrome problems

        # Very aggressive buffering
        fastcgi_buffering on;
        fastcgi_buffer_size 256k;
        fastcgi_buffers 64 256k;
        fastcgi_busy_buffers_size 512k;
        fastcgi_max_temp_file_size 2048m;
        fastcgi_temp_file_write_size 256k;

        # Force buffering conflicts
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 32 128k;

        # Aggressive gzip that might interfere with streaming
        gzip on;
        gzip_vary on;
        gzip_min_length 1;
        gzip_comp_level 9;
        gzip_types text/csv application/octet-stream text/plain;
        gzip_proxied any;
    }

    error_log /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;

    location = /favicon.ico {
        access_log off;
        log_not_found off;
    }

    location ~* .(jpg|jpeg|png|gif|ico|css|js|svg|ttf|woff|woff2|eot)$ {
        root   /var/www/public/;
        expires max;
        add_header Cache-Control public;
        access_log off;
    }

}
