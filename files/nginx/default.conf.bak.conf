server {
    listen 80;
    server_name localhost;
    root /var/www;
    index index.php index.html server.php;

    # --- GZ<PERSON> SETTINGS TO REPRODUCE THE ISSUE ---
    # These settings are designed to cause <PERSON>in<PERSON> to buffer and gzip responses
    # in a way that can trigger the "Resuming downloading" issue in Chrome
    # when the content size exceeds the gzip_buffers limit.
    gzip on;
    gzip_vary on;
    gzip_proxied any; # Important for gzipping responses from PHP-FPM
    gzip_comp_level 6;
    gzip_min_length 1000;
    # CRITICAL: Gzip the correct content types.
    # Including text/csv and text/html as your production headers showed both.
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript text/csv text/html;
    # CRITICAL: Set the gzip buffer to a value that will be exceeded around 6MB.
    # 48 buffers of 128k each = 6MB. When the uncompressed response from PHP
    # exceeds this, Nginx's gzip module may stop compressing, which confuses Chrome.
    gzip_buffers 48 128k;

    location / {
        try_files $uri $uri/ /server.php?$query_string;
    }

    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass php-fpm:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;

        # --- FASTCGI BUFFERING SETTINGS TO REPRODUCE THE ISSUE ---
        # These settings control how Nginx buffers responses from PHP-FPM.
        # Combined with gzip settings, they can create the problematic behavior.
        fastcgi_buffering on;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 50 128k; # 50 * 128k = 6.4MB
        # CRITICAL: Force Nginx to hold a large amount of data in memory before sending.
        # This value (6.25MB) is close to the total buffer size (6.4MB) and should
        # force the gzip buffer (6MB) to be exceeded, triggering the bug.
        fastcgi_busy_buffers_size 6400k;
        fastcgi_pass_header "X-Accel-Buffering"; # Allows PHP to control Nginx buffering
    }

    error_log /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;

    location = /favicon.ico {
        access_log off;
        log_not_found off;
    }

    location ~* .(jpg|jpeg|png|gif|ico|css|js|svg|ttf|woff|woff2|eot)$ {
        root   /var/www/public/;
        expires max;
        add_header Cache-Control public;
        access_log off;
    }

}
