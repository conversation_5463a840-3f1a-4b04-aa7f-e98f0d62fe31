; このファイルはConfigMapで上書きされます
[www]
user = www-data
group = www-data
listen = 0.0.0.0:9000

pm = dynamic
pm.max_children = 100
pm.start_servers = 10
pm.min_spare_servers = 10
pm.max_spare_servers = 50
pm.max_requests = 512

;slowlog = /var/log/php5.6-fpm/www-slow.log
php_admin_value[display_errors] = 'stderr'
php_admin_value[error_reporting] = 'E_ALL'
php_admin_value[error_log] = /dev/stderr
catch_workers_output = yes
php_admin_flag[log_errors] = on

; Add these to reproduce production streaming issues
php_admin_value[output_buffering] = 8192
php_admin_flag[implicit_flush] = on
php_value[session.save_handler] = files
php_value[session.save_path]    = /var/lib/php/session

;monitoring
pm.status_path = /phpfpm_status
ping.path = /phpfpm_ping
ping.response = pong
