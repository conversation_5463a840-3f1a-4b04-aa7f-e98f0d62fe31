<?php

return [
    'screenName' => 'テストアカウント',
    'menuName' => ['サンドボックス'],
    'offset' => 10,
    'pagerLinkNum' => 5,
    'deleteConfirmMessage' => 'サンドボックス－テストアカウントを削除します。よろしいですか？',
    'afterDeleteMessage' => 'サンドボックス－テストアカウントを削除しました。',
    'grade' => [2 => '会員', 1 => 'ゲスト'],
    'gradeEdit' => [1 => 'ゲスト', 2 => '会員'],
    'gender' => ['male' => '男性', 'female' => '女性'],
    'blood' => ['A' => 'Ａ', 'B' => 'Ｂ', 'O' => 'Ｏ', 'AB' => 'ＡＢ'],
    'point' => [0 => 0, 500 => 500, 1000 => 1000, 2000 => 2000, 5000 => 5000, 10000 => 10000, 100000 => 100000, 1000000 => 1000000, 10000000 => 10000000],
    'type' => ['null' => 'Null', 'staff' => 'Staff', 'developer' => 'Developer'],
    'birthDefault' => '1993/01/01',
    'createCountMax' => 20,

//*********************************************************************************************************************
    'namesUserSortType'  => [
            'date_desc'  => [
            'label'      => '登録日が新しい順',
            'order'      => 'u.regist_date desc',
    ],
            'date_asc'   => [
            'label'      => '登録日が古い順',
            'order'      => 'u.regist_date asc',
    ],
            'nicname_asc'=> [
            'label'      => 'ニックネーム五十音順',
            'order'      => 'cast(u.nickname as char) asc',
    ],
            'point_desc'   => [
            'label'      => '所持ポイントが多い順',
            'order'      => 'sp.point desc',
    ],
            'point_asc'   => [
            'label'      => '所持ポイントが少ない順',
            'order'      => 'sp.point asc',
    ],
    ],

];
