$(function() {
    // pager view count
    $('select[name="perPage"] option').filter(function() {
        return $(this).val() == $('select[name="perPage"]').data('value');
    }).prop('selected', true);
    $('select[name="perPage"]').change(function() {
        location.href = $(this).children('option[selected]').data('href');
    });

    $('select[name="userSort"] option').filter(function() {
        return $(this).val() == $('select[name="userSort"]').data('value');
    }).prop('selected', true);
    $('select[name="userSort"]').change(function() {
        location.href = $(this).children('option[selected]').data('href');
    });

    // 検索フォーム 送信
    $('#search-button').on('click', function() {
        $('#search-form').submit();
        return false;
    });
    // 検索フォーム リセット
    $('#reset-search-button').on('click', function() {
        $('#search-form [name="id"]' ).val('');
        $('#search-form [name="nickname"]').val('');
        return false;
    });
    // コピーボタン押下
    $('#copy-button').on('click', function() {
        // navigator.clipboardが使えるない場合
        if (!navigator.clipboard) {
            // テーブルを選択しコピー
            $("#user-list").select()
            document.execCommand('copy')
        } 

        // navigator.clipboardに書き込む
        let text = "";

        $("#user-list tr").each(function() {
            let userId = $(this).find('.user-id').html();
            let nickname = $(this).find('.nickname').html();

            if (userId && nickname) {
                text = text + userId + "\t" + nickname + "\n";
            }
        });

        navigator.clipboard.writeText(text);

        return false;
    });

    // ゲストプレイ(grade=1)の場合一部の項目を固定値とする
    // 切り替え時
    $('input[name="grade"]').change(function() {
        if ($(this).val() == 1) {
            setGuestInput();
        } else {
            setMemberInput();
        }
    });

    // ロード時
    if ($('input[name="grade"]:checked').val() == 1) {
        setGuestInput();
    } else {
        setMemberInput();
    }

    // ゲストプレイ選択
    function setGuestInput(){
        $('input[name="birth"]').datetimepicker("destroy");
        $('input[name="birth"]').attr("readonly","readonly");
        $('input[name="birth"]').val('1970/01/01')
        $('input[name="gender"]').attr("disabled","disabled");
        $('input[name="gender"][value="male"]').removeAttr("disabled").attr("checked","checked");
        $('select[name="blood"] > option').attr("disabled","disabled");
        $('select[name="blood"] > option[value="A"]').removeAttr("disabled").attr("selected","selected");
        $('select[name="point"] > option').attr("disabled","disabled");
        $('select[name="point"] > option[value="0"]').removeAttr("disabled").attr("selected","selected");
    }

    // ゲストプレイ以外の会員選択
    function setMemberInput(){
        $('input[name="birth"]').datetimepicker({
            format: 'Y/m/d', timepicker: false,
        });
        $('input[name="birth"]').removeAttr("readonly");
        $('input[name="gender"]').removeAttr("disabled");
        $('select[name="blood"] > option').removeAttr("disabled");
        $('select[name="point"] > option').removeAttr("disabled");
    }

    // 削除終了後
    if ($('div.message').length) {
        alert($('div.message').text());
    }
});

// 確認ボタン
$(document).on('click', 'a[confirm-form]', function() {
    if ($(this).data('confirm') && !confirm($(this).data('confirm'))) {
        return false;
    }
    $('#' + $(this).attr('confirm-form')).submit();
    return false;
});