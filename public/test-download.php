<?php
/**
 * Simple test script to reproduce Chrome download issues
 * Access via: http://localhost:8081/test-download.php
 */

// Log current PHP settings
error_log("=== Test Download Debug Info ===");
error_log("output_buffering: " . ini_get('output_buffering'));
error_log("implicit_flush: " . ini_get('implicit_flush'));
error_log("ob_get_level: " . ob_get_level());
error_log("memory_limit: " . ini_get('memory_limit'));

// Disable any existing output buffering
while (ob_get_level() > 0) {
    ob_end_clean();
}

// Set headers for CSV download
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename=test-download.csv');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Test different streaming scenarios
$test_mode = $_GET['mode'] ?? 'default';

switch ($test_mode) {
    case 'small':
        // Small file - should work fine
        echo "header1,header2,header3\n";
        echo "value1,value2,value3\n";
        break;
        
    case 'large':
        // Large file with streaming
        echo "id,data,timestamp\n";
        for ($i = 1; $i <= 10000; $i++) {
            echo "$i,\"Large data chunk with lots of text to make it bigger\",\"" . date('Y-m-d H:i:s') . "\"\n";
            if ($i % 100 == 0) {
                flush();
                usleep(10000); // 10ms delay
            }
        }
        break;
        
    case 'chunked':
        // Chunked streaming with explicit flushes
        echo "chunk,size,data\n";
        for ($i = 1; $i <= 50; $i++) {
            $data = str_repeat("x", 1000); // 1KB of data
            echo "$i,1000,\"$data\"\n";
            ob_flush();
            flush();
            sleep(1); // 1 second delay between chunks
        }
        break;
        
    case 'problematic':
        // This mode tries to reproduce the Chrome issue
        // by creating buffering conflicts
        
        // First, output some data
        echo "header1,header2,header3\n";
        
        // Force a flush
        ob_flush();
        flush();
        
        // Wait a bit
        sleep(2);
        
        // Output more data in chunks that might cause buffering issues
        for ($i = 1; $i <= 100; $i++) {
            // Create chunks that are exactly at buffer boundaries
            $chunk_size = 4096; // Match common buffer sizes
            $data = str_repeat("a", $chunk_size - 20); // Leave room for CSV formatting
            echo "$i,\"$data\"\n";
            
            // Only flush every few iterations to create buffering conflicts
            if ($i % 5 == 0) {
                ob_flush();
                flush();
                usleep(100000); // 100ms delay
            }
        }
        break;
        
    default:
        // Default test
        echo "test_type,description,status\n";
        echo "default,\"Basic CSV download test\",\"working\"\n";
        echo "small,\"Small file test\",\"should work\"\n";
        echo "large,\"Large file with streaming\",\"may have issues\"\n";
        echo "chunked,\"Chunked streaming test\",\"may have issues\"\n";
        echo "problematic,\"Buffering conflict test\",\"likely to cause Chrome issues\"\n";
        break;
}

// Log completion
error_log("Test download completed for mode: $test_mode");
?>
